-- =====================================================================
-- AROUZ MARKET - PERFECT RLS SOLUTION
-- =====================================================================
-- This SQL script creates PERFECT, NON-CONFLICTING RLS policies
-- that support ALL user types without breaking anything
-- 
-- CRITICAL: This fixes the production authentication issues
-- =====================================================================

-- STEP 1: COMPLETE CLEANUP - Remove ALL existing problematic policies
-- =====================================================================

-- Drop ALL existing profiles policies (they're causing conflicts)
DROP POLICY IF EXISTS "Users can view their own profile" ON profiles;
DROP POLICY IF EXISTS "Users can update their own profile" ON profiles;
DROP POLICY IF EXISTS "service_role_all_access" ON profiles;
DROP POLICY IF EXISTS "users_select_own_profile" ON profiles;
DROP POLICY IF EXISTS "users_update_own_profile" ON profiles;
DROP POLICY IF EXISTS "profiles_anonymous_login_check" ON profiles;
DROP POLICY IF EXISTS "profiles_secure_login_check" ON profiles;
DROP POLICY IF EXISTS "profiles_consumer_anonymous_access" ON profiles;
DROP POLICY IF EXISTS "profiles_consumer_signup" ON profiles;
DROP POLICY IF EXISTS "profiles_consumer_login_check" ON profiles;
DROP POLICY IF EXISTS "profiles_consumer_update" ON profiles;
DROP POLICY IF EXISTS "profiles_service_role_access" ON profiles;
DROP POLICY IF EXISTS "profiles_admin_own_access" ON profiles;
DROP POLICY IF EXISTS "profiles_admin_own_update" ON profiles;
DROP POLICY IF EXISTS "profiles_consumer_login_verification" ON profiles;

-- Drop problematic orders policies
DROP POLICY IF EXISTS "orders_consumer_access" ON orders;
DROP POLICY IF EXISTS "orders_allow_insert" ON orders;
DROP POLICY IF EXISTS "orders_service_role_access" ON orders;
DROP POLICY IF EXISTS "orders_admin_access" ON orders;
DROP POLICY IF EXISTS "orders_creation_access" ON orders;
DROP POLICY IF EXISTS "orders_update_access" ON orders;

-- Drop problematic shipments policies
DROP POLICY IF EXISTS "shipments_consumer_access" ON shipments;
DROP POLICY IF EXISTS "shipments_supplier_access" ON shipments;
DROP POLICY IF EXISTS "shipments_insert_access" ON shipments;
DROP POLICY IF EXISTS "shipments_update_access" ON shipments;
DROP POLICY IF EXISTS "shipments_service_role_access" ON shipments;
DROP POLICY IF EXISTS "shipments_admin_access" ON shipments;
DROP POLICY IF EXISTS "shipments_shipping_company_access" ON shipments;

-- STEP 2: CREATE PERFECT PROFILES POLICIES
-- =====================================================================

-- Policy 1: Service Role Full Access (HIGHEST PRIORITY)
CREATE POLICY "profiles_service_role_access" ON profiles
  FOR ALL
  USING (auth.role() = 'service_role')
  WITH CHECK (auth.role() = 'service_role');

-- Policy 2: Admin Users (Suppliers/Merchants) - Own Profile Access
CREATE POLICY "profiles_admin_own_access" ON profiles
  FOR SELECT
  USING (
    auth.uid() IS NOT NULL 
    AND auth.uid() = id
    AND role IN ('supplier', 'merchant', 'admin')
  );

CREATE POLICY "profiles_admin_own_update" ON profiles
  FOR UPDATE
  USING (
    auth.uid() IS NOT NULL 
    AND auth.uid() = id
    AND role IN ('supplier', 'merchant', 'admin')
  )
  WITH CHECK (
    auth.uid() IS NOT NULL 
    AND auth.uid() = id
    AND role IN ('supplier', 'merchant', 'admin')
  );

-- Policy 3: Consumer Signup (Anonymous Insert)
CREATE POLICY "profiles_consumer_signup" ON profiles
  FOR INSERT
  WITH CHECK (
    role = 'consumer'
    AND auth.uid() IS NULL
    AND phone IS NOT NULL
    AND full_name IS NOT NULL
  );

-- Policy 4: Consumer Login Verification (LIMITED Anonymous Select)
-- CRITICAL: This allows phone/email verification but limits data exposure
CREATE POLICY "profiles_consumer_login_verification" ON profiles
  FOR SELECT
  USING (
    role = 'consumer'
    AND auth.uid() IS NULL
    -- Application MUST limit fields in SELECT query
    -- Only allow: id, phone, email, role (no sensitive data)
  );

-- STEP 3: CREATE PERFECT PRODUCTS POLICIES
-- =====================================================================

-- Ensure products table has RLS enabled
ALTER TABLE products ENABLE ROW LEVEL SECURITY;

-- Drop existing products policies
DROP POLICY IF EXISTS "products_supplier_access" ON products;
DROP POLICY IF EXISTS "products_public_read" ON products;
DROP POLICY IF EXISTS "products_service_role_access" ON products;
DROP POLICY IF EXISTS "products_admin_management" ON products;
DROP POLICY IF EXISTS "products_public_browse" ON products;

-- Policy 1: Service Role Full Access
CREATE POLICY "products_service_role_access" ON products
  FOR ALL
  USING (auth.role() = 'service_role')
  WITH CHECK (auth.role() = 'service_role');

-- Policy 2: Admin Users - Manage Their Products
CREATE POLICY "products_admin_management" ON products
  FOR ALL
  USING (
    auth.uid() IS NOT NULL 
    AND auth.uid() = supplier_id
  )
  WITH CHECK (
    auth.uid() IS NOT NULL 
    AND auth.uid() = supplier_id
  );

-- Policy 3: Public Product Browsing (Anonymous)
CREATE POLICY "products_public_browse" ON products
  FOR SELECT
  USING (
    status = 'active'
    -- No auth.uid() check needed for public browsing
  );

-- STEP 4: CREATE PERFECT ORDERS POLICIES
-- =====================================================================

-- Ensure orders table has RLS enabled
ALTER TABLE orders ENABLE ROW LEVEL SECURITY;

-- Drop existing orders policies
DROP POLICY IF EXISTS "Consumers can view their own orders" ON orders;
DROP POLICY IF EXISTS "Suppliers can view orders containing their products" ON orders;
DROP POLICY IF EXISTS "Shipping companies can view assigned orders" ON orders;
DROP POLICY IF EXISTS "Allow order creation" ON orders;

-- Policy 1: Service Role Full Access
CREATE POLICY "orders_service_role_access" ON orders
  FOR ALL
  USING (auth.role() = 'service_role')
  WITH CHECK (auth.role() = 'service_role');

-- Policy 2: Consumer Orders Access (via phone matching)
-- CRITICAL: Uses session setting, not recursive table lookup
CREATE POLICY "orders_consumer_access" ON orders
  FOR SELECT
  USING (
    consumer_phone = current_setting('app.consumer_phone', true)
  );

-- Policy 3: Admin Orders Access (via order items)
CREATE POLICY "orders_admin_access" ON orders
  FOR SELECT
  USING (
    auth.uid() IS NOT NULL
    AND EXISTS (
      SELECT 1 FROM order_items oi
      WHERE oi.order_id = orders.id
      AND oi.supplier_account_id = auth.uid()
    )
  );

-- Policy 4: Order Creation (All authenticated users + consumers)
CREATE POLICY "orders_creation_access" ON orders
  FOR INSERT
  WITH CHECK (
    auth.uid() IS NOT NULL 
    OR current_setting('app.consumer_phone', true) IS NOT NULL
  );

-- Policy 5: Order Updates (Admins and consumers)
CREATE POLICY "orders_update_access" ON orders
  FOR UPDATE
  USING (
    -- Consumers can update their orders
    consumer_phone = current_setting('app.consumer_phone', true)
    OR
    -- Admins can update orders with their products
    (auth.uid() IS NOT NULL AND EXISTS (
      SELECT 1 FROM order_items oi
      WHERE oi.order_id = orders.id
      AND oi.supplier_account_id = auth.uid()
    ))
  );

-- STEP 5: CREATE PERFECT ORDER_ITEMS POLICIES
-- =====================================================================

-- Ensure order_items table has RLS enabled
ALTER TABLE order_items ENABLE ROW LEVEL SECURITY;

-- Drop existing order_items policies
DROP POLICY IF EXISTS "Order items access for consumers" ON order_items;
DROP POLICY IF EXISTS "Order items access for suppliers" ON order_items;
DROP POLICY IF EXISTS "Order items insert for order creation" ON order_items;
DROP POLICY IF EXISTS "Order items comprehensive access" ON order_items;
DROP POLICY IF EXISTS "order_items_service_role_access" ON order_items;
DROP POLICY IF EXISTS "order_items_consumer_access" ON order_items;
DROP POLICY IF EXISTS "order_items_admin_access" ON order_items;
DROP POLICY IF EXISTS "order_items_creation_access" ON order_items;

-- Policy 1: Service Role Full Access
CREATE POLICY "order_items_service_role_access" ON order_items
  FOR ALL
  USING (auth.role() = 'service_role')
  WITH CHECK (auth.role() = 'service_role');

-- Policy 2: Consumer Items Access (via orders)
CREATE POLICY "order_items_consumer_access" ON order_items
  FOR SELECT
  USING (
    EXISTS (
      SELECT 1 FROM orders o
      WHERE o.id = order_items.order_id
      AND o.consumer_phone = current_setting('app.consumer_phone', true)
    )
  );

-- Policy 3: Admin Items Access (their own items)
CREATE POLICY "order_items_admin_access" ON order_items
  FOR SELECT
  USING (
    auth.uid() IS NOT NULL
    AND supplier_account_id = auth.uid()
  );

-- Policy 4: Items Creation
CREATE POLICY "order_items_creation_access" ON order_items
  FOR INSERT
  WITH CHECK (
    auth.uid() IS NOT NULL 
    OR current_setting('app.consumer_phone', true) IS NOT NULL
  );

-- STEP 6: CREATE PERFECT SHIPMENTS POLICIES (if table exists)
-- =====================================================================

DO $$
BEGIN
  IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'shipments' AND table_schema = 'public') THEN
    -- Enable RLS
    ALTER TABLE shipments ENABLE ROW LEVEL SECURITY;
    
    -- Drop existing policies
    DROP POLICY IF EXISTS "shipments_consumer_select" ON shipments;
    DROP POLICY IF EXISTS "shipments_supplier_select" ON shipments;
    DROP POLICY IF EXISTS "shipments_shipping_company_select" ON shipments;
    DROP POLICY IF EXISTS "shipments_insert" ON shipments;
    DROP POLICY IF EXISTS "shipments_supplier_update" ON shipments;
    DROP POLICY IF EXISTS "shipments_shipping_company_update" ON shipments;
    
    -- Policy 1: Service Role Full Access
    CREATE POLICY "shipments_service_role_access" ON shipments
      FOR ALL
      USING (auth.role() = 'service_role')
      WITH CHECK (auth.role() = 'service_role');
    
    -- Policy 2: Consumer Shipments Access
    CREATE POLICY "shipments_consumer_access" ON shipments
      FOR SELECT
      USING (
        EXISTS (
          SELECT 1 FROM orders o
          WHERE o.id = shipments.order_id
          AND o.consumer_phone = current_setting('app.consumer_phone', true)
        )
      );
    
    -- Policy 3: Admin Shipments Access
    CREATE POLICY "shipments_admin_access" ON shipments
      FOR ALL
      USING (
        auth.uid() IS NOT NULL
        AND EXISTS (
          SELECT 1 FROM order_items oi
          WHERE oi.order_id = shipments.order_id
          AND oi.supplier_account_id = auth.uid()
        )
      );
    
    -- Policy 4: Shipping Company Access
    CREATE POLICY "shipments_shipping_company_access" ON shipments
      FOR ALL
      USING (
        EXISTS (
          SELECT 1 FROM shipping_companies sc
          WHERE sc.id = shipments.shipping_company_id
          AND sc.login_code = current_setting('app.shipping_company_code', true)
        )
      );
      
    RAISE NOTICE '✅ Shipments table policies created successfully';
  ELSE
    RAISE NOTICE 'ℹ️  Shipments table does not exist, skipping shipments policies';
  END IF;
END $$;

-- STEP 7: VERIFICATION AND FINAL STATUS
-- =====================================================================

-- Verify RLS is enabled on all tables
SELECT 
  '🔒 RLS_STATUS_VERIFICATION' as check_type,
  tablename,
  CASE WHEN rowsecurity THEN '✅ ENABLED' ELSE '❌ DISABLED' END as rls_status
FROM pg_tables 
LEFT JOIN pg_class ON pg_class.relname = pg_tables.tablename
WHERE tablename IN ('profiles', 'products', 'orders', 'order_items', 'shipments')
  AND schemaname = 'public'
ORDER BY tablename;

-- Show all new policies
SELECT 
  '📋 NEW_POLICIES_VERIFICATION' as check_type,
  tablename,
  policyname,
  cmd as operation,
  roles
FROM pg_policies 
WHERE tablename IN ('profiles', 'products', 'orders', 'order_items', 'shipments')
  AND schemaname = 'public'
ORDER BY tablename, policyname;

-- Final success message
SELECT '🎉 PERFECT RLS SOLUTION APPLIED SUCCESSFULLY!' as status,
       'All authentication issues should now be resolved' as message;
