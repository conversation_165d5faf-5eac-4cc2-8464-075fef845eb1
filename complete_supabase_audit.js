import { createClient } from '@supabase/supabase-js';

const supabaseUrl = 'https://irkwpzcskeqtasutqnxp.supabase.co';
const supabaseServiceKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imlya3dwemNza2VxdGFzdXRxbnhwIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0NzQ5ODY4MywiZXhwIjoyMDYzMDc0NjgzfQ.PLfCCbh4zgAJDDC6sS82fCU32JYA7e2VmwzvavF-PW8';

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function completeSupabaseAudit() {
  console.log('🔍 COMPLETE SUPABASE RLS & POLICIES AUDIT');
  console.log('='.repeat(60));

  try {
    // SECTION 1: DATABASE OVERVIEW
    console.log('\n🗄️  DATABASE OVERVIEW');
    console.log('-'.repeat(40));

    // Get all tables with RLS status
    const { data: tablesData, error: tablesError } = await supabase.rpc('get_tables_with_rls_status');
    
    if (tablesError) {
      console.log('⚠️  Using alternative method to get table info...');
      
      // Alternative: Get basic table info
      const { data: basicTables, error: basicError } = await supabase
        .from('information_schema.tables')
        .select('table_name')
        .eq('table_schema', 'public');
        
      if (!basicError) {
        console.log('📋 Tables found:', basicTables.map(t => t.table_name).join(', '));
      }
    } else {
      console.log('📋 Tables with RLS status:', tablesData);
    }

    // SECTION 2: PROFILES TABLE DETAILED ANALYSIS
    console.log('\n👤 PROFILES TABLE DETAILED ANALYSIS');
    console.log('-'.repeat(40));

    // Test profiles access with different methods
    console.log('\n🧪 Testing Profiles Access...');

    // Test 1: Service role access
    console.log('1. Testing service role access...');
    const { data: serviceProfiles, error: serviceError } = await supabase
      .from('profiles')
      .select('id, email, role, created_at')
      .limit(5);

    if (serviceError) {
      console.log('❌ Service role access failed:', serviceError.message);
    } else {
      console.log('✅ Service role access works:', serviceProfiles.length, 'profiles found');
      console.log('Sample profiles:', serviceProfiles);
    }

    // Test 2: Count profiles by role
    console.log('\n2. Testing profile counts by role...');
    const { data: roleCounts, error: roleError } = await supabase
      .from('profiles')
      .select('role')
      .then(({ data, error }) => {
        if (error) return { data: null, error };
        
        const counts = data.reduce((acc, profile) => {
          acc[profile.role] = (acc[profile.role] || 0) + 1;
          return acc;
        }, {});
        
        return { data: counts, error: null };
      });

    if (roleError) {
      console.log('❌ Role count failed:', roleError.message);
    } else {
      console.log('✅ Profile counts by role:', roleCounts);
    }

    // SECTION 3: PRODUCTS TABLE ANALYSIS
    console.log('\n📦 PRODUCTS TABLE ANALYSIS');
    console.log('-'.repeat(40));

    // Test products access
    const { data: productsData, error: productsError } = await supabase
      .from('products')
      .select('id, name, supplier_id, status')
      .limit(5);

    if (productsError) {
      console.log('❌ Products access failed:', productsError.message);
    } else {
      console.log('✅ Products access works:', productsData.length, 'products found');
      console.log('Sample products:', productsData);
    }

    // SECTION 4: ORDERS TABLE ANALYSIS
    console.log('\n🛒 ORDERS TABLE ANALYSIS');
    console.log('-'.repeat(40));

    // Test orders access
    const { data: ordersData, error: ordersError } = await supabase
      .from('orders')
      .select('id, consumer_name, status, created_at')
      .limit(5);

    if (ordersError) {
      console.log('❌ Orders access failed:', ordersError.message);
    } else {
      console.log('✅ Orders access works:', ordersData.length, 'orders found');
      console.log('Sample orders:', ordersData);
    }

    // SECTION 5: AUTH SYSTEM ANALYSIS
    console.log('\n🔐 AUTH SYSTEM ANALYSIS');
    console.log('-'.repeat(40));

    // Test auth users access
    console.log('Testing auth.users access...');
    try {
      const { data: authUsers, error: authError } = await supabase.auth.admin.listUsers();
      
      if (authError) {
        console.log('❌ Auth users access failed:', authError.message);
      } else {
        console.log('✅ Auth users access works:', authUsers.users.length, 'users found');
        console.log('Recent auth users:', authUsers.users.slice(0, 3).map(u => ({
          id: u.id,
          email: u.email,
          created_at: u.created_at
        })));
      }
    } catch (error) {
      console.log('❌ Auth users access error:', error.message);
    }

    // SECTION 6: RLS POLICIES TESTING
    console.log('\n🛡️  RLS POLICIES TESTING');
    console.log('-'.repeat(40));

    // Test with anon key
    const anonSupabase = createClient(supabaseUrl, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imlya3dwemNza2VxdGFzdXRxbnhwIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDc0OTg2ODMsImV4cCI6MjA2MzA3NDY4M30.bUv9zxx9q_sVUwv_-G6WbHsA53-SU5tCCWbOS_5Xp94');

    console.log('Testing anon access to profiles...');
    const { data: anonProfiles, error: anonError } = await anonSupabase
      .from('profiles')
      .select('id, role')
      .limit(1);

    if (anonError) {
      console.log('❌ Anon profiles access failed:', anonError.message);
    } else {
      console.log('✅ Anon profiles access works:', anonProfiles);
    }

    // SECTION 7: COMPREHENSIVE SUMMARY
    console.log('\n📋 COMPREHENSIVE SUMMARY');
    console.log('-'.repeat(40));

    const summary = {
      profiles_service_access: !serviceError,
      profiles_anon_access: !anonError,
      products_access: !productsError,
      orders_access: !ordersError,
      auth_users_access: false, // Will be updated based on auth test
      total_profiles: serviceProfiles?.length || 0,
      profile_roles: roleCounts || {},
      timestamp: new Date().toISOString()
    };

    console.log('🎯 FINAL SUMMARY:', JSON.stringify(summary, null, 2));

    // SECTION 8: RECOMMENDATIONS
    console.log('\n💡 RECOMMENDATIONS');
    console.log('-'.repeat(40));

    if (serviceError) {
      console.log('🚨 CRITICAL: Service role cannot access profiles table');
      console.log('   → This indicates RLS policy issues');
      console.log('   → Run the SQL audit script for detailed policy analysis');
    }

    if (!anonError) {
      console.log('⚠️  WARNING: Anonymous users can access profiles');
      console.log('   → This might be intentional for consumer access');
      console.log('   → Verify this is the intended behavior');
    }

    if (productsError) {
      console.log('🚨 ISSUE: Cannot access products table');
      console.log('   → Check products table RLS policies');
    }

    console.log('\n✅ AUDIT COMPLETE!');
    console.log('📄 For detailed SQL-level analysis, run the COMPLETE_SUPABASE_AUDIT.sql script');

  } catch (error) {
    console.error('❌ Audit failed:', error);
  }
}

// Run the audit
completeSupabaseAudit();
