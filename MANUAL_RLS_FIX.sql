-- =====================================================================
-- 🚨 MANUAL RLS FIX - RUN THIS IN SUPABASE SQL EDITOR
-- =====================================================================
-- Copy and paste this entire script into the Supabase SQL Editor
-- Go to: https://supabase.com/dashboard/project/irkwpzcskeqtasutqnxp/sql
-- Paste this script and click "Run"

-- STEP 1: SHOW CURRENT PROBLEMATIC POLICIES
-- =====================================================================
SELECT 
  '=== CURRENT POLICIES ===' as info,
  policyname, 
  cmd, 
  qual as using_clause,
  with_check
FROM pg_policies 
WHERE tablename = 'profiles' 
AND schemaname = 'public'
ORDER BY policyname;

-- STEP 2: DISABLE RLS TEMPORARILY
-- =====================================================================
ALTER TABLE profiles DISABLE ROW LEVEL SECURITY;

-- STEP 3: DROP ALL EXISTING POLICIES (NUCLEAR OPTION)
-- =====================================================================
-- This will drop every single policy on the profiles table

DO $$
DECLARE
    policy_record RECORD;
BEGIN
    -- Loop through all policies and drop them
    FOR policy_record IN 
        SELECT policyname 
        FROM pg_policies 
        WHERE tablename = 'profiles' 
        AND schemaname = 'public'
    LOOP
        EXECUTE 'DROP POLICY IF EXISTS "' || policy_record.policyname || '" ON profiles';
        RAISE NOTICE 'Dropped policy: %', policy_record.policyname;
    END LOOP;
    
    RAISE NOTICE 'All policies dropped successfully';
END $$;

-- STEP 4: RE-ENABLE RLS
-- =====================================================================
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;

-- STEP 5: CREATE ULTRA-SIMPLE, NON-RECURSIVE POLICIES
-- =====================================================================

-- Policy 1: Service role can do everything (for backend operations)
CREATE POLICY "service_role_access" ON profiles
  FOR ALL
  USING (auth.role() = 'service_role')
  WITH CHECK (auth.role() = 'service_role');

-- Policy 2: Users can access their own profile (NO RECURSION)
-- This is the simplest possible policy - just checks auth.uid() = id
CREATE POLICY "own_profile_access" ON profiles
  FOR ALL
  USING (auth.uid() = id)
  WITH CHECK (auth.uid() = id);

-- Policy 3: Anonymous consumer access (NO AUTH.USERS REFERENCE)
-- Consumers don't have auth.users records, so they need anonymous access
CREATE POLICY "consumer_access" ON profiles
  FOR ALL
  USING (role = 'consumer')
  WITH CHECK (role = 'consumer');

-- STEP 6: VERIFY THE FIX
-- =====================================================================

-- Show the new policies
SELECT 
  '=== NEW POLICIES ===' as info,
  policyname, 
  cmd, 
  qual as using_clause,
  with_check
FROM pg_policies 
WHERE tablename = 'profiles' 
AND schemaname = 'public'
ORDER BY policyname;

-- Test that we can access profiles
SELECT 
  '=== TEST RESULTS ===' as info,
  count(*) as total_profiles,
  count(*) FILTER (WHERE role = 'supplier') as suppliers,
  count(*) FILTER (WHERE role = 'merchant') as merchants,
  count(*) FILTER (WHERE role = 'consumer') as consumers
FROM profiles;

-- Show recent signups to verify data integrity
SELECT 
  '=== RECENT SIGNUPS ===' as info,
  id,
  email,
  role,
  full_name,
  created_at
FROM profiles 
WHERE role IN ('supplier', 'merchant', 'admin')
ORDER BY created_at DESC 
LIMIT 5;

-- STEP 7: SUCCESS MESSAGE
-- =====================================================================
SELECT '🎉 RLS FIX COMPLETED SUCCESSFULLY! 🎉' as success_message;
SELECT 'Please test admin signup and login in the browser now.' as next_step;
