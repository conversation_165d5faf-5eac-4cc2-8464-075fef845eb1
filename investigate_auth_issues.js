#!/usr/bin/env node

/**
 * AROUZ MARKET - Critical Authentication Investigation Script
 *
 * This script connects to the production Supabase project and performs
 * a comprehensive analysis of authentication and RLS policy issues.
 */

import { createClient } from '@supabase/supabase-js';

// Production Supabase credentials (provided by user)
const SUPABASE_URL = 'https://irkwpzcskeqtasutqnxp.supabase.co';
const SUPABASE_SERVICE_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imlya3dwemNza2VxdGFzdXRxbnhwIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0NzQ5ODY4MywiZXhwIjoyMDYzMDc0NjgzfQ.PLfCCbh4zgAJDDC6sS82fCU32JYA7e2VmwzvavF-PW8';
const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imlya3dwemNza2VxdGFzdXRxbnhwIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDc0OTg2ODMsImV4cCI6MjA2MzA3NDY4M30.bUv9zxx9q_sVUwv_-G6WbHsA53-SU5tCCWbOS_5Xp94';

// Create Supabase clients
const supabaseAdmin = createClient(SUPABASE_URL, SUPABASE_SERVICE_KEY);
const supabaseAnon = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  bright: '\x1b[1m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logSection(title) {
  console.log('\n' + '='.repeat(80));
  log(`🔍 ${title}`, 'bright');
  console.log('='.repeat(80));
}

async function investigateAuthIssues() {
  try {
    logSection('AROUZ MARKET - CRITICAL AUTHENTICATION INVESTIGATION');
    log('🚨 Investigating authentication and data isolation issues...', 'red');
    log(`📡 Connected to: ${SUPABASE_URL}`, 'cyan');

    // 1. Test basic connection
    logSection('1. CONNECTION TEST');
    const { data: connectionTest, error: connectionError } = await supabaseAdmin
      .from('profiles')
      .select('count')
      .limit(1);
    
    if (connectionError) {
      log(`❌ Connection failed: ${connectionError.message}`, 'red');
      return;
    }
    log('✅ Successfully connected to Supabase', 'green');

    // 2. Analyze RLS policies
    logSection('2. RLS POLICIES ANALYSIS');
    const { data: policies, error: policiesError } = await supabaseAdmin
      .rpc('get_policies_info');
    
    if (policiesError) {
      log(`⚠️  Could not fetch policies via RPC, trying direct query...`, 'yellow');
      
      // Try direct query to pg_policies
      const { data: directPolicies, error: directError } = await supabaseAdmin
        .from('pg_policies')
        .select('*')
        .in('tablename', ['profiles', 'orders', 'order_items', 'shipments', 'products']);
      
      if (directError) {
        log(`❌ Failed to fetch policies: ${directError.message}`, 'red');
      } else {
        log(`📋 Found ${directPolicies?.length || 0} RLS policies`, 'blue');
        if (directPolicies && directPolicies.length > 0) {
          directPolicies.forEach(policy => {
            log(`  📝 ${policy.tablename}.${policy.policyname}: ${policy.cmd} - ${policy.roles?.join(', ')}`, 'cyan');
          });
        }
      }
    }

    // 3. Check profiles table structure and data
    logSection('3. PROFILES TABLE ANALYSIS');
    const { data: profilesStructure, error: structureError } = await supabaseAdmin
      .rpc('get_table_info', { table_name: 'profiles' });
    
    if (structureError) {
      log(`⚠️  Could not get table structure via RPC, checking manually...`, 'yellow');
    }

    // Check profiles data
    const { data: profilesData, error: profilesError } = await supabaseAdmin
      .from('profiles')
      .select('id, email, phone, role, full_name, created_at')
      .limit(10);
    
    if (profilesError) {
      log(`❌ Failed to fetch profiles: ${profilesError.message}`, 'red');
    } else {
      log(`👥 Found ${profilesData?.length || 0} profiles in database`, 'blue');
      if (profilesData && profilesData.length > 0) {
        const roleCount = profilesData.reduce((acc, profile) => {
          acc[profile.role] = (acc[profile.role] || 0) + 1;
          return acc;
        }, {});
        log(`📊 Role distribution: ${JSON.stringify(roleCount)}`, 'cyan');
      }
    }

    // 4. Test authentication isolation
    logSection('4. AUTHENTICATION ISOLATION TEST');
    
    // Test anon access to profiles
    const { data: anonProfiles, error: anonError } = await supabaseAnon
      .from('profiles')
      .select('id, email, role')
      .limit(5);
    
    if (anonError) {
      log(`✅ Anonymous access properly blocked: ${anonError.message}`, 'green');
    } else {
      log(`🚨 CRITICAL: Anonymous access allowed! Found ${anonProfiles?.length || 0} profiles`, 'red');
      if (anonProfiles && anonProfiles.length > 0) {
        log(`⚠️  Exposed data: ${JSON.stringify(anonProfiles)}`, 'yellow');
      }
    }

    // 5. Check orders and products access
    logSection('5. DATA ACCESS PATTERNS');
    
    const tables = ['orders', 'order_items', 'products', 'shipments'];
    for (const table of tables) {
      const { data, error } = await supabaseAnon
        .from(table)
        .select('*')
        .limit(1);
      
      if (error) {
        log(`✅ ${table}: Anonymous access blocked`, 'green');
      } else {
        log(`🚨 ${table}: Anonymous access allowed! (${data?.length || 0} records)`, 'red');
      }
    }

    // 6. Check recent migrations
    logSection('6. RECENT MIGRATIONS ANALYSIS');
    const { data: migrations, error: migrationsError } = await supabaseAdmin
      .from('supabase_migrations')
      .select('*')
      .order('version', { ascending: false })
      .limit(10);
    
    if (migrationsError) {
      log(`⚠️  Could not fetch migrations: ${migrationsError.message}`, 'yellow');
    } else {
      log(`📋 Recent migrations:`, 'blue');
      migrations?.forEach(migration => {
        log(`  📅 ${migration.version}: ${migration.name || 'Unnamed'}`, 'cyan');
      });
    }

    logSection('INVESTIGATION COMPLETE');
    log('🔍 Investigation completed. Check the output above for issues.', 'bright');
    
  } catch (error) {
    log(`💥 Investigation failed: ${error.message}`, 'red');
    console.error(error);
  }
}

// Run the investigation
investigateAuthIssues();
