# Professional Experience - ArouzMarket Platform

## Full-Stack Software Engineer | ArouzMarket (Automotive Parts Marketplace)
**Duration:** 6 months (40+ hours/week) | **Work Schedule:** Monday-Friday 9AM-5PM + evening deployments
**Tech Stack:** React 18, TypeScript, Node.js, Supabase PostgreSQL, Tailwind CSS, Vite, React Query, Framer Motion

• **Solved critical authentication isolation challenge** by architecting a zero-cross-contamination multi-role system supporting 4 user types (suppliers, merchants, consumers, distributors) using Supabase RLS policies and localStorage session management, eliminating authentication conflicts that were causing 90% session corruption and reducing localStorage operations by 90% through intelligent 5-second session caching

• **Built AI-powered vehicle compatibility engine** from scratch using custom search algorithms, ZXing barcode scanner integration, and prompt-engineered AI description generator that processes part numbers, OEM codes, and EAN barcodes to auto-generate structured product specifications, reducing manual data entry by 85% and improving part-to-vehicle matching accuracy from 40% to 95% across 200+ car models

• **Engineered high-performance marketplace infrastructure** using React 18 Suspense, lazy-loaded components, React Query with 5-minute stale time, and Postgres query optimization, achieving consistent sub-75ms page transitions and 2-5 minute deployment cycles, while supporting 1000+ SKUs across 40+ categories with real-time inventory tracking and dynamic pricing tiers

• **Developed complex order processing system** integrating Google Maps JavaScript API, Dexatel SMS Edge Functions, and custom fee calculation engine with tiered commission structures, handling wholesale/retail pricing logic, automated shipping company assignment algorithms, and real-time tracking across 48 Algerian administrative divisions with 100% geographic accuracy using coordinate-based wilaya detection

• **Implemented production-grade internationalization system** supporting Arabic (RTL), English, and French with i18next, built responsive mobile-first UI using Tailwind CSS and Radix UI components, integrated Web Vitals performance monitoring, and maintained Airbnb-style design patterns with Framer Motion animations under 75ms transition requirements

• **Established enterprise CI/CD pipeline** with Vite build optimization, automated FTP deployment, environment-specific configurations (dev/UAT/production), comprehensive error boundary implementation, and zero-downtime deployment strategy, managing 15+ deployment packages with 99.9% uptime and instant rollback capabilities using structured deployment versioning

**Technical Challenges Solved:** Authentication session conflicts (reduced from 90% failure to 0%), slow product search (rewrote filtering logic using indexed tokens, improved speed by 300%), complex RLS policy recursion (implemented session-based consumer authentication), Google Maps CORS issues (migrated to Supabase Edge Functions), and deployment performance bottlenecks (optimized bundle splitting and lazy loading).

**Business Impact:** Delivered production-ready marketplace handling 25+ merchant registrations, processing 100+ daily product scans, supporting real-time order tracking for 500+ products, and positioning platform to compete with established automotive marketplaces in North African market with enterprise-scale architecture capable of handling 10,000+ concurrent users.
