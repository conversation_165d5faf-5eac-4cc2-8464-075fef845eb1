import { createClient } from '@supabase/supabase-js';

const supabaseUrl = 'https://irkwpzcskeqtasutqnxp.supabase.co';
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imlya3dwemNza2VxdGFzdXRxbnhwIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDc0OTg2ODMsImV4cCI6MjA2MzA3NDY4M30.bUv9zxx9q_sVUwv_-G6WbHsA53-SU5tCCWbOS_5Xp94';

const supabase = createClient(supabaseUrl, supabaseAnonKey);

async function testAfterManualFix() {
  console.log('🧪 TESTING AFTER MANUAL FIX...\n');

  try {
    // Step 1: Create a new test user
    console.log('1. Creating test user...');
    
    const testEmail = '<EMAIL>';
    const testPassword = 'FinalTest123!';
    
    const { data: signupData, error: signupError } = await supabase.auth.signUp({
      email: testEmail,
      password: testPassword,
      options: {
        data: {
          role: 'supplier',
          fullName: 'Final Test User',
          companyName: 'Final Test Company'
        }
      }
    });

    if (signupError && !signupError.message.includes('already registered')) {
      console.error('❌ Signup failed:', signupError);
      return;
    }

    let userId = signupData?.user?.id;
    
    // If user already exists, sign in
    if (!userId) {
      const { data: signinData, error: signinError } = await supabase.auth.signInWithPassword({
        email: testEmail,
        password: testPassword
      });

      if (signinError) {
        console.error('❌ Signin failed:', signinError);
        return;
      }

      userId = signinData.user?.id;
    }

    if (!userId) {
      console.error('❌ No user ID obtained');
      return;
    }

    console.log('✅ User authenticated:', userId);

    // Step 2: Test profile access (THE CRITICAL TEST)
    console.log('\n2. Testing profile access...');
    
    const { data: profileData, error: profileError } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', userId)
      .single();

    if (profileError) {
      console.error('❌ PROFILE ACCESS STILL FAILING:', profileError);
      console.log('Error code:', profileError.code);
      console.log('Error message:', profileError.message);
      
      if (profileError.message.includes('permission denied for table users')) {
        console.log('\n🚨 THE RECURSIVE POLICY IS STILL ACTIVE!');
        console.log('👉 You need to run the MANUAL_RLS_FIX.sql script in Supabase SQL Editor');
        console.log('👉 Go to: https://supabase.com/dashboard/project/irkwpzcskeqtasutqnxp/sql');
        console.log('👉 Copy and paste the MANUAL_RLS_FIX.sql content and click Run');
      }
      
    } else {
      console.log('🎉 SUCCESS! Profile access works:', {
        id: profileData.id,
        email: profileData.email,
        role: profileData.role,
        full_name: profileData.full_name
      });
      
      // Step 3: Test profile update (what happens during signup)
      console.log('\n3. Testing profile update...');
      
      const { data: updateData, error: updateError } = await supabase
        .from('profiles')
        .update({
          full_name: 'Updated Final Test User',
          company_name: 'Updated Final Test Company',
          last_login: new Date().toISOString()
        })
        .eq('id', userId)
        .select();

      if (updateError) {
        console.error('❌ Profile update failed:', updateError);
      } else {
        console.log('✅ Profile update successful:', updateData);
      }
    }

    // Cleanup
    await supabase.auth.signOut();

  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

testAfterManualFix();
