import { createClient } from '@supabase/supabase-js';

// Supabase configuration
const supabaseUrl = 'https://irkwpzcskeqtasutqnxp.supabase.co';
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imlya3dwemNza2VxdGFzdXRxbnhwIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDc0OTg2ODMsImV4cCI6MjA2MzA3NDY4M30.bUv9zxx9q_sVUwv_-G6WbHsA53-SU5tCCWbOS_5Xp94';

const supabase = createClient(supabaseUrl, supabaseAnonKey);

async function testWishlistFunctionality() {
  try {
    console.log('🧪 Testing wishlist functionality...');

    // Test consumer phone (use a known consumer phone from your database)
    const testConsumerPhone = '+213555123456'; // Replace with actual consumer phone
    
    // Step 1: Set consumer phone context
    console.log('📋 Step 1: Setting consumer phone context...');
    const { data: contextData, error: contextError } = await supabase.rpc('set_app_config', {
      setting_name: 'app.consumer_phone',
      setting_value: testConsumerPhone,
      is_local: false
    });

    if (contextError) {
      console.error('❌ Error setting consumer context:', contextError);
      return;
    }
    console.log('✅ Consumer context set successfully');

    // Step 2: Test adding to wishlist
    console.log('📋 Step 2: Testing add to wishlist...');
    const testProduct = {
      consumer_phone: testConsumerPhone,
      product_id: 'TEST-WISHLIST-001',
      product_name: 'Test Product for Wishlist',
      product_price: 100.00,
      priority: 1
    };

    const { data: insertData, error: insertError } = await supabase
      .from('consumer_wishlists')
      .insert(testProduct)
      .select()
      .single();

    if (insertError) {
      console.error('❌ Error adding to wishlist:', insertError);
      console.error('Error details:', insertError.message);
    } else {
      console.log('✅ Successfully added to wishlist:', insertData);
    }

    // Step 3: Test reading from wishlist
    console.log('📋 Step 3: Testing read from wishlist...');
    const { data: selectData, error: selectError } = await supabase
      .from('consumer_wishlists')
      .select('*')
      .eq('consumer_phone', testConsumerPhone);

    if (selectError) {
      console.error('❌ Error reading wishlist:', selectError);
      console.error('Error details:', selectError.message);
    } else {
      console.log('✅ Successfully read wishlist:', selectData);
    }

    // Step 4: Test removing from wishlist
    console.log('📋 Step 4: Testing remove from wishlist...');
    const { error: deleteError } = await supabase
      .from('consumer_wishlists')
      .delete()
      .eq('consumer_phone', testConsumerPhone)
      .eq('product_id', 'TEST-WISHLIST-001');

    if (deleteError) {
      console.error('❌ Error removing from wishlist:', deleteError);
      console.error('Error details:', deleteError.message);
    } else {
      console.log('✅ Successfully removed from wishlist');
    }

    console.log('🎉 Wishlist functionality test completed!');

  } catch (error) {
    console.error('❌ Error in testWishlistFunctionality:', error);
  }
}

// Run the test
testWishlistFunctionality();
