import { createClient } from '@supabase/supabase-js';

// Supabase configuration
const supabaseUrl = 'https://irkwpzcskeqtasutqnxp.supabase.co';
const supabaseServiceKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imlya3dwemNza2VxdGFzdXRxbnhwIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0NzQ5ODY4MywiZXhwIjoyMDYzMDc0NjgzfQ.PLfCCbh4zgAJDDC6sS82fCU32JYA7e2VmwzvavF-PW8';
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imlya3dwemNza2VxdGFzdXRxbnhwIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDc0OTg2ODMsImV4cCI6MjA2MzA3NDY4M30.bUv9zxx9q_sVUwv_-G6WbHsA53-SU5tCCWbOS_5Xp94';

const supabaseService = createClient(supabaseUrl, supabaseServiceKey);
const supabaseAnon = createClient(supabaseUrl, supabaseAnonKey);

async function testDisableRLS() {
  try {
    console.log('🔧 Testing wishlist with <PERSON><PERSON> temporarily disabled...');

    // Step 1: Disable R<PERSON> temporarily
    console.log('📋 Step 1: Disabling RLS temporarily...');
    
    // Use raw SQL to disable RLS
    const { data: disableResult, error: disableError } = await supabaseService
      .rpc('exec_sql', { sql_query: 'ALTER TABLE consumer_wishlists DISABLE ROW LEVEL SECURITY;' });

    if (disableError) {
      console.error('❌ Error disabling RLS:', disableError);
      return;
    }
    console.log('✅ RLS disabled');

    // Step 2: Test insert with anonymous role
    console.log('📋 Step 2: Testing insert with anonymous role (RLS disabled)...');
    
    const testPhone = '+213555123456';
    const { data: insertData, error: insertError } = await supabaseAnon
      .from('consumer_wishlists')
      .insert({
        consumer_phone: testPhone,
        product_id: 'NO-RLS-TEST-001',
        product_name: 'Test Without RLS',
        priority: 1
      })
      .select()
      .single();

    if (insertError) {
      console.error('❌ Insert failed even without RLS:', insertError);
    } else {
      console.log('✅ Insert successful without RLS:', insertData);
      
      // Test select
      const { data: selectData, error: selectError } = await supabaseAnon
        .from('consumer_wishlists')
        .select('*')
        .eq('product_id', 'NO-RLS-TEST-001');

      if (selectError) {
        console.error('❌ Select failed:', selectError);
      } else {
        console.log('✅ Select successful:', selectData);
      }
      
      // Clean up
      await supabaseAnon
        .from('consumer_wishlists')
        .delete()
        .eq('product_id', 'NO-RLS-TEST-001');
      console.log('✅ Test data cleaned up');
    }

    // Step 3: Re-enable RLS
    console.log('📋 Step 3: Re-enabling RLS...');
    
    const { data: enableResult, error: enableError } = await supabaseService
      .rpc('exec_sql', { sql_query: 'ALTER TABLE consumer_wishlists ENABLE ROW LEVEL SECURITY;' });

    if (enableError) {
      console.error('❌ Error re-enabling RLS:', enableError);
    } else {
      console.log('✅ RLS re-enabled');
    }

    // Step 4: Test insert with RLS enabled again
    console.log('📋 Step 4: Testing insert with RLS enabled again...');
    
    const { data: insertData2, error: insertError2 } = await supabaseAnon
      .from('consumer_wishlists')
      .insert({
        consumer_phone: testPhone,
        product_id: 'RLS-TEST-002',
        product_name: 'Test With RLS Again',
        priority: 1
      })
      .select()
      .single();

    if (insertError2) {
      console.error('❌ Insert failed with RLS enabled:', insertError2.message);
      console.log('🔍 This confirms the issue is with RLS policies');
    } else {
      console.log('✅ Insert successful with RLS enabled:', insertData2);
      
      // Clean up
      await supabaseAnon
        .from('consumer_wishlists')
        .delete()
        .eq('product_id', 'RLS-TEST-002');
    }

    console.log('🎉 RLS test completed!');

  } catch (error) {
    console.error('❌ Error in test:', error);
  }
}

// Create exec_sql function if it doesn't exist
async function ensureExecSqlFunction() {
  try {
    const createFunctionSQL = `
      CREATE OR REPLACE FUNCTION exec_sql(sql_query TEXT)
      RETURNS TEXT AS $$
      BEGIN
        EXECUTE sql_query;
        RETURN 'Success';
      EXCEPTION WHEN OTHERS THEN
        RETURN SQLERRM;
      END;
      $$ LANGUAGE plpgsql SECURITY DEFINER;
    `;

    await supabaseService.rpc('exec_sql', { sql_query: createFunctionSQL });
    console.log('✅ exec_sql function ready');
  } catch (error) {
    console.log('📝 exec_sql function check completed');
  }
}

// Run the test
console.log('🚀 Starting RLS disable test...');
ensureExecSqlFunction().then(() => {
  testDisableRLS();
});
