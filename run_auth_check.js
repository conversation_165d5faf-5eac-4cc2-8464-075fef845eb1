import { createClient } from '@supabase/supabase-js';
import fs from 'fs';

// Production Supabase credentials
const SUPABASE_URL = 'https://irkwpzcskeqtasutqnxp.supabase.co';
const SUPABASE_SERVICE_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imlya3dwemNza2VxdGFzdXRxbnhwIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0NzQ5ODY4MywiZXhwIjoyMDYzMDc0NjgzfQ.PLfCCbh4zgAJDDC6sS82fCU32JYA7e2VmwzvavF-PW8';

const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_KEY);

console.log('🔍 AROUZ MARKET - Authentication Investigation');
console.log('=' .repeat(60));

async function runCheck() {
  try {
    // Test connection first
    console.log('📡 Testing connection...');
    const { data: testData, error: testError } = await supabase
      .from('profiles')
      .select('count')
      .limit(1);
    
    if (testError) {
      console.error('❌ Connection failed:', testError.message);
      return;
    }
    console.log('✅ Connected successfully');

    // 1. Check RLS status
    console.log('\n🔒 Checking RLS status...');
    const { data: rlsStatus, error: rlsError } = await supabase
      .rpc('sql', {
        query: `
          SELECT 
            tablename,
            CASE WHEN rowsecurity THEN 'ENABLED' ELSE 'DISABLED' END as rls_status
          FROM pg_tables 
          LEFT JOIN pg_class ON pg_class.relname = pg_tables.tablename
          WHERE tablename IN ('profiles', 'orders', 'order_items', 'shipments', 'products')
            AND schemaname = 'public'
          ORDER BY tablename;
        `
      });

    if (rlsError) {
      console.log('⚠️  RLS check failed, trying alternative method...');
      
      // Try direct table access to check if RLS is working
      const { data: profilesTest, error: profilesError } = await supabase
        .from('profiles')
        .select('id, role')
        .limit(5);
      
      if (profilesError) {
        console.log('✅ RLS appears to be working (access denied)');
      } else {
        console.log(`🚨 RLS may be disabled (got ${profilesTest?.length || 0} records)`);
      }
    } else {
      console.log('RLS Status:', rlsStatus);
    }

    // 2. Check profiles data
    console.log('\n👥 Checking profiles data...');
    const { data: profilesData, error: profilesError } = await supabase
      .from('profiles')
      .select('role')
      .limit(100);
    
    if (profilesError) {
      console.log('❌ Profiles access failed:', profilesError.message);
    } else {
      const roleCounts = profilesData.reduce((acc, p) => {
        acc[p.role] = (acc[p.role] || 0) + 1;
        return acc;
      }, {});
      console.log('📊 Role distribution:', roleCounts);
    }

    // 3. Check policies
    console.log('\n📋 Checking RLS policies...');
    const { data: policies, error: policiesError } = await supabase
      .rpc('sql', {
        query: `
          SELECT tablename, policyname, cmd, roles
          FROM pg_policies 
          WHERE tablename IN ('profiles', 'orders', 'order_items', 'shipments')
            AND schemaname = 'public'
          ORDER BY tablename, policyname;
        `
      });

    if (policiesError) {
      console.log('⚠️  Could not fetch policies:', policiesError.message);
    } else {
      console.log(`Found ${policies?.length || 0} policies`);
      if (policies && policies.length > 0) {
        policies.forEach(p => {
          console.log(`  📝 ${p.tablename}.${p.policyname} (${p.cmd})`);
        });
      }
    }

    // 4. Test anonymous access
    console.log('\n🔓 Testing anonymous access...');
    const anonClient = createClient(SUPABASE_URL, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imlya3dwemNza2VxdGFzdXRxbnhwIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDc0OTg2ODMsImV4cCI6MjA2MzA3NDY4M30.bUv9zxx9q_sVUwv_-G6WbHsA53-SU5tCCWbOS_5Xp94');
    
    const { data: anonData, error: anonError } = await anonClient
      .from('profiles')
      .select('id, role')
      .limit(5);
    
    if (anonError) {
      console.log('✅ Anonymous access properly blocked');
    } else {
      console.log(`🚨 CRITICAL: Anonymous access allowed! Got ${anonData?.length || 0} records`);
      if (anonData && anonData.length > 0) {
        console.log('Exposed data:', anonData);
      }
    }

    console.log('\n✅ Investigation complete!');

  } catch (error) {
    console.error('💥 Investigation failed:', error.message);
  }
}

runCheck();
