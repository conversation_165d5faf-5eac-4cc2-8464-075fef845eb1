-- =====================================================================
-- AROUZ MARKET - MANUAL AUTHENTICATION FIX
-- =====================================================================
-- INSTRUCTIONS: Copy and paste this SQL into Supabase SQL Editor
-- Run this with the service role or as postgres user
-- This will fix the authentication isolation issues
-- =====================================================================

-- STEP 1: Clean up all existing conflicting policies on profiles table
DROP POLICY IF EXISTS "service_role_all_access" ON profiles;
DROP POLICY IF EXISTS "users_select_own_profile" ON profiles;
DROP POLICY IF EXISTS "users_update_own_profile" ON profiles;
DROP POLICY IF EXISTS "users_insert_own_profile" ON profiles;
DROP POLICY IF EXISTS "profiles_consumer_anonymous_select" ON profiles;
DROP POLICY IF EXISTS "profiles_consumer_anonymous_insert" ON profiles;
DROP POLICY IF EXISTS "profiles_consumer_anonymous_update" ON profiles;
DROP POLICY IF EXISTS "profiles_authenticated_select_non_consumer" ON profiles;
DROP POLICY IF EXISTS "profiles_authenticated_update_non_consumer" ON profiles;
DROP POLICY IF EXISTS "profiles_authenticated_insert_non_consumer" ON profiles;
DROP POLICY IF EXISTS "profiles_service_role_all_access" ON profiles;
DROP POLICY IF EXISTS "Users can view their own profile" ON profiles;
DROP POLICY IF EXISTS "Users can update their own profile" ON profiles;
DROP POLICY IF EXISTS "Users can insert their own profile" ON profiles;
DROP POLICY IF EXISTS "Service role can do anything" ON profiles;
DROP POLICY IF EXISTS "Postgres role can do anything" ON profiles;

-- STEP 2: Create working RLS policies for profiles table

-- 1. Service role needs full access for triggers and admin operations
CREATE POLICY "profiles_service_role_access" ON profiles
  FOR ALL
  USING (auth.role() = 'service_role')
  WITH CHECK (auth.role() = 'service_role');

-- 2. Authenticated users (suppliers/merchants) can access their own profiles
CREATE POLICY "profiles_authenticated_own_access" ON profiles
  FOR ALL
  USING (auth.uid() = id)
  WITH CHECK (auth.uid() = id);

-- 3. Consumer authentication - Allow anonymous access for consumer profiles only
-- This is needed because consumers don't have auth.users records
CREATE POLICY "profiles_consumer_anonymous_access" ON profiles
  FOR ALL
  USING (role = 'consumer')
  WITH CHECK (role = 'consumer');

-- 4. Allow anonymous read access for login verification (all roles)
-- This is needed for the login process to check if users exist
CREATE POLICY "profiles_anonymous_login_check" ON profiles
  FOR SELECT
  USING (true);

-- STEP 3: Ensure proper grants
GRANT ALL ON profiles TO postgres;
GRANT ALL ON profiles TO service_role;
GRANT SELECT, UPDATE, INSERT ON profiles TO authenticated;
GRANT SELECT ON profiles TO anon;

-- STEP 4: Fix orders table policies (if orders table exists)
-- Check if orders table exists and fix its policies
DO $$
BEGIN
  IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'orders' AND table_schema = 'public') THEN
    -- Drop existing problematic orders policies
    DROP POLICY IF EXISTS "orders_consumer_select" ON orders;
    DROP POLICY IF EXISTS "orders_supplier_select" ON orders;
    DROP POLICY IF EXISTS "orders_insert" ON orders;
    DROP POLICY IF EXISTS "orders_update" ON orders;
    
    -- Create working orders policies
    -- Consumers can view their own orders
    CREATE POLICY "orders_consumer_access" ON orders
      FOR SELECT USING (
        consumer_phone IN (
          SELECT phone FROM profiles 
          WHERE role = 'consumer'
        )
      );
    
    -- Suppliers/merchants can view orders (with application-level filtering)
    CREATE POLICY "orders_supplier_access" ON orders
      FOR SELECT USING (
        EXISTS (
          SELECT 1 FROM profiles p
          WHERE p.id = auth.uid()
          AND p.role IN ('supplier', 'merchant')
        )
      );
    
    -- Allow order creation
    CREATE POLICY "orders_insert_access" ON orders
      FOR INSERT WITH CHECK (true);
    
    -- Allow order updates
    CREATE POLICY "orders_update_access" ON orders
      FOR UPDATE USING (
        -- Consumers can update their own orders
        consumer_phone IN (
          SELECT phone FROM profiles
          WHERE role = 'consumer'
        )
        OR
        -- Suppliers/merchants can update orders
        EXISTS (
          SELECT 1 FROM profiles p
          WHERE p.id = auth.uid()
          AND p.role IN ('supplier', 'merchant')
        )
      );
      
    RAISE NOTICE 'Orders table policies updated successfully';
  ELSE
    RAISE NOTICE 'Orders table does not exist, skipping orders policies';
  END IF;
END $$;

-- STEP 5: Verification
-- Verify RLS is enabled on profiles
SELECT 
  'RLS_STATUS' as check_type,
  schemaname,
  tablename,
  CASE WHEN rowsecurity THEN 'ENABLED' ELSE 'DISABLED' END as rls_status
FROM pg_tables 
LEFT JOIN pg_class ON pg_class.relname = pg_tables.tablename
WHERE tablename = 'profiles'
  AND schemaname = 'public';

-- Show current policies
SELECT 
  'CURRENT_POLICIES' as check_type,
  schemaname,
  tablename,
  policyname,
  cmd,
  roles
FROM pg_policies 
WHERE tablename = 'profiles'
  AND schemaname = 'public'
ORDER BY policyname;

-- Test basic access
SELECT 
  'ACCESS_TEST' as check_type,
  COUNT(*) as total_profiles,
  COUNT(CASE WHEN role = 'consumer' THEN 1 END) as consumers,
  COUNT(CASE WHEN role = 'supplier' THEN 1 END) as suppliers,
  COUNT(CASE WHEN role = 'merchant' THEN 1 END) as merchants
FROM profiles;

-- Final success message
SELECT '✅ AUTHENTICATION FIX APPLIED SUCCESSFULLY!' as status;
