import { createClient } from '@supabase/supabase-js';

const supabaseUrl = 'https://irkwpzcskeqtasutqnxp.supabase.co';
const supabaseServiceKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imlya3dwemNza2VxdGFzdXRxbnhwIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0NzQ5ODY4MywiZXhwIjoyMDYzMDc0NjgzfQ.PLfCCbh4zgAJDDC6sS82fCU32JYA7e2VmwzvavF-PW8';

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function applyEmergencyFix() {
  console.log('🚨 Applying Emergency Admin Auth Fix...\n');

  try {
    // Step 1: Drop all conflicting policies
    console.log('1. Dropping conflicting policies...');
    
    const policiesToDrop = [
      'profiles_service_role_access',
      'profiles_admin_own_access', 
      'profiles_admin_own_update',
      'profiles_consumer_signup',
      'profiles_consumer_login_verification',
      'profiles_authenticated_select_simple',
      'profiles_authenticated_update_simple',
      'profiles_authenticated_insert_simple',
      'service_role_all_access',
      'users_select_own_profile',
      'users_update_own_profile',
      'users_insert_own_profile',
      'Users can view their own profile',
      'Users can update their own profile'
    ];

    for (const policy of policiesToDrop) {
      const { error } = await supabase.rpc('exec_sql', {
        sql: `DROP POLICY IF EXISTS "${policy}" ON profiles;`
      });
      if (error && !error.message.includes('does not exist')) {
        console.error(`Error dropping ${policy}:`, error);
      } else {
        console.log(`✅ Dropped policy: ${policy}`);
      }
    }

    // Step 2: Create new working policies
    console.log('\n2. Creating new working policies...');

    const newPolicies = [
      {
        name: 'profiles_service_role_full_access',
        sql: `CREATE POLICY "profiles_service_role_full_access" ON profiles
              FOR ALL
              USING (auth.role() = 'service_role')
              WITH CHECK (auth.role() = 'service_role');`
      },
      {
        name: 'profiles_authenticated_own_access',
        sql: `CREATE POLICY "profiles_authenticated_own_access" ON profiles
              FOR SELECT
              USING (auth.uid() IS NOT NULL AND auth.uid() = id);`
      },
      {
        name: 'profiles_authenticated_own_update',
        sql: `CREATE POLICY "profiles_authenticated_own_update" ON profiles
              FOR UPDATE
              USING (auth.uid() IS NOT NULL AND auth.uid() = id)
              WITH CHECK (auth.uid() IS NOT NULL AND auth.uid() = id);`
      },
      {
        name: 'profiles_authenticated_insert',
        sql: `CREATE POLICY "profiles_authenticated_insert" ON profiles
              FOR INSERT
              WITH CHECK (auth.uid() IS NOT NULL AND auth.uid() = id);`
      },
      {
        name: 'profiles_consumer_anonymous_signup',
        sql: `CREATE POLICY "profiles_consumer_anonymous_signup" ON profiles
              FOR INSERT
              WITH CHECK (
                role = 'consumer'
                AND auth.uid() IS NULL
                AND phone IS NOT NULL
                AND full_name IS NOT NULL
              );`
      },
      {
        name: 'profiles_consumer_anonymous_login',
        sql: `CREATE POLICY "profiles_consumer_anonymous_login" ON profiles
              FOR SELECT
              USING (
                role = 'consumer'
                AND auth.uid() IS NULL
              );`
      }
    ];

    for (const policy of newPolicies) {
      const { error } = await supabase.rpc('exec_sql', { sql: policy.sql });
      if (error) {
        console.error(`Error creating ${policy.name}:`, error);
      } else {
        console.log(`✅ Created policy: ${policy.name}`);
      }
    }

    // Step 3: Test profile access
    console.log('\n3. Testing profile access...');
    
    const { data: profiles, error: profilesError } = await supabase
      .from('profiles')
      .select('id, email, role, full_name')
      .eq('role', 'supplier')
      .limit(3);

    if (profilesError) {
      console.error('❌ Profile access test failed:', profilesError);
    } else {
      console.log('✅ Profile access test passed:', profiles.length, 'profiles found');
    }

    console.log('\n🎉 Emergency fix applied successfully!');
    console.log('👉 Please test admin signup and login now.');

  } catch (error) {
    console.error('❌ Emergency fix failed:', error);
  }
}

applyEmergencyFix();
