import { createClient } from '@supabase/supabase-js';

// Supabase configuration
const supabaseUrl = 'https://irkwpzcskeqtasutqnxp.supabase.co';
const supabaseServiceKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imlya3dwemNza2VxdGFzdXRxbnhwIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0NzQ5ODY4MywiZXhwIjoyMDYzMDc0NjgzfQ.PLfCCbh4zgAJDDC6sS82fCU32JYA7e2VmwzvavF-PW8';
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imlya3dwemNza2VxdGFzdXRxbnhwIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDc0OTg2ODMsImV4cCI6MjA2MzA3NDY4M30.bUv9zxx9q_sVUwv_-G6WbHsA53-SU5tCCWbOS_5Xp94';

const supabaseService = createClient(supabaseUrl, supabaseServiceKey);
const supabaseAnon = createClient(supabaseUrl, supabaseAnonKey);

async function verifyReviewsFix() {
  try {
    console.log('🧪 VERIFYING REVIEWS FIX...');
    console.log('='.repeat(50));

    let allTestsPassed = true;

    // CRITICAL TEST 1: Verify admin authentication still works
    console.log('\n🔒 CRITICAL TEST 1: Admin authentication');
    const { data: adminTest, error: adminError } = await supabaseService
      .from('profiles')
      .select('id, email, role')
      .eq('role', 'supplier')
      .limit(1);

    if (adminError) {
      console.error('❌ CRITICAL FAILURE: Admin auth broken!', adminError);
      allTestsPassed = false;
    } else {
      console.log('✅ Admin authentication: WORKING');
    }

    // CRITICAL TEST 2: Verify consumer profiles still work
    console.log('\n👤 CRITICAL TEST 2: Consumer profiles');
    const { data: consumerTest, error: consumerError } = await supabaseService
      .from('profiles')
      .select('id, phone, role')
      .eq('role', 'consumer')
      .limit(1);

    if (consumerError) {
      console.error('❌ CRITICAL FAILURE: Consumer profiles broken!', consumerError);
      allTestsPassed = false;
    } else {
      console.log('✅ Consumer profiles: WORKING');
    }

    // CRITICAL TEST 3: Verify wishlist still works
    console.log('\n💝 CRITICAL TEST 3: Wishlist functionality');
    const { data: wishlistTest, error: wishlistError } = await supabaseAnon
      .from('consumer_wishlists')
      .select('*')
      .limit(1);

    if (wishlistError) {
      console.error('❌ CRITICAL FAILURE: Wishlist broken!', wishlistError);
      allTestsPassed = false;
    } else {
      console.log('✅ Wishlist functionality: WORKING');
    }

    // TEST 4: Verify reviews anonymous SELECT works
    console.log('\n📖 TEST 4: Reviews anonymous SELECT');
    const { data: selectTest, error: selectError } = await supabaseAnon
      .from('consumer_reviews')
      .select('*')
      .eq('is_approved', true)
      .limit(1);

    if (selectError) {
      console.error('❌ Reviews SELECT failed:', selectError.message);
      allTestsPassed = false;
    } else {
      console.log('✅ Reviews SELECT: WORKING');
    }

    // TEST 5: Verify reviews anonymous INSERT works (THE MAIN FIX)
    console.log('\n➕ TEST 5: Reviews anonymous INSERT (THE MAIN FIX)');
    const testPhone = '+213555888999';
    const startTime = Date.now();
    
    const { data: insertTest, error: insertError } = await supabaseAnon
      .from('consumer_reviews')
      .insert({
        consumer_phone: testPhone,
        product_id: 'VERIFY-REVIEWS-FIX-001',
        product_name: 'Verification Test Product',
        rating: 5,
        title: 'Verification Test Review',
        review_text: 'This is a verification test review to ensure the fix works'
      })
      .select()
      .single();

    const endTime = Date.now();
    const duration = endTime - startTime;

    if (insertError) {
      console.error('❌ REVIEWS INSERT FAILED:', insertError.message);
      console.error('❌ THE FIX DID NOT WORK');
      allTestsPassed = false;
    } else {
      console.log('✅ Reviews INSERT: WORKING! 🎉');
      console.log(`✅ Insert duration: ${duration}ms (should be fast now!)`);
      console.log('✅ THE FIX IS SUCCESSFUL! 🎉');

      // TEST 6: Verify UPDATE works
      console.log('\n✏️ TEST 6: Reviews anonymous UPDATE');
      const { data: updateTest, error: updateError } = await supabaseAnon
        .from('consumer_reviews')
        .update({ rating: 4, title: 'Updated Test Review' })
        .eq('product_id', 'VERIFY-REVIEWS-FIX-001')
        .select()
        .single();

      if (updateError) {
        console.error('❌ Reviews UPDATE failed:', updateError.message);
        allTestsPassed = false;
      } else {
        console.log('✅ Reviews UPDATE: WORKING');
      }

      // TEST 7: Verify reading the review works
      console.log('\n📖 TEST 7: Reading the new review');
      const { data: readTest, error: readError } = await supabaseAnon
        .from('consumer_reviews')
        .select('*')
        .eq('product_id', 'VERIFY-REVIEWS-FIX-001');

      if (readError) {
        console.error('❌ Reviews READ failed:', readError.message);
        allTestsPassed = false;
      } else {
        console.log('✅ Reviews READ: WORKING');
        console.log('✅ Found review:', readTest[0]?.title);
      }

      // TEST 8: Verify DELETE works
      console.log('\n🗑️ TEST 8: Reviews anonymous DELETE');
      const { error: deleteError } = await supabaseAnon
        .from('consumer_reviews')
        .delete()
        .eq('product_id', 'VERIFY-REVIEWS-FIX-001');

      if (deleteError) {
        console.error('❌ Reviews DELETE failed:', deleteError.message);
        allTestsPassed = false;
      } else {
        console.log('✅ Reviews DELETE: WORKING');
      }
    }

    // FINAL RESULTS
    console.log('\n' + '='.repeat(50));
    console.log('🎯 VERIFICATION RESULTS:');
    console.log('='.repeat(50));

    if (allTestsPassed) {
      console.log('🎉 ALL TESTS PASSED! 🎉');
      console.log('✅ Admin authentication: WORKING');
      console.log('✅ Consumer profiles: WORKING');
      console.log('✅ Wishlist functionality: WORKING');
      console.log('✅ Reviews SELECT: WORKING');
      console.log('✅ Reviews INSERT: WORKING');
      console.log('✅ Reviews UPDATE: WORKING');
      console.log('✅ Reviews DELETE: WORKING');
      console.log('');
      console.log('🚀 REVIEWS FUNCTIONALITY IS NOW FULLY RESTORED!');
      console.log('⚡ Reviews should now submit quickly without long waits');
      console.log('🛡️ All existing authentication systems remain intact');
      console.log('');
      console.log('📱 You can now test the reviews functionality:');
      console.log('   1. Go to http://localhost:8080');
      console.log('   2. Navigate to any product page');
      console.log('   3. Authenticate as a consumer with phone');
      console.log('   4. Click "Write a Review" button');
      console.log('   5. Submit a review - it should work instantly!');
    } else {
      console.log('❌ SOME TESTS FAILED');
      console.log('🚨 Please check the errors above');
      console.log('🔧 The fix may need additional adjustments');
    }

  } catch (error) {
    console.error('❌ Verification failed:', error);
  }
}

// Run verification
verifyReviewsFix();
