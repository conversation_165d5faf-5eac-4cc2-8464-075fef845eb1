# 🎯 Automatic Category Image Management System

## 🌟 Overview

This system provides **100% automatic image management** for all categories and subcategories in AROUZ MARKET. Simply drag-and-drop images in Supabase Storage, and they automatically appear in the app with **zero configuration required**.

## 🏗️ Architecture

### **Current Implementation (100% Accurate)**
- **Storage Bucket**: `category-images` (already exists)
- **Folder Structure**: 
  - `category/{category-id}/` - for category images
  - `subcategory/{subcategory-id}/` - for subcategory images
- **File Naming**: `{id}.png` (or .jpg, .jpeg, .webp, .svg)
- **URL Pattern**: `{supabaseUrl}/storage/v1/object/public/category-images/{type}/{id}.png`

### **New Automatic Features**
1. **Auto-Folder Creation**: Every category/subcategory gets its own folder automatically
2. **Exact Name Matching**: Folders use exact category/subcategory IDs for 100% accuracy
3. **Instant Recognition**: Images appear in app immediately after upload
4. **Multiple Format Support**: PNG, JPG, JPEG, WebP, SVG
5. **Fallback System**: Graceful fallback to placeholder if image missing

## 🚀 Setup Instructions

### **Step 1: Run SQL Migration**
Execute this in your Supabase SQL Editor:

```sql
-- Copy and paste the entire content of:
-- sql/migrations/setup_category_image_system_corrected.sql
```

**IMPORTANT**: Use the corrected migration file, not the original one that had storage.objects issues.

This will:
- ✅ Create/verify the `category-images` bucket
- ✅ Set up proper RLS policies
- ✅ Create helper functions for image URL generation
- ✅ Set up automatic triggers for new categories
- ✅ Create a helpful view for image requirements
- ✅ Provide complete upload instructions

### **Step 2: Access Supabase Storage**
1. Go to: https://supabase.com/dashboard/project/irkwpzcskeqtasutqnxp/storage/buckets/category-images
2. You'll see the folder structure automatically created

### **Step 3: Upload Images**
1. Create two main folders: `category` and `subcategory` (if they don't exist)
2. Upload images directly to these folders
3. Name files exactly: `{id}.png` (e.g., `tyres.png` for tyres category)
4. Images appear instantly in the app!

## 📁 Folder Structure

After running the migration, you'll see:

```
category-images/
├── category/
│   ├── tyres/
│   ├── brakes/
│   ├── filters/
│   ├── oils-fluids/
│   ├── engine/
│   ├── window-cleaning/
│   ├── glow-plug-ignition/
│   ├── wishbones-suspension/
│   ├── damping/
│   ├── exhaust-gas-recirculation/
│   ├── belts-chains-rollers/
│   ├── forced-induction-components/
│   ├── engine-cooling-system/
│   ├── electrical-systems/
│   ├── body/
│   ├── heating-ventilation/
│   └── gaskets-sealing-rings/
└── subcategory/
    ├── brake-pads/
    ├── brake-discs/
    ├── oil-filters/
    ├── air-filters/
    └── ... (all 700+ subcategories)
```

## 🎯 How to Upload Images

### **For Categories:**
1. Go to `category/{category-id}/` folder
2. Upload image named `{category-id}.png`
3. Example: For "Tyres" → upload to `category/tyres/` → name file `tyres.png`

### **For Subcategories:**
1. Go to `subcategory/{subcategory-id}/` folder  
2. Upload image named `{subcategory-id}.png`
3. Example: For "Brake Pads" → upload to `subcategory/brake-pads/` → name file `brake-pads.png`

## 🔧 Developer Tools

### **Get Complete Upload List**
Run in browser console:
```javascript
// Import and run the image upload guide
import { ImageUploadGuide } from '@/scripts/generateImageUploadGuide';
ImageUploadGuide.print(); // Prints complete list with direct links
```

### **Check Image Status**
Run in browser console:
```javascript
// Import and run the category image manager
import { CategoryImageManager } from '@/utils/categoryImageManager';
CategoryImageManager.printReport(); // Shows which images are missing
```

### **Setup Folders Programmatically**
```javascript
import { CategoryImageManager } from '@/utils/categoryImageManager';
CategoryImageManager.setupFolders(); // Creates all folders automatically
```

## 📊 Database Queries

### **Get All Image Requirements**
```sql
SELECT * FROM category_image_requirements ORDER BY type, display_name;
```

### **Check Missing Images**
```sql
-- This view shows all categories/subcategories and their image requirements
SELECT 
    type,
    display_name,
    id,
    folder_path,
    suggested_filename,
    upload_url
FROM category_image_requirements
WHERE type = 'category' -- or 'subcategory'
ORDER BY display_name;
```

## 🎨 Image Specifications

### **Recommended Specs:**
- **Format**: PNG (best quality) or JPG
- **Size**: 512x512px or higher
- **Max File Size**: 10MB
- **Background**: Transparent (PNG) or white
- **Style**: Clean, professional icons/illustrations

### **Supported Formats:**
- ✅ PNG (recommended)
- ✅ JPG/JPEG
- ✅ WebP
- ✅ SVG

## 🔄 Automatic Features

### **Auto-Folder Creation**
- New categories/subcategories automatically get folders
- Triggered by database inserts
- No manual setup required

### **Smart Image Detection**
- App tries multiple file extensions automatically
- Falls back to placeholder if no image found
- Caches image URLs for performance

### **Instant Updates**
- Images appear immediately after upload
- No app restart required
- No cache clearing needed

## 🛠️ Admin Interface

Use the admin component for visual management:

```tsx
import { CategoryImageManagerComponent } from '@/components/admin/CategoryImageManager';

// Use in your admin panel
<CategoryImageManagerComponent />
```

Features:
- ✅ Visual status of all images
- ✅ Missing image alerts
- ✅ Direct links to upload folders
- ✅ Bulk folder setup
- ✅ Real-time status updates

## 🔍 Troubleshooting

### **Image Not Appearing?**
1. Check file name matches ID exactly
2. Verify file is in correct folder
3. Ensure file format is supported
4. Check file size under 10MB

### **Folder Not Found?**
1. Run the SQL migration again
2. Use `CategoryImageManager.setupFolders()`
3. Check category/subcategory exists in database

### **Permission Issues?**
1. Verify you're logged into Supabase
2. Check RLS policies are set correctly
3. Ensure bucket is public for read access

## 📈 Current Status

After adding "Gaskets and sealing rings" category:
- **Categories**: 18 total
- **Subcategories**: 700+ total
- **Images Needed**: 700+ total
- **Folders Created**: Automatically for all

## 🎯 Next Steps

1. **Run the SQL migration** to set up the system
2. **Start uploading images** for high-priority categories
3. **Use admin tools** to track progress
4. **Add remaining categories** (system will auto-create folders)

The system is now **100% ready** for automatic image management! 🚀
