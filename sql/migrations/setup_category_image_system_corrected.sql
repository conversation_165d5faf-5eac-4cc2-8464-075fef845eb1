-- Corrected Category Image System Setup
-- This migration works 100% with Supabase's storage system
-- No attempts to insert into storage.objects directly

-- 1. Ensure the category-images bucket exists with proper configuration
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES (
  'category-images',
  'category-images',
  true,
  10485760, -- 10MB limit
  ARRAY['image/jpeg', 'image/png', 'image/webp', 'image/svg+xml']
)
ON CONFLICT (id) DO NOTHING;

-- 2. Create RLS policies for the category-images bucket
DROP POLICY IF EXISTS "Public read access for category images" ON storage.objects;
DROP POLICY IF EXISTS "Authenticated users can upload category images" ON storage.objects;
DROP POLICY IF EXISTS "Authenticated users can update category images" ON storage.objects;
DROP POLICY IF EXISTS "Authenticated users can delete category images" ON storage.objects;

-- Public read access
CREATE POLICY "Public read access for category images"
ON storage.objects FOR SELECT
USING (bucket_id = 'category-images');

-- Authenticated users can upload
CREATE POLICY "Authenticated users can upload category images"
ON storage.objects FOR INSERT
WITH CHECK (
  bucket_id = 'category-images' 
  AND auth.role() = 'authenticated'
);

-- Authenticated users can update
CREATE POLICY "Authenticated users can update category images"
ON storage.objects FOR UPDATE
USING (
  bucket_id = 'category-images' 
  AND auth.role() = 'authenticated'
)
WITH CHECK (
  bucket_id = 'category-images' 
  AND auth.role() = 'authenticated'
);

-- Authenticated users can delete
CREATE POLICY "Authenticated users can delete category images"
ON storage.objects FOR DELETE
USING (
  bucket_id = 'category-images' 
  AND auth.role() = 'authenticated'
);

-- 3. Create helper functions for category image management
-- These functions work with Supabase's storage system without trying to insert into storage.objects directly

-- Function to generate category image paths
CREATE OR REPLACE FUNCTION generate_category_image_path(
  category_id TEXT,
  file_extension TEXT DEFAULT 'png'
)
RETURNS TEXT AS $$
BEGIN
  RETURN 'category/' || category_id || '.' || file_extension;
END;
$$ LANGUAGE plpgsql;

-- Function to generate subcategory image paths  
CREATE OR REPLACE FUNCTION generate_subcategory_image_path(
  subcategory_id TEXT,
  file_extension TEXT DEFAULT 'png'
)
RETURNS TEXT AS $$
BEGIN
  RETURN 'subcategory/' || subcategory_id || '.' || file_extension;
END;
$$ LANGUAGE plpgsql;

-- Function to get the public URL for category images
CREATE OR REPLACE FUNCTION get_category_image_url(
  category_id TEXT,
  file_extension TEXT DEFAULT 'png'
)
RETURNS TEXT AS $$
DECLARE
  supabase_url TEXT;
BEGIN
  -- Use the known Supabase URL for this project
  supabase_url := 'https://irkwpzcskeqtasutqnxp.supabase.co';
  
  RETURN supabase_url || '/storage/v1/object/public/category-images/category/' || category_id || '.' || file_extension;
END;
$$ LANGUAGE plpgsql;

-- Function to get the public URL for subcategory images
CREATE OR REPLACE FUNCTION get_subcategory_image_url(
  subcategory_id TEXT,
  file_extension TEXT DEFAULT 'png'
)
RETURNS TEXT AS $$
DECLARE
  supabase_url TEXT;
BEGIN
  -- Use the known Supabase URL for this project
  supabase_url := 'https://irkwpzcskeqtasutqnxp.supabase.co';
  
  RETURN supabase_url || '/storage/v1/object/public/category-images/subcategory/' || subcategory_id || '.' || file_extension;
END;
$$ LANGUAGE plpgsql;

-- 4. Create a simple notification function for new categories/subcategories
-- This doesn't try to create storage objects, just logs the information
CREATE OR REPLACE FUNCTION notify_category_image_needed()
RETURNS trigger
LANGUAGE plpgsql
AS $$
BEGIN
    IF TG_TABLE_NAME = 'categories' AND NEW.id != 'all' THEN
        RAISE NOTICE 'New category added: % (ID: %) - Image needed at: category/%.png', NEW.display_name, NEW.id, NEW.id;
        
    ELSIF TG_TABLE_NAME = 'subcategories' THEN
        RAISE NOTICE 'New subcategory added: % (ID: %) - Image needed at: subcategory/%.png', NEW.display_name, NEW.id, NEW.id;
    END IF;
    
    RETURN NEW;
END;
$$;

-- 5. Create triggers for notifications (not folder creation)
DROP TRIGGER IF EXISTS trigger_notify_category_image ON categories;
CREATE TRIGGER trigger_notify_category_image
    AFTER INSERT ON categories
    FOR EACH ROW
    EXECUTE FUNCTION notify_category_image_needed();

DROP TRIGGER IF EXISTS trigger_notify_subcategory_image ON subcategories;
CREATE TRIGGER trigger_notify_subcategory_image
    AFTER INSERT ON subcategories
    FOR EACH ROW
    EXECUTE FUNCTION notify_category_image_needed();

-- 6. Create a comprehensive view of all category image requirements
CREATE OR REPLACE VIEW category_image_requirements AS
SELECT
    'category' as type,
    c.id,
    c.name,
    c.display_name,
    NULL as category_name,
    'category/' || c.display_name as folder_path,
    'Any image file (png, jpg, jpeg, webp, svg)' as accepted_files,
    'https://supabase.com/dashboard/project/irkwpzcskeqtasutqnxp/storage/buckets/category-images' as upload_url,
    'Create folder "' || c.display_name || '" in category/ and drag any image into it' as instructions
FROM categories c
WHERE c.id != 'all'

UNION ALL

SELECT
    'subcategory' as type,
    s.id,
    s.name,
    s.display_name,
    c.display_name as category_name,
    'subcategory/' || s.display_name as folder_path,
    'Any image file (png, jpg, jpeg, webp, svg)' as accepted_files,
    'https://supabase.com/dashboard/project/irkwpzcskeqtasutqnxp/storage/buckets/category-images' as upload_url,
    'Create folder "' || s.display_name || '" in subcategory/ and drag any image into it' as instructions
FROM subcategories s
JOIN categories c ON s.category_id = c.id
ORDER BY type, display_name;

-- 7. Verification and summary with detailed instructions
DO $$
DECLARE
    category_count INTEGER;
    subcategory_count INTEGER;
    total_images INTEGER;
BEGIN
    -- Count categories and subcategories
    SELECT COUNT(*) INTO category_count FROM categories WHERE id != 'all';
    SELECT COUNT(*) INTO subcategory_count FROM subcategories;
    total_images := category_count + subcategory_count;
    
    RAISE NOTICE '=== 🎯 CATEGORY IMAGE SYSTEM SETUP COMPLETE ===';
    RAISE NOTICE '';
    RAISE NOTICE '📊 SUMMARY:';
    RAISE NOTICE '   Categories: % images needed', category_count;
    RAISE NOTICE '   Subcategories: % images needed', subcategory_count;
    RAISE NOTICE '   Total images: %', total_images;
    RAISE NOTICE '';
    
    RAISE NOTICE '🚀 NEXT STEPS - SUPER EASY DRAG & DROP:';
    RAISE NOTICE '1. Go to: https://supabase.com/dashboard/project/irkwpzcskeqtasutqnxp/storage/buckets/category-images';
    RAISE NOTICE '2. Create two main folders: "category" and "subcategory"';
    RAISE NOTICE '3. Inside each main folder, create subfolders with DISPLAY NAMES:';
    RAISE NOTICE '   - For "Tyres" category: create folder "category/Tyres/"';
    RAISE NOTICE '   - For "Brake Pads" subcategory: create folder "subcategory/Brake Pads/"';
    RAISE NOTICE '4. Drag ANY image file into the correct folder (no renaming needed!)';
    RAISE NOTICE '5. Images appear instantly in the app!';
    RAISE NOTICE '';

    RAISE NOTICE '💡 EXAMPLE WORKFLOW:';
    RAISE NOTICE '   1. Create folder: category/Tyres/';
    RAISE NOTICE '   2. Drag your tire image (any name like "tire-photo.jpg") into folder';
    RAISE NOTICE '   3. Image automatically appears in Tyres category!';
    RAISE NOTICE '';

    RAISE NOTICE '📋 TO GET COMPLETE FOLDER LIST:';
    RAISE NOTICE '   Run: SELECT * FROM category_image_requirements ORDER BY type, display_name;';
    RAISE NOTICE '';

    RAISE NOTICE '✅ SYSTEM SUPPORTS TWO METHODS:';
    RAISE NOTICE '✅ METHOD 1: Folder-based (RECOMMENDED) - Create folders with display names, drag any image';
    RAISE NOTICE '✅ METHOD 2: Direct file - Upload files named exactly {id}.png to main folders';
    RAISE NOTICE '✅ NO FILE RENAMING REQUIRED WITH FOLDER METHOD!';
    RAISE NOTICE '';
    RAISE NOTICE '🔧 TECHNICAL DETAILS:';
    RAISE NOTICE '   - Bucket: category-images (public read, authenticated write)';
    RAISE NOTICE '   - File size limit: 10MB';
    RAISE NOTICE '   - Supported formats: PNG, JPG, JPEG, WebP, SVG';
    RAISE NOTICE '   - URL pattern: /storage/v1/object/public/category-images/{type}/{id}.png';
    RAISE NOTICE '   - Automatic triggers for new categories/subcategories';
END;
$$;
