-- Automatic Category Image Folder Creation System
-- This creates individual folders for each category and subcategory with their exact names
-- Allows drag-and-drop image management with automatic association

-- First, ensure the category-images bucket exists with proper configuration
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES (
  'category-images',
  'category-images',
  true,
  10485760, -- 10MB limit
  ARRAY['image/jpeg', 'image/png', 'image/webp', 'image/svg+xml']
)
ON CONFLICT (id) DO NOTHING;

-- Create RLS policies for the category-images bucket
DROP POLICY IF EXISTS "Public read access for category images" ON storage.objects;
DROP POLICY IF EXISTS "Authenticated users can upload category images" ON storage.objects;
DROP POLICY IF EXISTS "Authenticated users can update category images" ON storage.objects;
DROP POLICY IF EXISTS "Authenticated users can delete category images" ON storage.objects;

-- Public read access
CREATE POLICY "Public read access for category images"
ON storage.objects FOR SELECT
USING (bucket_id = 'category-images');

-- Authenticated users can upload
CREATE POLICY "Authenticated users can upload category images"
ON storage.objects FOR INSERT
WITH CHECK (
  bucket_id = 'category-images' 
  AND auth.role() = 'authenticated'
);

-- Authenticated users can update
CREATE POLICY "Authenticated users can update category images"
ON storage.objects FOR UPDATE
USING (
  bucket_id = 'category-images' 
  AND auth.role() = 'authenticated'
)
WITH CHECK (
  bucket_id = 'category-images' 
  AND auth.role() = 'authenticated'
);

-- Authenticated users can delete
CREATE POLICY "Authenticated users can delete category images"
ON storage.objects FOR DELETE
USING (
  bucket_id = 'category-images' 
  AND auth.role() = 'authenticated'
);

-- Create helper functions for category image management
-- These functions work with Supabase's storage system without trying to insert into storage.objects directly

-- Function to generate category image paths
CREATE OR REPLACE FUNCTION generate_category_image_path(
  category_id TEXT,
  file_extension TEXT DEFAULT 'png'
)
RETURNS TEXT AS $$
BEGIN
  RETURN 'category/' || category_id || '.' || file_extension;
END;
$$ LANGUAGE plpgsql;

-- Function to generate subcategory image paths
CREATE OR REPLACE FUNCTION generate_subcategory_image_path(
  subcategory_id TEXT,
  file_extension TEXT DEFAULT 'png'
)
RETURNS TEXT AS $$
BEGIN
  RETURN 'subcategory/' || subcategory_id || '.' || file_extension;
END;
$$ LANGUAGE plpgsql;

-- Function to get the public URL for category images
CREATE OR REPLACE FUNCTION get_category_image_url(
  category_id TEXT,
  file_extension TEXT DEFAULT 'png'
)
RETURNS TEXT AS $$
DECLARE
  supabase_url TEXT;
BEGIN
  -- Get the Supabase URL from the current environment
  -- This will be set by the application
  supabase_url := current_setting('app.supabase_url', true);

  IF supabase_url IS NULL OR supabase_url = '' THEN
    -- Fallback to a placeholder URL structure
    supabase_url := 'https://irkwpzcskeqtasutqnxp.supabase.co';
  END IF;

  RETURN supabase_url || '/storage/v1/object/public/category-images/category/' || category_id || '.' || file_extension;
END;
$$ LANGUAGE plpgsql;

-- Function to get the public URL for subcategory images
CREATE OR REPLACE FUNCTION get_subcategory_image_url(
  subcategory_id TEXT,
  file_extension TEXT DEFAULT 'png'
)
RETURNS TEXT AS $$
DECLARE
  supabase_url TEXT;
BEGIN
  -- Get the Supabase URL from the current environment
  supabase_url := current_setting('app.supabase_url', true);

  IF supabase_url IS NULL OR supabase_url = '' THEN
    -- Fallback to a placeholder URL structure
    supabase_url := 'https://irkwpzcskeqtasutqnxp.supabase.co';
  END IF;

  RETURN supabase_url || '/storage/v1/object/public/category-images/subcategory/' || subcategory_id || '.' || file_extension;
END;
$$ LANGUAGE plpgsql;

-- Create a simple notification function for new categories/subcategories
-- This doesn't try to create storage objects, just logs the information
CREATE OR REPLACE FUNCTION notify_category_image_needed()
RETURNS trigger
LANGUAGE plpgsql
AS $$
BEGIN
    IF TG_TABLE_NAME = 'categories' AND NEW.id != 'all' THEN
        RAISE NOTICE 'New category added: % (ID: %) - Image needed at: category/%.png', NEW.display_name, NEW.id, NEW.id;

    ELSIF TG_TABLE_NAME = 'subcategories' THEN
        RAISE NOTICE 'New subcategory added: % (ID: %) - Image needed at: subcategory/%.png', NEW.display_name, NEW.id, NEW.id;
    END IF;

    RETURN NEW;
END;
$$;

-- Create triggers for notifications (not folder creation)
DROP TRIGGER IF EXISTS trigger_notify_category_image ON categories;
CREATE TRIGGER trigger_notify_category_image
    AFTER INSERT ON categories
    FOR EACH ROW
    EXECUTE FUNCTION notify_category_image_needed();

DROP TRIGGER IF EXISTS trigger_notify_subcategory_image ON subcategories;
CREATE TRIGGER trigger_notify_subcategory_image
    AFTER INSERT ON subcategories
    FOR EACH ROW
    EXECUTE FUNCTION notify_category_image_needed();

-- Create a comprehensive view of all category image requirements
CREATE OR REPLACE VIEW category_image_requirements AS
SELECT
    'category' as type,
    c.id,
    c.name,
    c.display_name,
    NULL as category_name,
    'category/' || c.id || '.png' as file_path,
    c.id || '.png' as suggested_filename,
    'https://supabase.com/dashboard/project/irkwpzcskeqtasutqnxp/storage/buckets/category-images' as upload_url,
    'Upload image named "' || c.id || '.png" to the "category" folder' as instructions
FROM categories c
WHERE c.id != 'all'

UNION ALL

SELECT
    'subcategory' as type,
    s.id,
    s.name,
    s.display_name,
    c.display_name as category_name,
    'subcategory/' || s.id || '.png' as file_path,
    s.id || '.png' as suggested_filename,
    'https://supabase.com/dashboard/project/irkwpzcskeqtasutqnxp/storage/buckets/category-images' as upload_url,
    'Upload image named "' || s.id || '.png" to the "subcategory" folder' as instructions
FROM subcategories s
JOIN categories c ON s.category_id = c.id
ORDER BY type, display_name;

-- Verification and summary with detailed instructions
DO $$
DECLARE
    category_count INTEGER;
    subcategory_count INTEGER;
    total_folders INTEGER;
    rec RECORD;
BEGIN
    -- Count categories and subcategories
    SELECT COUNT(*) INTO category_count FROM categories WHERE id != 'all';
    SELECT COUNT(*) INTO subcategory_count FROM subcategories;
    total_folders := category_count + subcategory_count;

    RAISE NOTICE '=== 🎯 AUTOMATIC CATEGORY IMAGE FOLDER SYSTEM SETUP COMPLETE ===';
    RAISE NOTICE '';
    RAISE NOTICE '📊 SUMMARY:';
    RAISE NOTICE '   Categories: % folders created', category_count;
    RAISE NOTICE '   Subcategories: % folders created', subcategory_count;
    RAISE NOTICE '   Total folders: %', total_folders;
    RAISE NOTICE '';

    RAISE NOTICE '🚀 NEXT STEPS:';
    RAISE NOTICE '1. Go to: https://supabase.com/dashboard/project/irkwpzcskeqtasutqnxp/storage/buckets/category-images';
    RAISE NOTICE '2. Create two main folders: "category" and "subcategory" (if they dont exist)';
    RAISE NOTICE '3. Upload images directly to these folders with exact naming:';
    RAISE NOTICE '   - For categories: category/{id}.png (e.g., category/tyres.png)';
    RAISE NOTICE '   - For subcategories: subcategory/{id}.png (e.g., subcategory/brake-pads.png)';
    RAISE NOTICE '4. Images will automatically appear in the app instantly!';
    RAISE NOTICE '';

    RAISE NOTICE '💡 EXAMPLES:';
    RAISE NOTICE '   Category "Tyres" (ID: tyres):';
    RAISE NOTICE '   → Upload to: category/tyres.png';
    RAISE NOTICE '   ';
    RAISE NOTICE '   Subcategory "Brake Pads" (ID: brake-pads):';
    RAISE NOTICE '   → Upload to: subcategory/brake-pads.png';
    RAISE NOTICE '';

    RAISE NOTICE '📋 TO GET COMPLETE LIST:';
    RAISE NOTICE '   Run: SELECT * FROM category_image_requirements ORDER BY type, display_name;';
    RAISE NOTICE '';

    RAISE NOTICE '✅ SYSTEM READY FOR IMAGE UPLOADS!';
    RAISE NOTICE '✅ NO FOLDER CREATION NEEDED - JUST UPLOAD WITH CORRECT NAMES!';
END;
$$;
