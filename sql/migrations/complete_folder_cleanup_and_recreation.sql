-- COMPLETE CATEGORY FOLDER CLEANUP AND RECREATION
-- 1. Delete ALL old loose images
-- 2. Create folders for ALL categories/subcategories from Supabase tables
-- 3. Ensure 100% accuracy with current database data

-- =====================================================
-- STEP 1: CLEAN UP ALL OLD LOOSE IMAGES
-- =====================================================

-- Delete all loose images (not in folders) from category-images bucket
DELETE FROM storage.objects 
WHERE bucket_id = 'category-images' 
AND name NOT LIKE '%/%'  -- Delete files not in folders
AND name NOT LIKE '%.keep'; -- Keep placeholder files

-- Delete old loose category images
DELETE FROM storage.objects 
WHERE bucket_id = 'category-images' 
AND (
  name LIKE 'category/%.png' OR
  name LIKE 'category/%.jpg' OR
  name LIKE 'category/%.jpeg' OR
  name LIKE 'category/%.webp' OR
  name LIKE 'category/%.svg'
) 
AND name NOT LIKE 'category/%/%'; -- Keep folder-based images

-- Delete old loose subcategory images
DELETE FROM storage.objects 
WHERE bucket_id = 'category-images' 
AND (
  name LIKE 'subcategory/%.png' OR
  name LIKE 'subcategory/%.jpg' OR
  name LIKE 'subcategory/%.jpeg' OR
  name LIKE 'subcategory/%.webp' OR
  name LIKE 'subcategory/%.svg'
) 
AND name NOT LIKE 'subcategory/%/%'; -- Keep folder-based images

-- =====================================================
-- STEP 2: CREATE FOLDERS FROM ACTUAL SUPABASE DATA
-- =====================================================

-- Function to create folders using REAL Supabase data
CREATE OR REPLACE FUNCTION create_folders_from_supabase_data()
RETURNS TABLE(
  folder_type text,
  folder_display_name text,
  folder_path text,
  folder_status text
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    category_record RECORD;
    subcategory_record RECORD;
    placeholder_data bytea;
    total_created INTEGER := 0;
    total_skipped INTEGER := 0;
BEGIN
    -- Create minimal placeholder file (1x1 transparent PNG)
    placeholder_data := decode('iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChAGA6VP8IQAAAABJRU5ErkJggg==', 'base64');
    
    RAISE NOTICE '🧹 CLEANING UP OLD IMAGES AND CREATING FRESH FOLDER STRUCTURE...';
    RAISE NOTICE '';
    
    -- =====================================================
    -- CREATE CATEGORY FOLDERS FROM SUPABASE CATEGORIES TABLE
    -- =====================================================
    
    RAISE NOTICE '📁 Creating category folders from Supabase categories table...';
    
    FOR category_record IN 
        SELECT id, name, display_name, description, id_prefix, sort_order
        FROM categories 
        WHERE is_active = true 
        AND id != 'all'  -- Skip 'all' category
        ORDER BY sort_order, display_name
    LOOP
        BEGIN
            -- Insert placeholder file to create folder
            INSERT INTO storage.objects (bucket_id, name, owner, metadata)
            VALUES (
                'category-images',
                'category/' || category_record.display_name || '/.keep',
                (SELECT auth.uid()),
                jsonb_build_object(
                    'size', length(placeholder_data),
                    'mimetype', 'image/png',
                    'category_id', category_record.id,
                    'category_name', category_record.name,
                    'display_name', category_record.display_name,
                    'description', category_record.description,
                    'id_prefix', category_record.id_prefix,
                    'sort_order', category_record.sort_order,
                    'type', 'category_folder_placeholder',
                    'created_from', 'supabase_categories_table'
                )
            )
            ON CONFLICT (bucket_id, name) DO NOTHING;
            
            -- Check if it was actually inserted
            IF FOUND THEN
                total_created := total_created + 1;
                folder_type := 'category';
                folder_display_name := category_record.display_name;
                folder_path := 'category/' || category_record.display_name || '/';
                folder_status := 'created';
                RETURN NEXT;

                RAISE NOTICE '✅ Created: category/%', category_record.display_name;
            ELSE
                total_skipped := total_skipped + 1;
                folder_type := 'category';
                folder_display_name := category_record.display_name;
                folder_path := 'category/' || category_record.display_name || '/';
                folder_status := 'already_exists';
                RETURN NEXT;

                RAISE NOTICE '📂 Exists: category/%', category_record.display_name;
            END IF;

        EXCEPTION WHEN OTHERS THEN
            RAISE NOTICE '❌ Error creating category folder for %: %', category_record.display_name, SQLERRM;
            folder_type := 'category';
            folder_display_name := category_record.display_name;
            folder_path := 'category/' || category_record.display_name || '/';
            folder_status := 'error: ' || SQLERRM;
            RETURN NEXT;
        END;
    END LOOP;
    
    -- =====================================================
    -- CREATE SUBCATEGORY FOLDERS FROM SUPABASE SUBCATEGORIES TABLE
    -- =====================================================
    
    RAISE NOTICE '';
    RAISE NOTICE '📁 Creating subcategory folders from Supabase subcategories table...';
    
    FOR subcategory_record IN 
        SELECT s.id, s.name, s.display_name, s.category_id, s.sort_order,
               c.name as category_name, c.display_name as category_display_name
        FROM subcategories s
        JOIN categories c ON s.category_id = c.id
        WHERE s.is_active = true 
        AND c.is_active = true
        ORDER BY c.sort_order, s.sort_order, s.display_name
    LOOP
        BEGIN
            -- Insert placeholder file to create folder
            INSERT INTO storage.objects (bucket_id, name, owner, metadata)
            VALUES (
                'category-images',
                'subcategory/' || subcategory_record.display_name || '/.keep',
                (SELECT auth.uid()),
                jsonb_build_object(
                    'size', length(placeholder_data),
                    'mimetype', 'image/png',
                    'subcategory_id', subcategory_record.id,
                    'subcategory_name', subcategory_record.name,
                    'display_name', subcategory_record.display_name,
                    'category_id', subcategory_record.category_id,
                    'category_name', subcategory_record.category_name,
                    'category_display_name', subcategory_record.category_display_name,
                    'sort_order', subcategory_record.sort_order,
                    'type', 'subcategory_folder_placeholder',
                    'created_from', 'supabase_subcategories_table'
                )
            )
            ON CONFLICT (bucket_id, name) DO NOTHING;
            
            -- Check if it was actually inserted
            IF FOUND THEN
                total_created := total_created + 1;
                folder_type := 'subcategory';
                folder_display_name := subcategory_record.display_name;
                folder_path := 'subcategory/' || subcategory_record.display_name || '/';
                folder_status := 'created';
                RETURN NEXT;

                RAISE NOTICE '✅ Created: subcategory/%', subcategory_record.display_name;
            ELSE
                total_skipped := total_skipped + 1;
                folder_type := 'subcategory';
                folder_display_name := subcategory_record.display_name;
                folder_path := 'subcategory/' || subcategory_record.display_name || '/';
                folder_status := 'already_exists';
                RETURN NEXT;

                RAISE NOTICE '📂 Exists: subcategory/%', subcategory_record.display_name;
            END IF;

        EXCEPTION WHEN OTHERS THEN
            RAISE NOTICE '❌ Error creating subcategory folder for %: %', subcategory_record.display_name, SQLERRM;
            folder_type := 'subcategory';
            folder_display_name := subcategory_record.display_name;
            folder_path := 'subcategory/' || subcategory_record.display_name || '/';
            folder_status := 'error: ' || SQLERRM;
            RETURN NEXT;
        END;
    END LOOP;
    
    RAISE NOTICE '';
    RAISE NOTICE '🎯 FOLDER CREATION SUMMARY:';
    RAISE NOTICE '   Total Created: %', total_created;
    RAISE NOTICE '   Total Skipped (already exist): %', total_skipped;
    RAISE NOTICE '';
    
    RETURN;
END;
$$;

-- =====================================================
-- STEP 3: EXECUTE THE FOLDER CREATION
-- =====================================================

-- Execute the function and show results
SELECT * FROM create_folders_from_supabase_data();

-- =====================================================
-- STEP 4: VERIFICATION AND SUMMARY
-- =====================================================

-- Show final summary
DO $$
DECLARE
    category_count INTEGER;
    subcategory_count INTEGER;
    created_category_folders INTEGER;
    created_subcategory_folders INTEGER;
    total_storage_objects INTEGER;
    old_loose_files INTEGER;
BEGIN
    -- Count actual data in Supabase tables
    SELECT COUNT(*) INTO category_count 
    FROM categories 
    WHERE is_active = true AND id != 'all';
    
    SELECT COUNT(*) INTO subcategory_count 
    FROM subcategories s
    JOIN categories c ON s.category_id = c.id
    WHERE s.is_active = true AND c.is_active = true;
    
    -- Count created folders in storage
    SELECT COUNT(*) INTO created_category_folders 
    FROM storage.objects 
    WHERE bucket_id = 'category-images' 
    AND name LIKE 'category/%/.keep';
    
    SELECT COUNT(*) INTO created_subcategory_folders 
    FROM storage.objects 
    WHERE bucket_id = 'category-images' 
    AND name LIKE 'subcategory/%/.keep';
    
    -- Count total storage objects
    SELECT COUNT(*) INTO total_storage_objects 
    FROM storage.objects 
    WHERE bucket_id = 'category-images';
    
    -- Count any remaining loose files
    SELECT COUNT(*) INTO old_loose_files 
    FROM storage.objects 
    WHERE bucket_id = 'category-images' 
    AND name NOT LIKE '%/%'
    AND name NOT LIKE '%.keep';
    
    RAISE NOTICE '';
    RAISE NOTICE '=== 🎯 COMPLETE FOLDER CLEANUP AND RECREATION SUMMARY ===';
    RAISE NOTICE '';
    RAISE NOTICE '📊 SUPABASE DATABASE DATA:';
    RAISE NOTICE '   Active Categories: %', category_count;
    RAISE NOTICE '   Active Subcategories: %', subcategory_count;
    RAISE NOTICE '   Total Expected Folders: %', category_count + subcategory_count;
    RAISE NOTICE '';
    
    RAISE NOTICE '📁 CREATED STORAGE FOLDERS:';
    RAISE NOTICE '   Category Folders: %', created_category_folders;
    RAISE NOTICE '   Subcategory Folders: %', created_subcategory_folders;
    RAISE NOTICE '   Total Created Folders: %', created_category_folders + created_subcategory_folders;
    RAISE NOTICE '';
    
    RAISE NOTICE '🧹 CLEANUP STATUS:';
    RAISE NOTICE '   Total Storage Objects: %', total_storage_objects;
    RAISE NOTICE '   Remaining Loose Files: %', old_loose_files;
    RAISE NOTICE '';
    
    IF (created_category_folders + created_subcategory_folders) = (category_count + subcategory_count) THEN
        RAISE NOTICE '✅ SUCCESS: All folders created perfectly!';
    ELSE
        RAISE NOTICE '⚠️  WARNING: Folder count mismatch - check for errors above';
    END IF;
    
    IF old_loose_files = 0 THEN
        RAISE NOTICE '✅ SUCCESS: All old loose images cleaned up!';
    ELSE
        RAISE NOTICE '⚠️  WARNING: % loose files still remain', old_loose_files;
    END IF;
    
    RAISE NOTICE '';
    RAISE NOTICE '🚀 READY FOR ORGANIZED IMAGE UPLOAD:';
    RAISE NOTICE '1. Go to: https://supabase.com/dashboard/project/irkwpzcskeqtasutqnxp/storage/buckets/category-images';
    RAISE NOTICE '2. Open category/ folder - see individual folders for each category';
    RAISE NOTICE '3. Open subcategory/ folder - see individual folders for each subcategory';
    RAISE NOTICE '4. Open any specific folder (e.g., category/Tyres/)';
    RAISE NOTICE '5. Drag and drop ANY image file (any name, any format)';
    RAISE NOTICE '6. Image appears instantly in your app!';
    RAISE NOTICE '';
    RAISE NOTICE '✅ PERFECT ORGANIZATION - EACH CATEGORY HAS ITS OWN FOLDER!';
    RAISE NOTICE '✅ NO MORE MIXED IMAGES - CLEAN SEPARATION!';
    RAISE NOTICE '✅ EASY MANAGEMENT - UPLOAD AND REPLACE EASILY!';
    RAISE NOTICE '✅ 100% ACCURATE - MATCHES YOUR SUPABASE DATABASE!';
END;
$$;
