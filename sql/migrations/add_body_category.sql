-- Migration: Add Body category and subcategories
-- Date: 2025-01-15
-- Description: Adds the "Body" category with 85 subcategories

-- Add the main category
INSERT INTO categories (id, name, display_name, description, id_prefix, sort_order)
VALUES (
  'body',
  'Body',
  'Body',
  'Body panels, doors, windows, mirrors, lights, and all exterior and interior body components',
  'BDY',
  15
) ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  display_name = EXCLUDED.display_name,
  description = EXCLUDED.description,
  id_prefix = EXCLUDED.id_prefix,
  sort_order = EXCLUDED.sort_order;

-- Add subcategories for Body

-- Body, side and rear related components (4 subcategories)
INSERT INTO subcategories (id, name, display_name, category_id, sort_order)
VALUES 
  ('body-exterior', 'Body exterior', 'Body exterior', 'body', 1),
  ('side-marker-panel', 'Side marker panel', 'Side marker panel', 'body', 2),
  ('wing-mirror-cover', 'Wing mirror cover', 'Wing mirror cover', 'body', 3),
  ('rear-door-strip', 'Rear door strip', 'Rear door strip', 'body', 4)
ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  display_name = EXCLUDED.display_name,
  category_id = EXCLUDED.category_id,
  sort_order = EXCLUDED.sort_order;

-- Car exterior (3 subcategories)
INSERT INTO subcategories (id, name, display_name, category_id, sort_order)
VALUES 
  ('door-handle', 'Door handle', 'Door handle', 'body', 5),
  ('bonnet-struts', 'Bonnet struts', 'Bonnet struts', 'body', 6),
  ('car-door-rubber-kit', 'Car door rubber kit', 'Car door rubber kit', 'body', 7)
ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  display_name = EXCLUDED.display_name,
  category_id = EXCLUDED.category_id,
  sort_order = EXCLUDED.sort_order;

-- Wing and related components (4 subcategories)
INSERT INTO subcategories (id, name, display_name, category_id, sort_order)
VALUES 
  ('wheel-arch-liner', 'Wheel arch liner', 'Wheel arch liner', 'body', 8),
  ('wing', 'Wing', 'Wing', 'body', 9),
  ('fender-apron', 'Fender apron', 'Fender apron', 'body', 10),
  ('wheel-arch-trim', 'Wheel arch trim', 'Wheel arch trim', 'body', 11)
ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  display_name = EXCLUDED.display_name,
  category_id = EXCLUDED.category_id,
  sort_order = EXCLUDED.sort_order;

-- Lighting and signalling components (12 subcategories)
INSERT INTO subcategories (id, name, display_name, category_id, sort_order)
VALUES
  ('rear-lights', 'Rear lights', 'Rear lights', 'body', 12),
  ('headlights', 'Headlights', 'Headlights', 'body', 13),
  ('indicator', 'Indicator', 'Indicator', 'body', 14),
  ('tail-lights', 'Tail lights', 'Tail lights', 'body', 15),
  ('number-plate-light', 'Number plate light', 'Number plate light', 'body', 16),
  ('reflector', 'Reflector', 'Reflector', 'body', 17),
  ('headlight-parts', 'Headlight parts', 'Headlight parts', 'body', 18),
  ('environment-sensor', 'Environment sensor', 'Environment sensor', 'body', 19),
  ('fog-light-cover', 'Fog light cover', 'Fog light cover', 'body', 20),
  ('fog-light-parts', 'Fog light parts', 'Fog light parts', 'body', 21),
  ('daytime-running-light', 'Daytime running light', 'Daytime running light', 'body', 22),
  ('reverse-lights', 'Reverse lights', 'Reverse lights', 'body', 23)
ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  display_name = EXCLUDED.display_name,
  category_id = EXCLUDED.category_id,
  sort_order = EXCLUDED.sort_order;

-- Additional lighting components (4 subcategories)
INSERT INTO subcategories (id, name, display_name, category_id, sort_order)
VALUES
  ('stop-control-unit-2', 'Stop control unit 2', 'Stop control unit 2', 'body', 24),
  ('headlight-kit', 'Headlight kit', 'Headlight kit', 'body', 25),
  ('rear-light', 'Rear light', 'Rear light', 'body', 26),
  ('low-vibration', 'Low vibration', 'Low vibration', 'body', 27)
ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  display_name = EXCLUDED.display_name,
  category_id = EXCLUDED.category_id,
  sort_order = EXCLUDED.sort_order;

-- Bumpers and related components (8 subcategories)
INSERT INTO subcategories (id, name, display_name, category_id, sort_order)
VALUES
  ('bumper-trim', 'Bumper trim', 'Bumper trim', 'body', 28),
  ('bumper', 'Bumper', 'Bumper', 'body', 29),
  ('front-end', 'Front end', 'Front end', 'body', 30),
  ('air-dam', 'Air dam', 'Air dam', 'body', 31),
  ('bumper-bracket', 'Bumper bracket', 'Bumper bracket', 'body', 32),
  ('tow-rope', 'Tow rope', 'Tow rope', 'body', 33),
  ('parking-sensors', 'Parking sensors', 'Parking sensors', 'body', 34),
  ('rubber-plate-bumper', 'Rubber plate bumper', 'Rubber plate bumper', 'body', 35)
ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  display_name = EXCLUDED.display_name,
  category_id = EXCLUDED.category_id,
  sort_order = EXCLUDED.sort_order;

-- Additional bumper components (3 subcategories)
INSERT INTO subcategories (id, name, display_name, category_id, sort_order)
VALUES
  ('tow-hook-cover', 'Tow hook cover', 'Tow hook cover', 'body', 36),
  ('mudflap', 'Mudflap', 'Mudflap', 'body', 37),
  ('roof-rack-cover-and-seal', 'Roof rack cover and seal', 'Roof rack cover and seal', 'body', 38)
ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  display_name = EXCLUDED.display_name,
  category_id = EXCLUDED.category_id,
  sort_order = EXCLUDED.sort_order;

-- Radio (4 subcategories)
INSERT INTO subcategories (id, name, display_name, category_id, sort_order)
VALUES
  ('communication', 'Communication', 'Communication', 'body', 39),
  ('infotainment', 'Infotainment', 'Infotainment', 'body', 40),
  ('central-door-locking-kit', 'Central door locking kit', 'Central door locking kit', 'body', 41),
  ('vehicle-light-kit', 'Vehicle light kit', 'Vehicle light kit', 'body', 42)
ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  display_name = EXCLUDED.display_name,
  category_id = EXCLUDED.category_id,
  sort_order = EXCLUDED.sort_order;

-- Additional radio components (2 subcategories)
INSERT INTO subcategories (id, name, display_name, category_id, sort_order)
VALUES
  ('central-door-locking-kit-2', 'Central door locking kit 2', 'Central door locking kit 2', 'body', 43),
  ('vehicle-light-kit-2', 'Vehicle light kit 2', 'Vehicle light kit 2', 'body', 44)
ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  display_name = EXCLUDED.display_name,
  category_id = EXCLUDED.category_id,
  sort_order = EXCLUDED.sort_order;

-- Body and related components (1 subcategory)
INSERT INTO subcategories (id, name, display_name, category_id, sort_order)
VALUES
  ('sun-roof', 'Sun roof', 'Sun roof', 'body', 45)
ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  display_name = EXCLUDED.display_name,
  category_id = EXCLUDED.category_id,
  sort_order = EXCLUDED.sort_order;

-- Doors and related components (8 subcategories)
INSERT INTO subcategories (id, name, display_name, category_id, sort_order)
VALUES
  ('door-trim', 'Door trim', 'Door trim', 'body', 46),
  ('door-catch', 'Door catch', 'Door catch', 'body', 47),
  ('door-handle-2', 'Door handle 2', 'Door handle 2', 'body', 48),
  ('door-trim-2', 'Door trim 2', 'Door trim 2', 'body', 49),
  ('door-lock', 'Door lock', 'Door lock', 'body', 50),
  ('door-hinge', 'Door hinge', 'Door hinge', 'body', 51),
  ('door-seal', 'Door seal', 'Door seal', 'body', 52),
  ('door-check', 'Door check', 'Door check', 'body', 53)
ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  display_name = EXCLUDED.display_name,
  category_id = EXCLUDED.category_id,
  sort_order = EXCLUDED.sort_order;

-- Additional door components (4 subcategories)
INSERT INTO subcategories (id, name, display_name, category_id, sort_order)
VALUES
  ('door-handle-3', 'Door handle 3', 'Door handle 3', 'body', 54),
  ('door-trim-3', 'Door trim 3', 'Door trim 3', 'body', 55),
  ('door-lock-2', 'Door lock 2', 'Door lock 2', 'body', 56),
  ('door-hinge-2', 'Door hinge 2', 'Door hinge 2', 'body', 57)
ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  display_name = EXCLUDED.display_name,
  category_id = EXCLUDED.category_id,
  sort_order = EXCLUDED.sort_order;

-- Fuel tanks and related components (4 subcategories)
INSERT INTO subcategories (id, name, display_name, category_id, sort_order)
VALUES
  ('fuel-tank-cap-for-fuel-tank', 'Fuel tank cap for fuel tank', 'Fuel tank cap for fuel tank', 'body', 58),
  ('fuel-tank-mounting-parts', 'Fuel tank mounting parts', 'Fuel tank mounting parts', 'body', 59),
  ('fuel-tank-cap', 'Fuel tank cap', 'Fuel tank cap', 'body', 60),
  ('fuel-tank-mounting-parts-2', 'Fuel tank mounting parts 2', 'Fuel tank mounting parts 2', 'body', 61)
ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  display_name = EXCLUDED.display_name,
  category_id = EXCLUDED.category_id,
  sort_order = EXCLUDED.sort_order;

-- Body parts and related components (8 subcategories)
INSERT INTO subcategories (id, name, display_name, category_id, sort_order)
VALUES
  ('bonnet-support', 'Bonnet support', 'Bonnet support', 'body', 62),
  ('bonnet-panel', 'Bonnet panel', 'Bonnet panel', 'body', 63),
  ('hardtop', 'Hardtop', 'Hardtop', 'body', 64),
  ('convertible', 'Convertible', 'Convertible', 'body', 65),
  ('boot-lid-parts', 'Boot lid parts', 'Boot lid parts', 'body', 66),
  ('boot', 'Boot', 'Boot', 'body', 67),
  ('roof-rack', 'Roof rack', 'Roof rack', 'body', 68),
  ('rear-window', 'Rear window', 'Rear window', 'body', 69)
ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  display_name = EXCLUDED.display_name,
  category_id = EXCLUDED.category_id,
  sort_order = EXCLUDED.sort_order;

-- Additional body parts and related components (8 subcategories)
INSERT INTO subcategories (id, name, display_name, category_id, sort_order)
VALUES
  ('bonnet-support-2', 'Bonnet support 2', 'Bonnet support 2', 'body', 70),
  ('bonnet-panel-2', 'Bonnet panel 2', 'Bonnet panel 2', 'body', 71),
  ('hardtop-2', 'Hardtop 2', 'Hardtop 2', 'body', 72),
  ('convertible-2', 'Convertible 2', 'Convertible 2', 'body', 73),
  ('boot-lid-parts-2', 'Boot lid parts 2', 'Boot lid parts 2', 'body', 74),
  ('boot-2', 'Boot 2', 'Boot 2', 'body', 75),
  ('roof-rack-2', 'Roof rack 2', 'Roof rack 2', 'body', 76),
  ('rear-window-2', 'Rear window 2', 'Rear window 2', 'body', 77)
ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  display_name = EXCLUDED.display_name,
  category_id = EXCLUDED.category_id,
  sort_order = EXCLUDED.sort_order;

-- Window and glass components (8 subcategories)
INSERT INTO subcategories (id, name, display_name, category_id, sort_order)
VALUES
  ('window-regulator', 'Window regulator', 'Window regulator', 'body', 78),
  ('window-panel', 'Window panel', 'Window panel', 'body', 79),
  ('windscreen', 'Windscreen', 'Windscreen', 'body', 80),
  ('windscreen-wiper-blade', 'Windscreen wiper blade', 'Windscreen wiper blade', 'body', 81),
  ('windscreen-wiper-motor', 'Windscreen wiper motor', 'Windscreen wiper motor', 'body', 82),
  ('windscreen-washer-pump', 'Windscreen washer pump', 'Windscreen washer pump', 'body', 83),
  ('windscreen-wiper-linkage', 'Windscreen wiper linkage', 'Windscreen wiper linkage', 'body', 84),
  ('windscreen-wiper-arm', 'Windscreen wiper arm', 'Windscreen wiper arm', 'body', 85)
ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  display_name = EXCLUDED.display_name,
  category_id = EXCLUDED.category_id,
  sort_order = EXCLUDED.sort_order;
