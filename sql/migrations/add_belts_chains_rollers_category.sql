-- Migration: Add Belts, chains, rollers category and subcategories
-- Date: 2025-01-15
-- Description: Adds the "Belts, chains, rollers" category with 32 subcategories

-- Add the main category
INSERT INTO categories (id, name, display_name, description, id_prefix, sort_order)
VALUES (
  'belts-chains-rollers',
  'Belts, chains, rollers',
  'Belts, chains, rollers',
  'Timing belts, serpentine belts, chains, pulleys, tensioners, and related drive components',
  'BCR',
  12
) ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  display_name = EXCLUDED.display_name,
  description = EXCLUDED.description,
  id_prefix = EXCLUDED.id_prefix,
  sort_order = EXCLUDED.sort_order;

-- Add subcategories for Belts, chains, rollers (32 total)

-- Accessory drive components (10 subcategories)
INSERT INTO subcategories (id, name, display_name, category_id, sort_order)
VALUES
  ('serpentine-belt', 'Serpentine belt', 'Serpentine belt', 'belts-chains-rollers', 1),
  ('drive-belt-tensioner', 'Drive belt tensioner', 'Drive belt tensioner', 'belts-chains-rollers', 2),
  ('deflection-guide-pulley-v-belt', 'Deflection / guide pulley, v-belt', 'Deflection / guide pulley, v-belt', 'belts-chains-rollers', 3),
  ('poly-v-belt-kit', 'Poly v-belt kit', 'Poly v-belt kit', 'belts-chains-rollers', 4),
  ('idler-pulley-serpentine-belt', 'Idler pulley, serpentine belt', 'Idler pulley, serpentine belt', 'belts-chains-rollers', 5),
  ('tensioner-pulley-v-belt', 'Tensioner pulley, v-belt', 'Tensioner pulley, v-belt', 'belts-chains-rollers', 6),
  ('tensioner-pulley-v-ribbed-belt', 'Tensioner pulley, v-ribbed belt', 'Tensioner pulley, v-ribbed belt', 'belts-chains-rollers', 7),
  ('vibration-damper-v-ribbed-belt', 'Vibration damper, v-ribbed belt', 'Vibration damper, v-ribbed belt', 'belts-chains-rollers', 8),
  ('v-belt', 'V-belt', 'V-belt', 'belts-chains-rollers', 9),
  ('power-steering-pump-pulley', 'Power steering pump pulley', 'Power steering pump pulley', 'belts-chains-rollers', 10)
ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  display_name = EXCLUDED.display_name,
  category_id = EXCLUDED.category_id,
  sort_order = EXCLUDED.sort_order;

-- Timing belts and related components (9 subcategories)
INSERT INTO subcategories (id, name, display_name, category_id, sort_order)
VALUES
  ('timing-belt-kit', 'Timing belt kit', 'Timing belt kit', 'belts-chains-rollers', 11),
  ('timing-belt-tensioner-pulley', 'Timing belt tensioner pulley', 'Timing belt tensioner pulley', 'belts-chains-rollers', 12),
  ('vibration-damper-timing-belt', 'Vibration damper, timing belt', 'Vibration damper, timing belt', 'belts-chains-rollers', 13),
  ('timing-belt-and-water-pump', 'Timing belt and water pump', 'Timing belt and water pump', 'belts-chains-rollers', 14),
  ('water-pump', 'Water pump', 'Water pump', 'belts-chains-rollers', 15),
  ('timing-belt-deflection-pulley', 'Timing belt deflection pulley', 'Timing belt deflection pulley', 'belts-chains-rollers', 16),
  ('timing-belt', 'Timing belt', 'Timing belt', 'belts-chains-rollers', 17),
  ('timing-cover', 'Timing cover', 'Timing cover', 'belts-chains-rollers', 18),
  ('timing-belt-tensioner', 'Timing belt tensioner', 'Timing belt tensioner', 'belts-chains-rollers', 19)
ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  display_name = EXCLUDED.display_name,
  category_id = EXCLUDED.category_id,
  sort_order = EXCLUDED.sort_order;

-- Timing chains and related components (6 subcategories)
INSERT INTO subcategories (id, name, display_name, category_id, sort_order)
VALUES
  ('timing-chain', 'Timing chain', 'Timing chain', 'belts-chains-rollers', 20),
  ('timing-chain-kit', 'Timing chain kit', 'Timing chain kit', 'belts-chains-rollers', 21),
  ('timing-chain-guide', 'Timing chain guide', 'Timing chain guide', 'belts-chains-rollers', 22),
  ('seal-timing-chain-tensioner', 'Seal, timing chain tensioner', 'Seal, timing chain tensioner', 'belts-chains-rollers', 23),
  ('drive-chain', 'Drive chain', 'Drive chain', 'belts-chains-rollers', 24),
  ('timing-chain-tensioner', 'Timing chain tensioner', 'Timing chain tensioner', 'belts-chains-rollers', 25)
ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  display_name = EXCLUDED.display_name,
  category_id = EXCLUDED.category_id,
  sort_order = EXCLUDED.sort_order;

-- Pulleys and related components (5 subcategories)
INSERT INTO subcategories (id, name, display_name, category_id, sort_order)
VALUES
  ('crankshaft-pulley', 'Crankshaft pulley', 'Crankshaft pulley', 'belts-chains-rollers', 26),
  ('water-pump-pulley', 'Water pump pulley', 'Water pump pulley', 'belts-chains-rollers', 27),
  ('freewheel-clutch', 'Freewheel clutch', 'Freewheel clutch', 'belts-chains-rollers', 28),
  ('pulley-bolt', 'Pulley bolt', 'Pulley bolt', 'belts-chains-rollers', 29),
  ('flexible-coupling-sleeve', 'Flexible coupling sleeve', 'Flexible coupling sleeve', 'belts-chains-rollers', 30)
ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  display_name = EXCLUDED.display_name,
  category_id = EXCLUDED.category_id,
  sort_order = EXCLUDED.sort_order;

-- Tools (1 subcategory)
INSERT INTO subcategories (id, name, display_name, category_id, sort_order)
VALUES
  ('belt-chain-tools', 'Belt / chain tools', 'Belt / chain tools', 'belts-chains-rollers', 31)
ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  display_name = EXCLUDED.display_name,
  category_id = EXCLUDED.category_id,
  sort_order = EXCLUDED.sort_order;

-- Verify the insertions
SELECT 'Category added:' as status, id, name FROM categories WHERE id = 'belts-chains-rollers';
SELECT 'Subcategories added:' as status, COUNT(*) as count FROM subcategories WHERE category_id = 'belts-chains-rollers';
SELECT 'All subcategories:' as status, id, name FROM subcategories WHERE category_id = 'belts-chains-rollers' ORDER BY name;
