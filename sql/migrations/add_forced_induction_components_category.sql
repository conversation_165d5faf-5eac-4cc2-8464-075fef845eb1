-- Migration: Add Forced induction components category and subcategories
-- Date: 2025-01-15
-- Description: Adds the "Forced induction components" category with 10 subcategories

-- Add the main category
INSERT INTO categories (id, name, display_name, description, id_prefix, sort_order)
VALUES (
  'forced-induction-components',
  'Forced induction components',
  'Forced induction components',
  'Turbochargers, intercoolers, boost sensors, and related forced induction system components',
  'FIC',
  13
) ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  display_name = EXCLUDED.display_name,
  description = EXCLUDED.description,
  id_prefix = EXCLUDED.id_prefix,
  sort_order = EXCLUDED.sort_order;

-- Add subcategories for Forced induction components (10 total)

-- Forced induction components (10 subcategories)
INSERT INTO subcategories (id, name, display_name, category_id, sort_order)
VALUES 
  ('turbo', 'Turbo', 'Turbo', 'forced-induction-components', 1),
  ('intercooler', 'Intercooler', 'Intercooler', 'forced-induction-components', 2),
  ('intercooler-pipe', 'Intercooler pipe', 'Intercooler pipe', 'forced-induction-components', 3),
  ('mounting-kit-charger', 'Mounting kit, charger', 'Mounting kit, charger', 'forced-induction-components', 4),
  ('turbo-gasket', 'Turbo gasket', 'Turbo gasket', 'forced-induction-components', 5),
  ('turbo-solenoid-valve', 'Turbo solenoid valve', 'Turbo solenoid valve', 'forced-induction-components', 6),
  ('boost-pressure-sensor', 'Boost pressure sensor', 'Boost pressure sensor', 'forced-induction-components', 7),
  ('turbo-oil-feed-pipe', 'Turbo oil feed pipe', 'Turbo oil feed pipe', 'forced-induction-components', 8),
  ('diverter-valve', 'Diverter valve', 'Diverter valve', 'forced-induction-components', 9),
  ('seal-turbo-air-hose', 'Seal, turbo air hose', 'Seal, turbo air hose', 'forced-induction-components', 10)
ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  display_name = EXCLUDED.display_name,
  category_id = EXCLUDED.category_id,
  sort_order = EXCLUDED.sort_order;

-- Verify the insertions
SELECT 'Category added:' as status, id, name FROM categories WHERE id = 'forced-induction-components';
SELECT 'Subcategories added:' as status, COUNT(*) as count FROM subcategories WHERE category_id = 'forced-induction-components';
SELECT 'All subcategories:' as status, id, name FROM subcategories WHERE category_id = 'forced-induction-components' ORDER BY name;
