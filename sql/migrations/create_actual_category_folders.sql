-- ACTUALLY CREATE PHYSICAL FOLDERS in Supabase Storage
-- This SQL will create real folders that you can see in the Supabase Storage UI
-- Each category and subcategory will have its own folder

-- 1. Ensure the category-images bucket exists
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES (
  'category-images',
  'category-images',
  true,
  10485760, -- 10MB limit
  ARRAY['image/jpeg', 'image/png', 'image/webp', 'image/svg+xml']
)
ON CONFLICT (id) DO NOTHING;

-- 2. Create RLS policies
DROP POLICY IF EXISTS "Public read access for category images" ON storage.objects;
DROP POLICY IF EXISTS "Authenticated users can upload category images" ON storage.objects;
DROP POLICY IF EXISTS "Authenticated users can update category images" ON storage.objects;
DROP POLICY IF EXISTS "Authenticated users can delete category images" ON storage.objects;

CREATE POLICY "Public read access for category images"
ON storage.objects FOR SELECT
USING (bucket_id = 'category-images');

CREATE POLICY "Authenticated users can upload category images"
ON storage.objects FOR INSERT
WITH CHECK (bucket_id = 'category-images' AND auth.role() = 'authenticated');

CREATE POLICY "Authenticated users can update category images"
ON storage.objects FOR UPDATE
USING (bucket_id = 'category-images' AND auth.role() = 'authenticated')
WITH CHECK (bucket_id = 'category-images' AND auth.role() = 'authenticated');

CREATE POLICY "Authenticated users can delete category images"
ON storage.objects FOR DELETE
USING (bucket_id = 'category-images' AND auth.role() = 'authenticated');

-- 3. Create function to actually create folders using storage.objects
CREATE OR REPLACE FUNCTION create_physical_category_folders()
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    category_record RECORD;
    subcategory_record RECORD;
    placeholder_data bytea;
    folder_count INTEGER := 0;
BEGIN
    -- Create a minimal placeholder file (1x1 transparent PNG)
    placeholder_data := decode('iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChAGA6VP8IQAAAABJRU5ErkJggg==', 'base64');
    
    RAISE NOTICE 'Creating physical folders in Supabase Storage...';
    
    -- Create category folders
    FOR category_record IN 
        SELECT id, name, display_name 
        FROM categories 
        WHERE id != 'all'
    LOOP
        BEGIN
            -- Insert placeholder file to create the folder structure
            INSERT INTO storage.objects (bucket_id, name, owner, metadata)
            VALUES (
                'category-images',
                'category/' || category_record.display_name || '/.keep',
                (SELECT auth.uid()),
                jsonb_build_object(
                    'size', length(placeholder_data),
                    'mimetype', 'image/png',
                    'category_id', category_record.id,
                    'category_name', category_record.name,
                    'display_name', category_record.display_name,
                    'type', 'category_folder_placeholder'
                )
            )
            ON CONFLICT (bucket_id, name) DO NOTHING;
            
            folder_count := folder_count + 1;
            RAISE NOTICE 'Created category folder: %', category_record.display_name;
            
        EXCEPTION WHEN OTHERS THEN
            RAISE NOTICE 'Could not create category folder for %: %', category_record.display_name, SQLERRM;
        END;
    END LOOP;
    
    -- Create subcategory folders
    FOR subcategory_record IN 
        SELECT s.id, s.name, s.display_name, c.name as category_name
        FROM subcategories s
        JOIN categories c ON s.category_id = c.id
    LOOP
        BEGIN
            -- Insert placeholder file to create the folder structure
            INSERT INTO storage.objects (bucket_id, name, owner, metadata)
            VALUES (
                'category-images',
                'subcategory/' || subcategory_record.display_name || '/.keep',
                (SELECT auth.uid()),
                jsonb_build_object(
                    'size', length(placeholder_data),
                    'mimetype', 'image/png',
                    'subcategory_id', subcategory_record.id,
                    'subcategory_name', subcategory_record.name,
                    'display_name', subcategory_record.display_name,
                    'category_name', subcategory_record.category_name,
                    'type', 'subcategory_folder_placeholder'
                )
            )
            ON CONFLICT (bucket_id, name) DO NOTHING;
            
            folder_count := folder_count + 1;
            RAISE NOTICE 'Created subcategory folder: %', subcategory_record.display_name;
            
        EXCEPTION WHEN OTHERS THEN
            RAISE NOTICE 'Could not create subcategory folder for %: %', subcategory_record.display_name, SQLERRM;
        END;
    END LOOP;
    
    RAISE NOTICE 'Folder creation completed! Created % folders total.', folder_count;
END;
$$;

-- 4. Execute the function to create all folders
SELECT create_physical_category_folders();

-- 5. Create view to show the folder structure
CREATE OR REPLACE VIEW category_folder_structure AS
SELECT 
    'category' as type,
    c.id,
    c.display_name,
    'category/' || c.display_name || '/' as folder_path,
    'Upload any image file to this folder' as instructions
FROM categories c
WHERE c.id != 'all'

UNION ALL

SELECT 
    'subcategory' as type,
    s.id,
    s.display_name,
    'subcategory/' || s.display_name || '/' as folder_path,
    'Upload any image file to this folder' as instructions
FROM subcategories s
ORDER BY type, display_name;

-- 6. Show completion summary
DO $$
DECLARE
    category_count INTEGER;
    subcategory_count INTEGER;
    total_folders INTEGER;
    created_objects INTEGER;
BEGIN
    -- Count categories and subcategories
    SELECT COUNT(*) INTO category_count FROM categories WHERE id != 'all';
    SELECT COUNT(*) INTO subcategory_count FROM subcategories;
    total_folders := category_count + subcategory_count;
    
    -- Count created storage objects
    SELECT COUNT(*) INTO created_objects 
    FROM storage.objects 
    WHERE bucket_id = 'category-images' 
    AND name LIKE '%/.keep';
    
    RAISE NOTICE '';
    RAISE NOTICE '=== 🎯 PHYSICAL FOLDERS CREATED IN SUPABASE STORAGE ===';
    RAISE NOTICE '';
    RAISE NOTICE '📊 RESULTS:';
    RAISE NOTICE '   Categories: % folders', category_count;
    RAISE NOTICE '   Subcategories: % folders', subcategory_count;
    RAISE NOTICE '   Total expected: % folders', total_folders;
    RAISE NOTICE '   Actually created: % folders', created_objects;
    RAISE NOTICE '';
    
    RAISE NOTICE '🎯 WHAT YOU CAN DO NOW:';
    RAISE NOTICE '1. Go to: https://supabase.com/dashboard/project/irkwpzcskeqtasutqnxp/storage/buckets/category-images';
    RAISE NOTICE '2. You will see "category" and "subcategory" folders';
    RAISE NOTICE '3. Open "category" folder - you will see individual folders for each category:';
    RAISE NOTICE '   - Tyres/';
    RAISE NOTICE '   - Brakes/';
    RAISE NOTICE '   - Gaskets and sealing rings/';
    RAISE NOTICE '   - etc.';
    RAISE NOTICE '4. Open "subcategory" folder - you will see individual folders for each subcategory:';
    RAISE NOTICE '   - Brake Pads/';
    RAISE NOTICE '   - Oil Filters/';
    RAISE NOTICE '   - Intake Manifold Gasket/';
    RAISE NOTICE '   - etc.';
    RAISE NOTICE '5. Open any specific folder (e.g., category/Tyres/)';
    RAISE NOTICE '6. Drag and drop ANY image file (any name, any format)';
    RAISE NOTICE '7. Image will appear instantly in your app!';
    RAISE NOTICE '';
    
    RAISE NOTICE '✅ PERFECT ORGANIZATION:';
    RAISE NOTICE '✅ Each category has its own folder';
    RAISE NOTICE '✅ Each subcategory has its own folder';
    RAISE NOTICE '✅ No file naming restrictions';
    RAISE NOTICE '✅ Easy to find and replace images';
    RAISE NOTICE '✅ No mixing of different category images';
    RAISE NOTICE '';
    
    RAISE NOTICE '📋 TO SEE ALL FOLDERS:';
    RAISE NOTICE '   Run: SELECT * FROM category_folder_structure;';
    RAISE NOTICE '';
    
    RAISE NOTICE '🚀 READY FOR BULK IMAGE UPLOAD!';
END;
$$;
