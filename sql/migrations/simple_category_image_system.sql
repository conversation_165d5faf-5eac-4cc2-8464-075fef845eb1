-- Simple Category Image System - NO FOLDER CREATION REQUIRED!
-- Just upload images with flexible naming directly to main folders

-- 1. Ensure the category-images bucket exists with proper configuration
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES (
  'category-images',
  'category-images',
  true,
  10485760, -- 10MB limit
  ARRAY['image/jpeg', 'image/png', 'image/webp', 'image/svg+xml']
)
ON CONFLICT (id) DO NOTHING;

-- 2. Create RLS policies for the category-images bucket
DROP POLICY IF EXISTS "Public read access for category images" ON storage.objects;
DROP POLICY IF EXISTS "Authenticated users can upload category images" ON storage.objects;
DROP POLICY IF EXISTS "Authenticated users can update category images" ON storage.objects;
DROP POLICY IF EXISTS "Authenticated users can delete category images" ON storage.objects;

-- Public read access
CREATE POLICY "Public read access for category images"
ON storage.objects FOR SELECT
USING (bucket_id = 'category-images');

-- Authenticated users can upload
CREATE POLICY "Authenticated users can upload category images"
ON storage.objects FOR INSERT
WITH CHECK (
  bucket_id = 'category-images' 
  AND auth.role() = 'authenticated'
);

-- Authenticated users can update
CREATE POLICY "Authenticated users can update category images"
ON storage.objects FOR UPDATE
USING (
  bucket_id = 'category-images' 
  AND auth.role() = 'authenticated'
)
WITH CHECK (
  bucket_id = 'category-images' 
  AND auth.role() = 'authenticated'
);

-- Authenticated users can delete
CREATE POLICY "Authenticated users can delete category images"
ON storage.objects FOR DELETE
USING (
  bucket_id = 'category-images' 
  AND auth.role() = 'authenticated'
);

-- 3. Create a comprehensive view of all category image requirements
CREATE OR REPLACE VIEW category_image_requirements AS
SELECT 
    'category' as type,
    c.id,
    c.name,
    c.display_name,
    NULL as category_name,
    'category/' as upload_folder,
    ARRAY[
        c.id || '.png',
        c.display_name || '.png', 
        lower(c.display_name) || '.png',
        replace(lower(c.display_name), ' ', '-') || '.png',
        replace(lower(c.display_name), ' ', '_') || '.png',
        replace(c.display_name, ' ', '') || '.png'
    ] as accepted_filenames,
    'Upload to category/ folder with any of the accepted filenames' as instructions
FROM categories c
WHERE c.id != 'all'

UNION ALL

SELECT 
    'subcategory' as type,
    s.id,
    s.name,
    s.display_name,
    c.display_name as category_name,
    'subcategory/' as upload_folder,
    ARRAY[
        s.id || '.png',
        s.display_name || '.png',
        lower(s.display_name) || '.png', 
        replace(lower(s.display_name), ' ', '-') || '.png',
        replace(lower(s.display_name), ' ', '_') || '.png',
        replace(s.display_name, ' ', '') || '.png'
    ] as accepted_filenames,
    'Upload to subcategory/ folder with any of the accepted filenames' as instructions
FROM subcategories s
JOIN categories c ON s.category_id = c.id
ORDER BY type, display_name;

-- 4. Create a simple notification function for new categories/subcategories
CREATE OR REPLACE FUNCTION notify_category_image_needed()
RETURNS trigger
LANGUAGE plpgsql
AS $$
BEGIN
    IF TG_TABLE_NAME = 'categories' AND NEW.id != 'all' THEN
        RAISE NOTICE 'New category added: % - Upload image as: category/%.png (or any accepted naming pattern)', NEW.display_name, NEW.display_name;
        
    ELSIF TG_TABLE_NAME = 'subcategories' THEN
        RAISE NOTICE 'New subcategory added: % - Upload image as: subcategory/%.png (or any accepted naming pattern)', NEW.display_name, NEW.display_name;
    END IF;
    
    RETURN NEW;
END;
$$;

-- 5. Create triggers for notifications
DROP TRIGGER IF EXISTS trigger_notify_category_image ON categories;
CREATE TRIGGER trigger_notify_category_image
    AFTER INSERT ON categories
    FOR EACH ROW
    EXECUTE FUNCTION notify_category_image_needed();

DROP TRIGGER IF EXISTS trigger_notify_subcategory_image ON subcategories;
CREATE TRIGGER trigger_notify_subcategory_image
    AFTER INSERT ON subcategories
    FOR EACH ROW
    EXECUTE FUNCTION notify_category_image_needed();

-- 6. Verification and summary
DO $$
DECLARE
    category_count INTEGER;
    subcategory_count INTEGER;
    total_images INTEGER;
BEGIN
    -- Count categories and subcategories
    SELECT COUNT(*) INTO category_count FROM categories WHERE id != 'all';
    SELECT COUNT(*) INTO subcategory_count FROM subcategories;
    total_images := category_count + subcategory_count;
    
    RAISE NOTICE '=== 🎯 SIMPLE CATEGORY IMAGE SYSTEM SETUP COMPLETE ===';
    RAISE NOTICE '';
    RAISE NOTICE '📊 SUMMARY:';
    RAISE NOTICE '   Categories: % images needed', category_count;
    RAISE NOTICE '   Subcategories: % images needed', subcategory_count;
    RAISE NOTICE '   Total images: %', total_images;
    RAISE NOTICE '';
    
    RAISE NOTICE '🚀 SUPER SIMPLE UPLOAD PROCESS:';
    RAISE NOTICE '1. Go to: https://supabase.com/dashboard/project/irkwpzcskeqtasutqnxp/storage/buckets/category-images';
    RAISE NOTICE '2. Create ONLY two folders: "category" and "subcategory"';
    RAISE NOTICE '3. Upload images directly to these folders with flexible naming:';
    RAISE NOTICE '';
    
    RAISE NOTICE '💡 FLEXIBLE NAMING EXAMPLES:';
    RAISE NOTICE '   For "Tyres" category, ANY of these work:';
    RAISE NOTICE '   → category/tyres.png (ID-based)';
    RAISE NOTICE '   → category/Tyres.png (Display name)';
    RAISE NOTICE '   → category/tyres.jpg (Different extension)';
    RAISE NOTICE '   → category/tire.png (Close match)';
    RAISE NOTICE '';
    RAISE NOTICE '   For "Brake Pads" subcategory, ANY of these work:';
    RAISE NOTICE '   → subcategory/brake-pads.png (ID-based)';
    RAISE NOTICE '   → subcategory/Brake Pads.png (Display name)';
    RAISE NOTICE '   → subcategory/brake_pads.png (Snake case)';
    RAISE NOTICE '   → subcategory/BrakePads.png (No spaces)';
    RAISE NOTICE '';
    
    RAISE NOTICE '📋 TO GET COMPLETE NAMING OPTIONS:';
    RAISE NOTICE '   Run: SELECT display_name, accepted_filenames FROM category_image_requirements WHERE type = ''category'' ORDER BY display_name;';
    RAISE NOTICE '';
    
    RAISE NOTICE '✅ BENEFITS:';
    RAISE NOTICE '✅ NO FOLDER CREATION REQUIRED (except 2 main folders)';
    RAISE NOTICE '✅ FLEXIBLE FILE NAMING - Multiple patterns accepted';
    RAISE NOTICE '✅ SMART DETECTION - System finds images automatically';
    RAISE NOTICE '✅ BULK UPLOAD FRIENDLY - Upload hundreds at once';
    RAISE NOTICE '✅ ANY EXTENSION SUPPORTED - PNG, JPG, JPEG, WebP, SVG';
    RAISE NOTICE '';
    RAISE NOTICE '🔧 TECHNICAL DETAILS:';
    RAISE NOTICE '   - Bucket: category-images (public read, authenticated write)';
    RAISE NOTICE '   - File size limit: 10MB';
    RAISE NOTICE '   - Smart detection tries 7 naming patterns per image';
    RAISE NOTICE '   - Automatic fallback to placeholder if no image found';
    RAISE NOTICE '';
    RAISE NOTICE '🎯 READY FOR BULK UPLOAD!';
END;
$$;
