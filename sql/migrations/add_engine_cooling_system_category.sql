-- Migration: Add Engine cooling system category and subcategories
-- Date: 2025-01-15
-- Description: Adds the "Engine cooling system" category with 47 subcategories

-- Add the main category
INSERT INTO categories (id, name, display_name, description, id_prefix, sort_order)
VALUES (
  'engine-cooling-system',
  'Engine cooling system',
  'Engine cooling system',
  'Thermostats, radiators, water pumps, coolant hoses, and all engine cooling system components',
  'ECS',
  14
) ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  display_name = EXCLUDED.display_name,
  description = EXCLUDED.description,
  id_prefix = EXCLUDED.id_prefix,
  sort_order = EXCLUDED.sort_order;

-- Add subcategories for Engine cooling system (47 total)

-- Thermostats and related components (3 subcategories)
INSERT INTO subcategories (id, name, display_name, category_id, sort_order)
VALUES 
  ('thermostat', 'Thermostat', 'Thermostat', 'engine-cooling-system', 1),
  ('coolant-flange', 'Coolant flange', 'Coolant flange', 'engine-cooling-system', 2),
  ('thermostat-gasket', 'Thermostat gasket', 'Thermostat gasket', 'engine-cooling-system', 3)
ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  display_name = EXCLUDED.display_name,
  category_id = EXCLUDED.category_id,
  sort_order = EXCLUDED.sort_order;

-- Engine radiators and related components (11 subcategories)
INSERT INTO subcategories (id, name, display_name, category_id, sort_order)
VALUES 
  ('radiator', 'Radiator', 'Radiator', 'engine-cooling-system', 4),
  ('expansion-tank', 'Expansion tank', 'Expansion tank', 'engine-cooling-system', 5),
  ('radiator-cap', 'Radiator cap', 'Radiator cap', 'engine-cooling-system', 6),
  ('radiator-mounting-parts', 'Radiator mounting parts', 'Radiator mounting parts', 'engine-cooling-system', 7),
  ('radiator-gasket', 'Radiator gasket', 'Radiator gasket', 'engine-cooling-system', 8),
  ('timing-belt-and-water-pump', 'Timing belt and water pump', 'Timing belt and water pump', 'engine-cooling-system', 9),
  ('poly-v-belt-kit', 'Poly v-belt kit', 'Poly v-belt kit', 'engine-cooling-system', 10),
  ('coolant-pipe-seal', 'Coolant pipe seal', 'Coolant pipe seal', 'engine-cooling-system', 11),
  ('expansion-tank-cap', 'Expansion tank cap', 'Expansion tank cap', 'engine-cooling-system', 12),
  ('radiator-hose', 'Radiator hose', 'Radiator hose', 'engine-cooling-system', 13),
  ('heater-core-hose', 'Heater core hose', 'Heater core hose', 'engine-cooling-system', 14)
ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  display_name = EXCLUDED.display_name,
  category_id = EXCLUDED.category_id,
  sort_order = EXCLUDED.sort_order;

-- Water pumps and related components (4 subcategories)
INSERT INTO subcategories (id, name, display_name, category_id, sort_order)
VALUES 
  ('seal-thermal-switch', 'Seal, thermal switch', 'Seal, thermal switch', 'engine-cooling-system', 15),
  ('hose-transmission-oil-cooler', 'Hose, transmission oil cooler', 'Hose, transmission oil cooler', 'engine-cooling-system', 16),
  ('water-pump-gasket', 'Water pump gasket', 'Water pump gasket', 'engine-cooling-system', 17),
  ('core-plug', 'Core plug', 'Core plug', 'engine-cooling-system', 18)
ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  display_name = EXCLUDED.display_name,
  category_id = EXCLUDED.category_id,
  sort_order = EXCLUDED.sort_order;

-- Water pump components (3 subcategories)
INSERT INTO subcategories (id, name, display_name, category_id, sort_order)
VALUES
  ('water-pump', 'Water pump', 'Water pump', 'engine-cooling-system', 19),
  ('water-pump-pulley', 'Water pump pulley', 'Water pump pulley', 'engine-cooling-system', 20),
  ('intercooler-cooling', 'Intercooler', 'Intercooler', 'engine-cooling-system', 21)
ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  display_name = EXCLUDED.display_name,
  category_id = EXCLUDED.category_id,
  sort_order = EXCLUDED.sort_order;

-- Coolants and related components (3 subcategories)
INSERT INTO subcategories (id, name, display_name, category_id, sort_order)
VALUES
  ('engine-coolant', 'Engine coolant', 'Engine coolant', 'engine-cooling-system', 22),
  ('coolant-circuit-seals', 'Coolant circuit seals', 'Coolant circuit seals', 'engine-cooling-system', 23),
  ('distilled-water', 'Distilled water', 'Distilled water', 'engine-cooling-system', 24)
ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  display_name = EXCLUDED.display_name,
  category_id = EXCLUDED.category_id,
  sort_order = EXCLUDED.sort_order;

-- Cooling system sensors (5 subcategories)
INSERT INTO subcategories (id, name, display_name, category_id, sort_order)
VALUES
  ('coolant-temperature-sensor-cooling', 'Coolant temperature sensor', 'Coolant temperature sensor', 'engine-cooling-system', 25),
  ('coolant-level-sensor-cooling', 'Coolant level sensor', 'Coolant level sensor', 'engine-cooling-system', 26),
  ('gasket-coolant-flange', 'Gasket, coolant flange', 'Gasket, coolant flange', 'engine-cooling-system', 27),
  ('crowling-radiator-fan', 'Crowling, radiator fan', 'Crowling, radiator fan', 'engine-cooling-system', 28),
  ('oil-temperature-sensor-cooling', 'Oil temperature sensor', 'Oil temperature sensor', 'engine-cooling-system', 29)
ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  display_name = EXCLUDED.display_name,
  category_id = EXCLUDED.category_id,
  sort_order = EXCLUDED.sort_order;

-- Engine cooling fans and related components (8 subcategories)
INSERT INTO subcategories (id, name, display_name, category_id, sort_order)
VALUES
  ('radiator-fan', 'Radiator fan', 'Radiator fan', 'engine-cooling-system', 30),
  ('radiator-fan-switch', 'Radiator fan switch', 'Radiator fan switch', 'engine-cooling-system', 31),
  ('fan-clutch', 'Fan clutch', 'Fan clutch', 'engine-cooling-system', 32),
  ('radiator-fan-blade', 'Radiator fan blade', 'Radiator fan blade', 'engine-cooling-system', 33),
  ('support-cooling-fan', 'Support, cooling fan', 'Support, cooling fan', 'engine-cooling-system', 34),
  ('seal-ring-for-coolant-pipe', 'Seal ring for coolant pipe', 'Seal ring for coolant pipe', 'engine-cooling-system', 35),
  ('cooling-fan-relay', 'Cooling fan relay', 'Cooling fan relay', 'engine-cooling-system', 36),
  ('oil-hose', 'Oil hose', 'Oil hose', 'engine-cooling-system', 37)
ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  display_name = EXCLUDED.display_name,
  category_id = EXCLUDED.category_id,
  sort_order = EXCLUDED.sort_order;

-- Radiator fan motor (1 subcategory)
INSERT INTO subcategories (id, name, display_name, category_id, sort_order)
VALUES
  ('radiator-fan-motor', 'Radiator fan motor', 'Radiator fan motor', 'engine-cooling-system', 38)
ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  display_name = EXCLUDED.display_name,
  category_id = EXCLUDED.category_id,
  sort_order = EXCLUDED.sort_order;

-- Oil coolers and related components (4 subcategories)
INSERT INTO subcategories (id, name, display_name, category_id, sort_order)
VALUES
  ('oil-cooler', 'Oil cooler', 'Oil cooler', 'engine-cooling-system', 39),
  ('oil-cooler-gasket', 'Oil cooler gasket', 'Oil cooler gasket', 'engine-cooling-system', 40),
  ('fan-control-module', 'Fan control module', 'Fan control module', 'engine-cooling-system', 41),
  ('oil-cooler-thermostat', 'Oil cooler thermostat', 'Oil cooler thermostat', 'engine-cooling-system', 42)
ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  display_name = EXCLUDED.display_name,
  category_id = EXCLUDED.category_id,
  sort_order = EXCLUDED.sort_order;

-- Tools (1 subcategory)
INSERT INTO subcategories (id, name, display_name, category_id, sort_order)
VALUES
  ('cooling-system-tools', 'Cooling system tools', 'Cooling system tools', 'engine-cooling-system', 43)
ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  display_name = EXCLUDED.display_name,
  category_id = EXCLUDED.category_id,
  sort_order = EXCLUDED.sort_order;

-- Car Care (4 subcategories)
INSERT INTO subcategories (id, name, display_name, category_id, sort_order)
VALUES
  ('radiator-flush-cleaners', 'Radiator flush & cleaners', 'Radiator flush & cleaners', 'engine-cooling-system', 44),
  ('radiator-sealants', 'Radiator sealants', 'Radiator sealants', 'engine-cooling-system', 45),
  ('flange-sealants', 'Flange sealants', 'Flange sealants', 'engine-cooling-system', 46),
  ('all-purpose-sealants', 'All-purpose sealants', 'All-purpose sealants', 'engine-cooling-system', 47)
ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  display_name = EXCLUDED.display_name,
  category_id = EXCLUDED.category_id,
  sort_order = EXCLUDED.sort_order;
