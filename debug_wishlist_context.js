import { createClient } from '@supabase/supabase-js';

// Supabase configuration
const supabaseUrl = 'https://irkwpzcskeqtasutqnxp.supabase.co';
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imlya3dwemNza2VxdGFzdXRxbnhwIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDc0OTg2ODMsImV4cCI6MjA2MzA3NDY4M30.bUv9zxx9q_sVUwv_-G6WbHsA53-SU5tCCWbOS_5Xp94';

const supabase = createClient(supabaseUrl, supabaseAnonKey);

async function debugWishlistContext() {
  try {
    console.log('🔍 Debugging wishlist context...');

    const testPhone = '+213555123456';
    
    // Step 1: Set the context
    console.log('📋 Step 1: Setting consumer phone context...');
    const { data: setResult, error: setError } = await supabase.rpc('set_app_config', {
      setting_name: 'app.consumer_phone',
      setting_value: testPhone,
      is_local: false
    });

    if (setError) {
      console.error('❌ Error setting context:', setError);
      return;
    }
    console.log('✅ Context set result:', setResult);

    // Step 2: Check if the context is actually set
    console.log('📋 Step 2: Checking current context...');
    const { data: checkResult, error: checkError } = await supabase.rpc('exec_sql', {
      sql_query: "SELECT current_setting('app.consumer_phone', true) as consumer_phone;"
    });

    if (checkError) {
      console.error('❌ Error checking context:', checkError);
    } else {
      console.log('✅ Current context:', checkResult);
    }

    // Step 3: Check current policies
    console.log('📋 Step 3: Checking current wishlist policies...');
    const { data: policies, error: policyError } = await supabase.rpc('exec_sql', {
      sql_query: `SELECT policyname, cmd, qual, with_check 
                  FROM pg_policies 
                  WHERE tablename = 'consumer_wishlists' 
                  ORDER BY policyname;`
    });

    if (policyError) {
      console.error('❌ Error checking policies:', policyError);
    } else {
      console.log('✅ Current policies:');
      console.table(policies);
    }

    // Step 4: Test a simple query to see what happens
    console.log('📋 Step 4: Testing simple select...');
    const { data: selectData, error: selectError } = await supabase
      .from('consumer_wishlists')
      .select('*')
      .limit(1);

    if (selectError) {
      console.error('❌ Select error:', selectError);
    } else {
      console.log('✅ Select works, found items:', selectData?.length || 0);
    }

    // Step 5: Test insert with detailed error info
    console.log('📋 Step 5: Testing insert with debug info...');
    
    // First, let's see what the current setting returns in the context of an insert
    const { data: debugInsert, error: debugError } = await supabase.rpc('exec_sql', {
      sql_query: `
        DO $$
        DECLARE
          current_phone TEXT;
          test_phone TEXT := '${testPhone}';
        BEGIN
          current_phone := current_setting('app.consumer_phone', true);
          RAISE NOTICE 'Current setting value: %', current_phone;
          RAISE NOTICE 'Test phone value: %', test_phone;
          RAISE NOTICE 'Values match: %', (current_phone = test_phone);
        END $$;
      `
    });

    if (debugError) {
      console.error('❌ Debug error:', debugError);
    } else {
      console.log('✅ Debug completed');
    }

    // Step 6: Try the actual insert
    console.log('📋 Step 6: Attempting insert...');
    const { data: insertData, error: insertError } = await supabase
      .from('consumer_wishlists')
      .insert({
        consumer_phone: testPhone,
        product_id: 'DEBUG-TEST-001',
        product_name: 'Debug Test Product',
        priority: 1
      })
      .select()
      .single();

    if (insertError) {
      console.error('❌ Insert error:', insertError);
      console.error('Error code:', insertError.code);
      console.error('Error details:', insertError.details);
      console.error('Error hint:', insertError.hint);
    } else {
      console.log('✅ Insert successful:', insertData);
      
      // Clean up
      await supabase
        .from('consumer_wishlists')
        .delete()
        .eq('product_id', 'DEBUG-TEST-001');
    }

  } catch (error) {
    console.error('❌ Error in debug:', error);
  }
}

// Run the debug
debugWishlistContext();
