-- INCREMENTAL FIX: Only add what's missing after partial run
-- Run this directly in Supabase SQL Editor 
-- Date: July 8, 2025

-- 1. First, let's check what policies currently exist
SELECT schemaname, tablename, policyname 
FROM pg_policies 
WHERE tablename IN ('profiles', 'products', 'wholesale_pricing_tiers', 'tyre_specifications', 'vehicle_compatibility')
ORDER BY tablename, policyname;

-- 2. MARKETPLACE PRODUCT LOADING FIXES (This is what was missing from your first run)
-- The problem: Restrictive RLS policies on products table are blocking marketplace access
-- Solution: Replace restrictive policies with hybrid policies

-- Drop conflicting restrictive product policies that block marketplace access
DROP POLICY IF EXISTS "Users can only see their own products" ON products;

-- Create new policy that allows BOTH user access AND marketplace access
CREATE POLICY "products_user_and_marketplace_access" ON products
  FOR SELECT USING (
    -- Allow users to see their own products (admin panel access)
    auth.uid() = user_id
    OR
    -- Allow marketplace access to active/out_of_stock products (marketplace access)
    status IN ('active', 'out_of_stock')
  );

-- Ensure wholesale pricing tiers are accessible for marketplace
DROP POLICY IF EXISTS "Users can only see pricing for their own products" ON wholesale_pricing_tiers;

CREATE POLICY "pricing_tiers_user_and_marketplace_access" ON wholesale_pricing_tiers
  FOR SELECT USING (
    -- Allow users to see pricing for their own products
    EXISTS (
      SELECT 1 FROM products
      WHERE products.id = wholesale_pricing_tiers.product_id
      AND products.user_id = auth.uid()
    )
    OR
    -- Allow marketplace access to pricing for active/out_of_stock products
    EXISTS (
      SELECT 1 FROM products
      WHERE products.id = wholesale_pricing_tiers.product_id
      AND products.status IN ('active', 'out_of_stock')
    )
  );

-- Ensure tyre specifications are accessible for marketplace
DROP POLICY IF EXISTS "Users can only see tyre specs for their own products" ON tyre_specifications;

CREATE POLICY "tyre_specs_user_and_marketplace_access" ON tyre_specifications
  FOR SELECT USING (
    -- Allow users to see specs for their own products
    EXISTS (
      SELECT 1 FROM products
      WHERE products.id = tyre_specifications.product_id
      AND products.user_id = auth.uid()
    )
    OR
    -- Allow marketplace access to specs for active/out_of_stock products
    EXISTS (
      SELECT 1 FROM products
      WHERE products.id = tyre_specifications.product_id
      AND products.status IN ('active', 'out_of_stock')
    )
  );

-- Ensure vehicle compatibility is accessible for marketplace
DROP POLICY IF EXISTS "Users can only see vehicle compatibility for their own products" ON vehicle_compatibility;

CREATE POLICY "vehicle_compat_user_and_marketplace_access" ON vehicle_compatibility
  FOR SELECT USING (
    -- Allow users to see compatibility for their own products
    EXISTS (
      SELECT 1 FROM products
      WHERE products.id = vehicle_compatibility.product_id
      AND products.user_id = auth.uid()
    )
    OR
    -- Allow marketplace access to compatibility for active/out_of_stock products
    EXISTS (
      SELECT 1 FROM products
      WHERE products.id = vehicle_compatibility.product_id
      AND products.status IN ('active', 'out_of_stock')
    )
  );

-- 3. Add comments for the new marketplace policies
COMMENT ON POLICY "products_user_and_marketplace_access" ON products IS 
'HYBRID POLICY: Allows users to see their own products AND marketplace access to active/out_of_stock products';

COMMENT ON POLICY "pricing_tiers_user_and_marketplace_access" ON wholesale_pricing_tiers IS 
'HYBRID POLICY: Allows user access to own pricing AND marketplace access to active/out_of_stock product pricing';

COMMENT ON POLICY "tyre_specs_user_and_marketplace_access" ON tyre_specifications IS 
'HYBRID POLICY: Allows user access to own specs AND marketplace access to active/out_of_stock product specs';

COMMENT ON POLICY "vehicle_compat_user_and_marketplace_access" ON vehicle_compatibility IS 
'HYBRID POLICY: Allows user access to own compatibility AND marketplace access to active/out_of_stock product compatibility';

-- 4. Test queries to verify everything works
SELECT 'ADMIN_ACCESS_TEST: Admin profiles accessible' as test_result, 
       count(*) as admin_count,
       string_agg(email, ', ') as admin_emails
FROM profiles 
WHERE role IN ('admin', 'merchant', 'supplier') 
LIMIT 10;

SELECT 'CONSUMER_ACCESS_TEST: Consumer profiles accessible' as test_result, 
       count(*) as consumer_count
FROM profiles 
WHERE role = 'consumer' 
LIMIT 5;

SELECT 'MARKETPLACE_PRODUCTS_TEST: Active products accessible' as test_result, 
       count(*) as active_product_count
FROM products 
WHERE status IN ('active', 'out_of_stock')
LIMIT 10;

SELECT 'WHOLESALE_PRICING_TEST: Pricing tiers accessible' as test_result, 
       count(*) as pricing_tier_count
FROM wholesale_pricing_tiers wpt
JOIN products p ON p.id = wpt.product_id
WHERE p.status IN ('active', 'out_of_stock')
LIMIT 10;

-- 5. Final verification - show current policy status
SELECT 'POLICY_STATUS: Current policies after fix' as status,
       schemaname, tablename, policyname 
FROM pg_policies 
WHERE tablename IN ('profiles', 'products', 'wholesale_pricing_tiers', 'tyre_specifications', 'vehicle_compatibility')
ORDER BY tablename, policyname;
