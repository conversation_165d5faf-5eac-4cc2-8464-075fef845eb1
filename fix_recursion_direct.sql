-- DIRECT FIX: Remove infinite recursion in profiles RLS policies
-- This fixes the "infinite recursion detected in policy for relation 'profiles'" error
-- Run this directly in Supabase SQL Editor
-- Date: July 8, 2025

-- STEP 1: Drop ALL policies on profiles table to stop the recursion
-- This is safe because we'll recreate proper policies immediately after
DROP POLICY IF EXISTS "profiles_authenticated_select_admin_merchant_supplier" ON profiles;
DROP POLICY IF EXISTS "profiles_authenticated_update_admin_merchant_supplier" ON profiles;
DROP POLICY IF EXISTS "profiles_authenticated_insert_admin_merchant_supplier" ON profiles;
DROP POLICY IF EXISTS "profiles_admin_emergency_access" ON profiles;
DROP POLICY IF EXISTS "profiles_consumer_anonymous_select" ON profiles;
DROP POLICY IF EXISTS "profiles_consumer_anonymous_insert" ON profiles;
DROP POLICY IF EXISTS "profiles_consumer_anonymous_update" ON profiles;
DROP POLICY IF EXISTS "profiles_authenticated_select_non_consumer" ON profiles;
DROP POLICY IF EXISTS "profiles_authenticated_update_non_consumer" ON profiles;
DROP POLICY IF EXISTS "profiles_authenticated_insert_non_consumer" ON profiles;
DROP POLICY IF EXISTS "profiles_service_role_all_access" ON profiles;
DROP POLICY IF EXISTS "service_role_all_access" ON profiles;
DROP POLICY IF EXISTS "users_select_own_profile" ON profiles;
DROP POLICY IF EXISTS "users_update_own_profile" ON profiles;
DROP POLICY IF EXISTS "users_insert_own_profile" ON profiles;

-- STEP 2: Create simple, NON-RECURSIVE policies that work
-- These policies do NOT reference the profiles table in their conditions

-- Service role access (for system operations)
CREATE POLICY "service_role_all_access" ON profiles
  FOR ALL
  USING (auth.role() = 'service_role')
  WITH CHECK (auth.role() = 'service_role');

-- Authenticated users can access their own profile (NO RECURSION)
CREATE POLICY "users_select_own_profile" ON profiles
  FOR SELECT
  USING (auth.uid() = id);

CREATE POLICY "users_update_own_profile" ON profiles
  FOR UPDATE
  USING (auth.uid() = id)
  WITH CHECK (auth.uid() = id);

CREATE POLICY "users_insert_own_profile" ON profiles
  FOR INSERT
  WITH CHECK (auth.uid() = id);

-- Consumer anonymous access (for phone-based authentication)
CREATE POLICY "profiles_consumer_anonymous_select" ON profiles
  FOR SELECT 
  USING (role = 'consumer');

CREATE POLICY "profiles_consumer_anonymous_insert" ON profiles
  FOR INSERT 
  WITH CHECK (role = 'consumer');

CREATE POLICY "profiles_consumer_anonymous_update" ON profiles
  FOR UPDATE 
  USING (role = 'consumer')
  WITH CHECK (role = 'consumer');

-- Emergency admin access using auth.users (NO RECURSION)
CREATE POLICY "admin_emergency_access" ON profiles
  FOR ALL
  USING (
    EXISTS (
      SELECT 1 FROM auth.users 
      WHERE auth.users.id = auth.uid()
      AND auth.users.email IN (
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>'
      )
    )
  )
  WITH CHECK (
    EXISTS (
      SELECT 1 FROM auth.users 
      WHERE auth.users.id = auth.uid()
      AND auth.users.email IN (
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>'
      )
    )
  );

-- STEP 3: Ensure RLS is enabled
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;

-- STEP 4: Test that the fix worked
-- These queries should now work without infinite recursion
SELECT 'RECURSION_FIX_TEST: Profile count' as test_name, count(*) as result FROM profiles;

SELECT 'ADMIN_ACCESS_TEST: Admin profiles' as test_name, count(*) as result 
FROM profiles WHERE role IN ('admin', 'merchant', 'supplier');

SELECT 'CONSUMER_ACCESS_TEST: Consumer profiles' as test_name, count(*) as result 
FROM profiles WHERE role = 'consumer';

-- STEP 5: Verify the policies were created correctly
SELECT 'POLICY_VERIFICATION: New policies created' as test_name, count(*) as policy_count
FROM pg_policies 
WHERE tablename = 'profiles' 
AND schemaname = 'public';

-- STEP 6: Success message
SELECT 'SUCCESS: Infinite recursion fixed!' as status, 
       'Admin panel should now work properly' as message,
       now() as timestamp;
