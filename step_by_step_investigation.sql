-- STEP-BY-STEP INVESTIGATION: Run each query separately
-- Copy and paste each query ONE AT A TIME into Supabase SQL Editor
-- This will help us see exactly where things are failing

-- STEP 1: Check what RLS policies exist on profiles table
-- Copy and run this query first:
SELECT 
  tablename,
  policyname,
  cmd as command_type,
  qual as using_condition
FROM pg_policies 
WHERE schemaname = 'public'
AND tablename = 'profiles'
ORDER BY policyname;

-- STEP 2: Look for recursive patterns in profiles policies
-- Copy and run this query second:
SELECT 
  policyname,
  'RECURSIVE_PATTERN_FOUND' as issue_type,
  qual as problematic_condition
FROM pg_policies 
WHERE schemaname = 'public'
AND tablename = 'profiles'
AND qual LIKE '%EXISTS%SELECT%FROM profiles%';

-- STEP 3: Check auth.users table (should always work)
-- Copy and run this query third:
SELECT 
  id,
  email,
  created_at
FROM auth.users 
WHERE email LIKE '%arouzmarket%' OR email LIKE '%hamza%'
OR<PERSON><PERSON> BY created_at DESC
LIMIT 3;

-- STEP 4: Test if we can count profiles (might fail due to RLS)
-- Copy and run this query fourth:
SELECT count(*) as profile_count FROM profiles;

-- STEP 5: Check what products policies exist
-- Copy and run this query fifth:
SELECT 
  policyname,
  cmd as command_type,
  qual as using_condition
FROM pg_policies 
WHERE schemaname = 'public'
AND tablename = 'products'
ORDER BY policyname;

-- STEP 6: Test if we can count products (might fail due to RLS)
-- Copy and run this query sixth:
SELECT count(*) as product_count FROM products;
