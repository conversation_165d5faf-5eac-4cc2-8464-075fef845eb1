-- =====================================================================
-- 🔍 COMPLETE SUPABASE RLS & POLICIES AUDIT SCRIPT
-- =====================================================================
-- This script will show you EVERYTHING about your Supabase structure
-- Copy and paste this into Supabase SQL Editor to get complete visibility

-- SECTION 1: DATABASE OVERVIEW
-- =====================================================================
SELECT '🗄️  DATABASE OVERVIEW' as section_title;

-- Show all schemas
SELECT 
  '=== SCHEMAS ===' as info,
  schema_name,
  schema_owner
FROM information_schema.schemata 
WHERE schema_name NOT IN ('information_schema', 'pg_catalog', 'pg_toast')
ORDER BY schema_name;

-- SECTION 2: ALL TABLES WITH RLS STATUS
-- =====================================================================
SELECT '📋 ALL TABLES WITH RLS STATUS' as section_title;

SELECT 
  '=== TABLES & RLS STATUS ===' as info,
  schemaname,
  tablename,
  CASE 
    WHEN rowsecurity = true THEN '🔒 RLS ENABLED'
    ELSE '🔓 RLS DISABLED'
  END as rls_status,
  tableowner
FROM pg_tables 
WHERE schemaname = 'public'
ORDER BY tablename;

-- SECTION 3: COMPLETE RLS POLICIES BREAKDOWN
-- =====================================================================
SELECT '🛡️  COMPLETE RLS POLICIES BREAKDOWN' as section_title;

-- Show ALL policies on ALL tables
SELECT 
  '=== ALL RLS POLICIES ===' as info,
  schemaname,
  tablename,
  policyname,
  cmd as command_type,
  permissive,
  roles,
  qual as using_clause,
  with_check
FROM pg_policies 
WHERE schemaname = 'public'
ORDER BY tablename, policyname;

-- SECTION 4: PROFILES TABLE DETAILED ANALYSIS
-- =====================================================================
SELECT '👤 PROFILES TABLE DETAILED ANALYSIS' as section_title;

-- Profiles table structure
SELECT 
  '=== PROFILES TABLE COLUMNS ===' as info,
  column_name,
  data_type,
  is_nullable,
  column_default
FROM information_schema.columns 
WHERE table_schema = 'public' 
AND table_name = 'profiles'
ORDER BY ordinal_position;

-- Profiles table RLS status
SELECT 
  '=== PROFILES RLS STATUS ===' as info,
  tablename,
  CASE 
    WHEN rowsecurity = true THEN '🔒 RLS ENABLED'
    ELSE '🔓 RLS DISABLED'
  END as rls_status
FROM pg_tables 
WHERE schemaname = 'public' 
AND tablename = 'profiles';

-- Profiles table policies (detailed)
SELECT 
  '=== PROFILES POLICIES DETAILED ===' as info,
  policyname,
  cmd as command_type,
  CASE 
    WHEN permissive = 'PERMISSIVE' THEN '✅ PERMISSIVE'
    ELSE '❌ RESTRICTIVE'
  END as policy_type,
  roles,
  qual as using_clause,
  with_check
FROM pg_policies 
WHERE schemaname = 'public' 
AND tablename = 'profiles'
ORDER BY policyname;

-- SECTION 5: AUTH SYSTEM ANALYSIS
-- =====================================================================
SELECT '🔐 AUTH SYSTEM ANALYSIS' as section_title;

-- Auth users table access
SELECT 
  '=== AUTH.USERS TABLE INFO ===' as info,
  'Checking if we can access auth.users...' as status;

-- Try to count auth users (this might fail if no access)
DO $$
BEGIN
  BEGIN
    PERFORM count(*) FROM auth.users;
    RAISE NOTICE 'SUCCESS: Can access auth.users table';
  EXCEPTION WHEN OTHERS THEN
    RAISE NOTICE 'ERROR: Cannot access auth.users table - %', SQLERRM;
  END;
END $$;

-- SECTION 6: PRODUCTS TABLE ANALYSIS
-- =====================================================================
SELECT '📦 PRODUCTS TABLE ANALYSIS' as section_title;

-- Products table RLS status
SELECT 
  '=== PRODUCTS RLS STATUS ===' as info,
  tablename,
  CASE 
    WHEN rowsecurity = true THEN '🔒 RLS ENABLED'
    ELSE '🔓 RLS DISABLED'
  END as rls_status
FROM pg_tables 
WHERE schemaname = 'public' 
AND tablename = 'products';

-- Products table policies
SELECT 
  '=== PRODUCTS POLICIES ===' as info,
  policyname,
  cmd as command_type,
  qual as using_clause,
  with_check
FROM pg_policies 
WHERE schemaname = 'public' 
AND tablename = 'products'
ORDER BY policyname;

-- SECTION 7: ORDERS TABLE ANALYSIS
-- =====================================================================
SELECT '🛒 ORDERS TABLE ANALYSIS' as section_title;

-- Orders table RLS status
SELECT 
  '=== ORDERS RLS STATUS ===' as info,
  tablename,
  CASE 
    WHEN rowsecurity = true THEN '🔒 RLS ENABLED'
    ELSE '🔓 RLS DISABLED'
  END as rls_status
FROM pg_tables 
WHERE schemaname = 'public' 
AND tablename = 'orders';

-- Orders table policies
SELECT 
  '=== ORDERS POLICIES ===' as info,
  policyname,
  cmd as command_type,
  qual as using_clause,
  with_check
FROM pg_policies 
WHERE schemaname = 'public' 
AND tablename = 'orders'
ORDER BY policyname;

-- SECTION 8: ALL OTHER TABLES WITH RLS
-- =====================================================================
SELECT '📊 ALL OTHER TABLES WITH RLS' as section_title;

-- Show all tables that have RLS enabled
SELECT 
  '=== TABLES WITH RLS ENABLED ===' as info,
  tablename,
  (SELECT count(*) FROM pg_policies WHERE pg_policies.tablename = pg_tables.tablename) as policy_count
FROM pg_tables 
WHERE schemaname = 'public' 
AND rowsecurity = true
ORDER BY tablename;

-- SECTION 9: ROLE PERMISSIONS
-- =====================================================================
SELECT '👥 ROLE PERMISSIONS' as section_title;

-- Show current role
SELECT 
  '=== CURRENT ROLE ===' as info,
  current_user as current_role,
  session_user as session_role;

-- Show available roles
SELECT 
  '=== AVAILABLE ROLES ===' as info,
  rolname as role_name,
  rolsuper as is_superuser,
  rolcreaterole as can_create_roles,
  rolcreatedb as can_create_db
FROM pg_roles 
WHERE rolname IN ('postgres', 'anon', 'authenticated', 'service_role')
ORDER BY rolname;

-- SECTION 10: FUNCTIONS AND TRIGGERS
-- =====================================================================
SELECT '⚙️  FUNCTIONS AND TRIGGERS' as section_title;

-- Show custom functions
SELECT 
  '=== CUSTOM FUNCTIONS ===' as info,
  routine_name,
  routine_type,
  data_type as return_type
FROM information_schema.routines 
WHERE routine_schema = 'public'
ORDER BY routine_name;

-- Show triggers
SELECT 
  '=== TRIGGERS ===' as info,
  trigger_name,
  event_manipulation,
  event_object_table,
  action_timing
FROM information_schema.triggers 
WHERE trigger_schema = 'public'
ORDER BY event_object_table, trigger_name;

-- SECTION 11: SUMMARY REPORT
-- =====================================================================
SELECT '📋 SUMMARY REPORT' as section_title;

-- Count everything
SELECT 
  '=== SUMMARY COUNTS ===' as info,
  (SELECT count(*) FROM pg_tables WHERE schemaname = 'public') as total_tables,
  (SELECT count(*) FROM pg_tables WHERE schemaname = 'public' AND rowsecurity = true) as tables_with_rls,
  (SELECT count(*) FROM pg_policies WHERE schemaname = 'public') as total_policies,
  (SELECT count(DISTINCT tablename) FROM pg_policies WHERE schemaname = 'public') as tables_with_policies;

-- Show potential issues
SELECT 
  '=== POTENTIAL ISSUES ===' as info,
  CASE 
    WHEN (SELECT count(*) FROM pg_policies WHERE schemaname = 'public' AND tablename = 'profiles') = 0 
    THEN '⚠️  NO POLICIES ON PROFILES TABLE'
    ELSE '✅ PROFILES TABLE HAS POLICIES'
  END as profiles_status,
  CASE 
    WHEN (SELECT rowsecurity FROM pg_tables WHERE schemaname = 'public' AND tablename = 'profiles') = false
    THEN '⚠️  RLS DISABLED ON PROFILES'
    ELSE '✅ RLS ENABLED ON PROFILES'
  END as profiles_rls_status;

-- SECTION 12: DETAILED POLICY ANALYSIS
-- =====================================================================
SELECT '🔬 DETAILED POLICY ANALYSIS' as section_title;

-- Show policy definitions with explanations
SELECT
  '=== POLICY DEFINITIONS EXPLAINED ===' as info,
  tablename,
  policyname,
  cmd,
  CASE
    WHEN qual IS NULL THEN 'No USING clause (applies to all rows)'
    ELSE 'USING: ' || qual
  END as using_explanation,
  CASE
    WHEN with_check IS NULL THEN 'No WITH CHECK clause'
    ELSE 'WITH CHECK: ' || with_check
  END as with_check_explanation
FROM pg_policies
WHERE schemaname = 'public'
ORDER BY tablename, policyname;

-- SECTION 13: SECURITY ANALYSIS
-- =====================================================================
SELECT '🔒 SECURITY ANALYSIS' as section_title;

-- Check for potentially problematic policies
SELECT
  '=== POTENTIAL SECURITY ISSUES ===' as info,
  tablename,
  policyname,
  CASE
    WHEN qual LIKE '%auth.users%' THEN '⚠️  REFERENCES AUTH.USERS (potential recursion)'
    WHEN qual IS NULL THEN '⚠️  NO RESTRICTIONS (allows all access)'
    WHEN qual = 'true' THEN '⚠️  ALWAYS TRUE (allows all access)'
    ELSE '✅ LOOKS SECURE'
  END as security_assessment,
  qual as policy_condition
FROM pg_policies
WHERE schemaname = 'public'
ORDER BY
  CASE
    WHEN qual LIKE '%auth.users%' THEN 1
    WHEN qual IS NULL THEN 2
    WHEN qual = 'true' THEN 3
    ELSE 4
  END,
  tablename;

-- SECTION 14: FINAL RECOMMENDATIONS
-- =====================================================================
SELECT '💡 FINAL RECOMMENDATIONS' as section_title;

-- Generate specific recommendations
SELECT
  '=== RECOMMENDATIONS ===' as info,
  CASE
    WHEN (SELECT count(*) FROM pg_policies WHERE schemaname = 'public' AND tablename = 'profiles' AND qual LIKE '%auth.users%') > 0
    THEN '🚨 URGENT: Remove auth.users references from profiles policies'
    WHEN (SELECT count(*) FROM pg_policies WHERE schemaname = 'public' AND tablename = 'profiles') = 0
    THEN '🚨 URGENT: Add RLS policies to profiles table'
    WHEN (SELECT rowsecurity FROM pg_tables WHERE schemaname = 'public' AND tablename = 'profiles') = false
    THEN '🚨 URGENT: Enable RLS on profiles table'
    ELSE '✅ Profiles table security looks good'
  END as profiles_recommendation;

SELECT '🎉 COMPLETE AUDIT FINISHED! 🎉' as final_message;
SELECT 'Copy all results and analyze them carefully!' as instruction;
