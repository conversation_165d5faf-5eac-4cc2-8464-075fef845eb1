import { createClient } from '@supabase/supabase-js';

const supabaseUrl = 'https://irkwpzcskeqtasutqnxp.supabase.co';
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imlya3dwemNza2VxdGFzdXRxbnhwIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDc0OTg2ODMsImV4cCI6MjA2MzA3NDY4M30.bUv9zxx9q_sVUwv_-G6WbHsA53-SU5tCCWbOS_5Xp94';

// This simulates the frontend client
const supabase = createClient(supabaseUrl, supabaseAnonKey);

async function createTestAdmin() {
  console.log('🔧 Creating Test Admin User...\n');

  try {
    // Step 1: Create a new admin user
    console.log('1. Creating new admin user...');
    
    const testEmail = '<EMAIL>';
    const testPassword = 'TestAdmin123!';
    
    const { data: signupData, error: signupError } = await supabase.auth.signUp({
      email: testEmail,
      password: testPassword,
      options: {
        data: {
          role: 'supplier',
          fullName: 'Test Admin User',
          companyName: 'Test Company'
        }
      }
    });

    if (signupError) {
      console.error('❌ Signup failed:', signupError);
      
      // If user already exists, try to sign in
      if (signupError.message.includes('already registered')) {
        console.log('User already exists, trying to sign in...');
        
        const { data: loginData, error: loginError } = await supabase.auth.signInWithPassword({
          email: testEmail,
          password: testPassword
        });

        if (loginError) {
          console.error('❌ Login also failed:', loginError);
          return;
        }

        console.log('✅ Login successful with existing user');
        await testProfileAccess(loginData.user);
        return;
      }
      return;
    }

    console.log('✅ Signup successful:', {
      user_id: signupData.user?.id,
      email: signupData.user?.email
    });

    // Step 2: Test profile access immediately
    if (signupData.user) {
      await testProfileAccess(signupData.user);
    }

  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

async function testProfileAccess(user) {
  console.log('\n2. Testing profile access...');
  
  // Wait a moment for the trigger to create the profile
  await new Promise(resolve => setTimeout(resolve, 1000));
  
  const { data: profileData, error: profileError } = await supabase
    .from('profiles')
    .select('*')
    .eq('id', user.id)
    .single();

  if (profileError) {
    console.error('❌ Profile access failed:', profileError);
    console.log('🔍 This is the exact error the frontend gets!');
    
    // Let's also check what the current session looks like
    const { data: sessionData } = await supabase.auth.getSession();
    console.log('Current session:', {
      user_id: sessionData.session?.user?.id,
      access_token_present: !!sessionData.session?.access_token,
      role: sessionData.session?.user?.role
    });
    
  } else {
    console.log('✅ Profile access successful:', {
      id: profileData.id,
      email: profileData.email,
      role: profileData.role,
      full_name: profileData.full_name
    });
  }

  // Test updating the profile (this is what happens during signup)
  console.log('\n3. Testing profile update...');
  
  const { data: updateData, error: updateError } = await supabase
    .from('profiles')
    .update({
      full_name: 'Updated Test Admin',
      company_name: 'Updated Test Company',
      last_login: new Date().toISOString()
    })
    .eq('id', user.id)
    .select();

  if (updateError) {
    console.error('❌ Profile update failed:', updateError);
  } else {
    console.log('✅ Profile update successful:', updateData);
  }
}

createTestAdmin();
