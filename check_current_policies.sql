-- Check current RLS policies on profiles table
SELECT 
    schemaname, 
    tablename, 
    policyname, 
    permissive, 
    roles, 
    cmd, 
    qual, 
    with_check 
FROM pg_policies 
WHERE tablename = 'profiles' 
ORDER BY policyname;

-- Check if R<PERSON> is enabled
SELECT schemaname, tablename, rowsecurity 
FROM pg_tables 
WHERE tablename = 'profiles';

-- Check current profiles in database
SELECT id, email, role, full_name, created_at 
FROM profiles 
ORDER BY created_at DESC 
LIMIT 10;
