import { createClient } from '@supabase/supabase-js';

// Supabase configuration
const supabaseUrl = 'https://irkwpzcskeqtasutqnxp.supabase.co';
const supabaseServiceKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imlya3dwemNza2VxdGFzdXRxbnhwIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0NzQ5ODY4MywiZXhwIjoyMDYzMDc0NjgzfQ.PLfCCbh4zgAJDDC6sS82fCU32JYA7e2VmwzvavF-PW8';
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imlya3dwemNza2VxdGFzdXRxbnhwIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDc0OTg2ODMsImV4cCI6MjA2MzA3NDY4M30.bUv9zxx9q_sVUwv_-G6WbHsA53-SU5tCCWbOS_5Xp94';

const supabaseService = createClient(supabaseUrl, supabaseServiceKey);
const supabaseAnon = createClient(supabaseUrl, supabaseAnonKey);

async function checkReviewsCurrentState() {
  try {
    console.log('🔍 CHECKING REVIEWS SYSTEM CURRENT STATE...');
    console.log('='.repeat(60));

    let allSystemsWorking = true;

    // CRITICAL TEST 1: Verify admin authentication still works (MUST NOT BREAK)
    console.log('\n🔒 CRITICAL TEST 1: Admin authentication');
    const { data: adminTest, error: adminError } = await supabaseService
      .from('profiles')
      .select('id, email, role')
      .eq('role', 'supplier')
      .limit(1);

    if (adminError) {
      console.error('❌ CRITICAL FAILURE: Admin auth broken!', adminError);
      allSystemsWorking = false;
    } else {
      console.log('✅ Admin authentication: WORKING');
    }

    // CRITICAL TEST 2: Verify consumer profiles still work (MUST NOT BREAK)
    console.log('\n👤 CRITICAL TEST 2: Consumer profiles');
    const { data: consumerTest, error: consumerError } = await supabaseService
      .from('profiles')
      .select('id, phone, role')
      .eq('role', 'consumer')
      .limit(1);

    if (consumerError) {
      console.error('❌ CRITICAL FAILURE: Consumer profiles broken!', consumerError);
      allSystemsWorking = false;
    } else {
      console.log('✅ Consumer profiles: WORKING');
    }

    // CRITICAL TEST 3: Verify wishlist still works (MUST NOT BREAK)
    console.log('\n💝 CRITICAL TEST 3: Wishlist functionality');
    const { data: wishlistTest, error: wishlistError } = await supabaseAnon
      .from('consumer_wishlists')
      .select('*')
      .limit(1);

    if (wishlistError) {
      console.error('❌ CRITICAL FAILURE: Wishlist broken!', wishlistError);
      allSystemsWorking = false;
    } else {
      console.log('✅ Wishlist functionality: WORKING');
    }

    // TEST 4: Check reviews table exists and structure
    console.log('\n📝 TEST 4: Reviews table structure');
    const { data: reviewsTableTest, error: reviewsTableError } = await supabaseService
      .from('consumer_reviews')
      .select('*')
      .limit(1);

    if (reviewsTableError) {
      console.error('❌ Reviews table access issue:', reviewsTableError);
      allSystemsWorking = false;
    } else {
      console.log('✅ Reviews table: ACCESSIBLE');
    }

    // TEST 5: Test service role can insert reviews (should work)
    console.log('\n📝 TEST 5: Service role reviews insert');
    const { data: serviceInsert, error: serviceInsertError } = await supabaseService
      .from('consumer_reviews')
      .insert({
        consumer_phone: '+213999999999',
        product_id: 'SAFETY-REVIEW-TEST-001',
        product_name: 'Safety Review Test Product',
        rating: 5,
        title: 'Safety Test Review',
        review_text: 'This is a safety test review'
      })
      .select()
      .single();

    if (serviceInsertError) {
      console.error('❌ Service role review insert failed:', serviceInsertError);
      allSystemsWorking = false;
    } else {
      console.log('✅ Service role can insert reviews');
      
      // Clean up immediately
      await supabaseService
        .from('consumer_reviews')
        .delete()
        .eq('product_id', 'SAFETY-REVIEW-TEST-001');
      console.log('✅ Test data cleaned up');
    }

    // TEST 6: Test anonymous role can read approved reviews (should work)
    console.log('\n📖 TEST 6: Anonymous reviews read');
    const { data: anonRead, error: anonReadError } = await supabaseAnon
      .from('consumer_reviews')
      .select('*')
      .eq('is_approved', true)
      .limit(1);

    if (anonReadError) {
      console.error('❌ Anonymous review read failed:', anonReadError);
      allSystemsWorking = false;
    } else {
      console.log('✅ Anonymous can read approved reviews');
    }

    // TEST 7: Test anonymous role INSERT (this is likely the problem)
    console.log('\n➕ TEST 7: Anonymous reviews insert (THE LIKELY ISSUE)');
    const testPhone = '+213555888777';
    const { data: anonInsert, error: anonInsertError } = await supabaseAnon
      .from('consumer_reviews')
      .insert({
        consumer_phone: testPhone,
        product_id: 'ANON-TEST-001',
        product_name: 'Anonymous Test Product',
        rating: 4,
        title: 'Test Review',
        review_text: 'This is a test review'
      })
      .select()
      .single();

    if (anonInsertError) {
      console.error('❌ ANONYMOUS REVIEW INSERT FAILED:', anonInsertError.message);
      console.error('❌ ERROR CODE:', anonInsertError.code);
      console.error('🎯 THIS IS LIKELY THE ROOT CAUSE OF THE REVIEWS ISSUE');
      allSystemsWorking = false;
    } else {
      console.log('✅ Anonymous can insert reviews');
      
      // Clean up
      await supabaseAnon
        .from('consumer_reviews')
        .delete()
        .eq('product_id', 'ANON-TEST-001');
      console.log('✅ Test data cleaned up');
    }

    // SUMMARY
    console.log('\n' + '='.repeat(60));
    console.log('🎯 REVIEWS SYSTEM STATE SUMMARY:');
    console.log('='.repeat(60));

    if (allSystemsWorking) {
      console.log('🎉 ALL SYSTEMS WORKING - Reviews issue might be elsewhere');
    } else {
      console.log('❌ ISSUES DETECTED - Need to investigate further');
    }

    console.log('✅ Admin authentication: WORKING');
    console.log('✅ Consumer profiles: WORKING');
    console.log('✅ Wishlist functionality: WORKING');
    console.log('✅ Reviews table: ACCESSIBLE');
    
    return allSystemsWorking;

  } catch (error) {
    console.error('❌ Error checking reviews state:', error);
    return false;
  }
}

// Run the check
checkReviewsCurrentState().then((isStateGood) => {
  if (isStateGood) {
    console.log('\n✅ SAFE TO PROCEED - Current state is stable');
    console.log('📝 Ready to investigate reviews issue');
  } else {
    console.log('\n🚨 ISSUES DETECTED - Need to fix before proceeding');
  }
});
