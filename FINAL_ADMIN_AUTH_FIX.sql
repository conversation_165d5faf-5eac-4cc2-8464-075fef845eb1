-- =====================================================================
-- 🚨 FINAL ADMIN AUTHENTICATION FIX
-- =====================================================================
-- Problem: "permission denied for table users" when accessing profiles
-- Root Cause: Recursive RLS policies that reference auth.users table
-- Solution: Drop ALL policies and create ultra-simple, non-recursive ones

-- STEP 1: DISABLE RLS TEMPORARILY TO CLEAN UP
-- =====================================================================
ALTER TABLE profiles DISABLE ROW LEVEL SECURITY;

-- STEP 2: DROP ALL EXISTING POLICIES (NUCLEAR OPTION)
-- =====================================================================
-- This ensures we start with a clean slate

-- Drop all known policies that might exist
DROP POLICY IF EXISTS "profiles_service_role_access" ON profiles;
DROP POLICY IF EXISTS "profiles_service_role_full_access" ON profiles;
DROP POLICY IF EXISTS "profiles_admin_own_access" ON profiles;
DROP POLICY IF EXISTS "profiles_admin_own_update" ON profiles;
DROP POLICY IF EXISTS "profiles_consumer_signup" ON profiles;
DROP POLICY IF EXISTS "profiles_consumer_login_verification" ON profiles;
DROP POLICY IF EXISTS "profiles_authenticated_select_simple" ON profiles;
DROP POLICY IF EXISTS "profiles_authenticated_update_simple" ON profiles;
DROP POLICY IF EXISTS "profiles_authenticated_insert_simple" ON profiles;
DROP POLICY IF EXISTS "profiles_authenticated_insert_admin_merchant_supplier" ON profiles;
DROP POLICY IF EXISTS "profiles_admin_emergency_access" ON profiles;
DROP POLICY IF EXISTS "profiles_emergency_admin_access" ON profiles;
DROP POLICY IF EXISTS "service_role_all_access" ON profiles;
DROP POLICY IF EXISTS "users_select_own_profile" ON profiles;
DROP POLICY IF EXISTS "users_update_own_profile" ON profiles;
DROP POLICY IF EXISTS "users_insert_own_profile" ON profiles;
DROP POLICY IF EXISTS "profiles_consumer_anonymous_select" ON profiles;
DROP POLICY IF EXISTS "profiles_consumer_anonymous_insert" ON profiles;
DROP POLICY IF EXISTS "profiles_consumer_anonymous_signup" ON profiles;
DROP POLICY IF EXISTS "profiles_consumer_anonymous_login" ON profiles;
DROP POLICY IF EXISTS "profiles_authenticated_own_access" ON profiles;
DROP POLICY IF EXISTS "profiles_authenticated_own_update" ON profiles;
DROP POLICY IF EXISTS "profiles_authenticated_insert" ON profiles;
DROP POLICY IF EXISTS "Users can view their own profile" ON profiles;
DROP POLICY IF EXISTS "Users can update their own profile" ON profiles;
DROP POLICY IF EXISTS "Postgres role can do anything" ON profiles;
DROP POLICY IF EXISTS "Service role can do anything" ON profiles;

-- STEP 3: RE-ENABLE RLS
-- =====================================================================
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;

-- STEP 4: CREATE ULTRA-SIMPLE, NON-RECURSIVE POLICIES
-- =====================================================================

-- Policy 1: Service Role Full Access (HIGHEST PRIORITY)
-- This allows all backend operations to work
CREATE POLICY "service_role_full_access" ON profiles
  FOR ALL
  USING (auth.role() = 'service_role')
  WITH CHECK (auth.role() = 'service_role');

-- Policy 2: Authenticated Users Own Profile Access
-- This is the SIMPLEST possible policy - no recursion, no complex logic
CREATE POLICY "authenticated_own_profile" ON profiles
  FOR ALL
  USING (auth.uid() = id)
  WITH CHECK (auth.uid() = id);

-- Policy 3: Consumer Anonymous Access (ISOLATED)
-- Consumers don't have auth.users records, so they need anonymous access
CREATE POLICY "consumer_anonymous_access" ON profiles
  FOR ALL
  USING (role = 'consumer')
  WITH CHECK (role = 'consumer');

-- STEP 5: VERIFY THE FIX WORKS
-- =====================================================================

-- Test that we can access profiles
SELECT 'Policy test:' as test_name, count(*) as profile_count 
FROM profiles 
WHERE role IN ('supplier', 'merchant', 'admin')
LIMIT 5;

-- Show the final policies
SELECT 
  policyname,
  cmd,
  CASE 
    WHEN qual IS NOT NULL THEN 'USING: ' || qual 
    ELSE 'No USING clause' 
  END as using_clause,
  CASE 
    WHEN with_check IS NOT NULL THEN 'WITH CHECK: ' || with_check 
    ELSE 'No WITH CHECK clause' 
  END as with_check_clause
FROM pg_policies 
WHERE tablename = 'profiles' 
ORDER BY policyname;
