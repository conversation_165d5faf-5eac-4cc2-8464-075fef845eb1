import { createClient } from '@supabase/supabase-js';

const supabaseUrl = 'https://irkwpzcskeqtasutqnxp.supabase.co';
const supabaseServiceKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imlya3dwemNza2VxdGFzdXRxbnhwIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0NzQ5ODY4MywiZXhwIjoyMDYzMDc0NjgzfQ.PLfCCbh4zgAJDDC6sS82fCU32JYA7e2VmwzvavF-PW8';

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function findRecursivePolicy() {
  console.log('🔍 Finding Recursive RLS Policy...\n');

  try {
    // Get all current policies on profiles table
    console.log('1. Current policies on profiles table:');
    
    const { data: policies, error: policiesError } = await supabase
      .from('pg_policies')
      .select('policyname, cmd, qual, with_check')
      .eq('tablename', 'profiles')
      .order('policyname');

    if (policiesError) {
      console.error('Error fetching policies:', policiesError);
      return;
    }

    console.log('Found', policies.length, 'policies:');
    policies.forEach(policy => {
      console.log(`\n📋 Policy: ${policy.policyname}`);
      console.log(`   Command: ${policy.cmd}`);
      if (policy.qual) {
        console.log(`   USING: ${policy.qual}`);
        // Check if this policy references auth.users
        if (policy.qual.includes('auth.users')) {
          console.log('   ⚠️  RECURSIVE: This policy references auth.users!');
        }
      }
      if (policy.with_check) {
        console.log(`   WITH CHECK: ${policy.with_check}`);
        if (policy.with_check.includes('auth.users')) {
          console.log('   ⚠️  RECURSIVE: This WITH CHECK references auth.users!');
        }
      }
    });

    // Now let's drop the recursive policies and create simple ones
    console.log('\n2. Dropping recursive policies...');
    
    const recursivePolicies = policies.filter(p => 
      (p.qual && p.qual.includes('auth.users')) || 
      (p.with_check && p.with_check.includes('auth.users'))
    );

    for (const policy of recursivePolicies) {
      console.log(`Dropping recursive policy: ${policy.policyname}`);
      
      const { error: dropError } = await supabase.rpc('exec_sql', {
        sql: `DROP POLICY IF EXISTS "${policy.policyname}" ON profiles;`
      });
      
      if (dropError && !dropError.message.includes('does not exist')) {
        console.error(`Error dropping ${policy.policyname}:`, dropError);
      }
    }

    // Create ultra-simple policies
    console.log('\n3. Creating ultra-simple policies...');
    
    const simplePolicies = [
      {
        name: 'profiles_service_role_access',
        sql: `CREATE POLICY "profiles_service_role_access" ON profiles
              FOR ALL
              USING (auth.role() = 'service_role')
              WITH CHECK (auth.role() = 'service_role');`
      },
      {
        name: 'profiles_own_access_simple',
        sql: `CREATE POLICY "profiles_own_access_simple" ON profiles
              FOR ALL
              USING (auth.uid() = id)
              WITH CHECK (auth.uid() = id);`
      },
      {
        name: 'profiles_consumer_anonymous',
        sql: `CREATE POLICY "profiles_consumer_anonymous" ON profiles
              FOR ALL
              USING (role = 'consumer' AND auth.uid() IS NULL)
              WITH CHECK (role = 'consumer' AND auth.uid() IS NULL);`
      }
    ];

    for (const policy of simplePolicies) {
      const { error: createError } = await supabase.rpc('exec_sql', {
        sql: policy.sql
      });
      
      if (createError) {
        console.error(`Error creating ${policy.name}:`, createError);
      } else {
        console.log(`✅ Created: ${policy.name}`);
      }
    }

    console.log('\n🎉 Recursive policies fixed!');

  } catch (error) {
    console.error('❌ Failed to find recursive policy:', error);
  }
}

findRecursivePolicy();
