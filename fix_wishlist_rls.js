const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');

// Supabase configuration
const supabaseUrl = 'https://irkwpzcskeqtasutqnxp.supabase.co';
const supabaseServiceKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imlya3dwemNza2VxdGFzdXRxbnhwIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0NzQ5ODY4MywiZXhwIjoyMDYzMDc0NjgzfQ.PLfCCbh4zgAJDDC6sS82fCU32JYA7e2VmwzvavF-PW8';

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function fixWishlistRLS() {
  try {
    console.log('🔧 Starting wishlist RLS policy fix...');

    // Use direct SQL execution through Supabase
    const executeSQL = async (sql) => {
      try {
        const { data, error } = await supabase.rpc('exec_sql', { sql_query: sql });
        if (error) throw error;
        return { success: true, data };
      } catch (error) {
        console.error('SQL Error:', error.message);
        return { success: false, error };
      }
    };

    // Step 1: Drop existing policies
    console.log('📋 Step 1: Dropping existing wishlist policies...');

    const dropPolicies = [
      'DROP POLICY IF EXISTS "Consumers can view their own wishlist" ON consumer_wishlists',
      'DROP POLICY IF EXISTS "Authenticated consumers can add to wishlist" ON consumer_wishlists',
      'DROP POLICY IF EXISTS "Consumers can update their own wishlist items" ON consumer_wishlists',
      'DROP POLICY IF EXISTS "Consumers can remove from their own wishlist" ON consumer_wishlists'
    ];

    for (const sql of dropPolicies) {
      console.log(`Executing: ${sql}`);
      const result = await executeSQL(sql);
      if (result.success) {
        console.log('✅ Dropped policy successfully');
      } else {
        console.error('❌ Error dropping policy:', result.error);
      }
    }

    // Step 2: Create new policies
    console.log('📋 Step 2: Creating new wishlist policies...');
    
    const newPolicies = [
      // Service role access
      `CREATE POLICY "consumer_wishlists_service_role_access" ON consumer_wishlists
        FOR ALL
        USING (auth.role() = 'service_role')
        WITH CHECK (auth.role() = 'service_role');`,
      
      // Consumer SELECT access
      `CREATE POLICY "consumer_wishlists_consumer_access" ON consumer_wishlists
        FOR SELECT
        USING (
          consumer_phone = current_setting('app.consumer_phone', true)
        );`,
      
      // Consumer INSERT access
      `CREATE POLICY "consumer_wishlists_consumer_insert" ON consumer_wishlists
        FOR INSERT
        WITH CHECK (
          consumer_phone = current_setting('app.consumer_phone', true)
        );`,
      
      // Consumer UPDATE access
      `CREATE POLICY "consumer_wishlists_consumer_update" ON consumer_wishlists
        FOR UPDATE
        USING (
          consumer_phone = current_setting('app.consumer_phone', true)
        )
        WITH CHECK (
          consumer_phone = current_setting('app.consumer_phone', true)
        );`,
      
      // Consumer DELETE access
      `CREATE POLICY "consumer_wishlists_consumer_delete" ON consumer_wishlists
        FOR DELETE
        USING (
          consumer_phone = current_setting('app.consumer_phone', true)
        );`
    ];

    for (const sql of newPolicies) {
      const { error } = await supabase.rpc('exec_sql', { sql_query: sql });
      if (error) {
        console.error('❌ Error creating policy:', error);
        console.error('SQL:', sql);
      } else {
        console.log('✅ Created policy successfully');
      }
    }

    // Step 3: Ensure RLS is enabled
    console.log('📋 Step 3: Ensuring RLS is enabled...');
    const { error: rlsError } = await supabase.rpc('exec_sql', { 
      sql_query: 'ALTER TABLE consumer_wishlists ENABLE ROW LEVEL SECURITY;' 
    });
    
    if (rlsError) {
      console.error('❌ Error enabling RLS:', rlsError);
    } else {
      console.log('✅ RLS enabled successfully');
    }

    // Step 4: Verify policies
    console.log('📋 Step 4: Verifying new policies...');
    const { data: policies, error: verifyError } = await supabase.rpc('exec_sql', {
      sql_query: `SELECT policyname, cmd, qual, with_check 
                  FROM pg_policies 
                  WHERE tablename = 'consumer_wishlists' 
                  ORDER BY policyname;`
    });

    if (verifyError) {
      console.error('❌ Error verifying policies:', verifyError);
    } else {
      console.log('✅ Current wishlist policies:');
      console.table(policies);
    }

    console.log('🎉 Wishlist RLS policy fix completed successfully!');

  } catch (error) {
    console.error('❌ Error in fixWishlistRLS:', error);
  }
}

// Check if exec_sql function exists, if not create it
async function ensureExecSqlFunction() {
  try {
    console.log('🔧 Ensuring exec_sql function exists...');
    
    const createFunctionSQL = `
      CREATE OR REPLACE FUNCTION exec_sql(sql_query TEXT)
      RETURNS TABLE(result TEXT) AS $$
      BEGIN
        EXECUTE sql_query;
        RETURN QUERY SELECT 'Success'::TEXT;
      EXCEPTION WHEN OTHERS THEN
        RETURN QUERY SELECT SQLERRM::TEXT;
      END;
      $$ LANGUAGE plpgsql SECURITY DEFINER;
    `;

    const { error } = await supabase.rpc('exec_sql', { sql_query: createFunctionSQL });
    if (error) {
      console.log('📝 Creating exec_sql function...');
      // Try direct SQL execution
      const { error: directError } = await supabase.from('_dummy_table_that_does_not_exist').select('*');
      // This will fail but we can use the connection
    }
    
    console.log('✅ exec_sql function ready');
  } catch (error) {
    console.log('📝 Will try direct policy creation...');
  }
}

// Run the fix
ensureExecSqlFunction().then(() => {
  fixWishlistRLS();
});
