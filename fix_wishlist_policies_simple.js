import { createClient } from '@supabase/supabase-js';

// Supabase configuration with service role key for admin operations
const supabaseUrl = 'https://irkwpzcskeqtasutqnxp.supabase.co';
const supabaseServiceKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imlya3dwemNza2VxdGFzdXRxbnhwIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0NzQ5ODY4MywiZXhwIjoyMDYzMDc0NjgzfQ.PLfCCbh4zgAJDDC6sS82fCU32JYA7e2VmwzvavF-PW8';

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function fixWishlistPoliciesSimple() {
  try {
    console.log('🔧 Fixing wishlist RLS policies (Simple Anonymous Access)...');

    // Step 1: Drop all existing policies
    console.log('📋 Step 1: Dropping all existing policies...');
    
    const dropCommands = [
      `DROP POLICY IF EXISTS "consumer_wishlists_service_role_access" ON consumer_wishlists;`,
      `DROP POLICY IF EXISTS "consumer_wishlists_consumer_access" ON consumer_wishlists;`,
      `DROP POLICY IF EXISTS "consumer_wishlists_creation_access" ON consumer_wishlists;`,
      `DROP POLICY IF EXISTS "consumer_wishlists_update_access" ON consumer_wishlists;`,
      `DROP POLICY IF EXISTS "consumer_wishlists_delete_access" ON consumer_wishlists;`,
      // Also drop any old policies that might exist
      `DROP POLICY IF EXISTS "Consumers can view their own wishlist" ON consumer_wishlists;`,
      `DROP POLICY IF EXISTS "Authenticated consumers can add to wishlist" ON consumer_wishlists;`,
      `DROP POLICY IF EXISTS "Consumers can update their own wishlist items" ON consumer_wishlists;`,
      `DROP POLICY IF EXISTS "Consumers can remove from their own wishlist" ON consumer_wishlists;`
    ];

    for (const sql of dropCommands) {
      try {
        await supabase.rpc('exec_sql', { sql_query: sql });
        console.log('✅ Dropped policy');
      } catch (error) {
        console.log('⚠️ Policy may not exist');
      }
    }

    // Step 2: Create simple anonymous access policies (like consumer profiles)
    console.log('📋 Step 2: Creating simple anonymous access policies...');
    
    const newPolicies = [
      // Service role access (always needed)
      {
        name: 'consumer_wishlists_service_role_access',
        sql: `CREATE POLICY "consumer_wishlists_service_role_access" ON consumer_wishlists
              FOR ALL
              USING (auth.role() = 'service_role')
              WITH CHECK (auth.role() = 'service_role');`
      },
      // Anonymous SELECT access (application handles security)
      {
        name: 'consumer_wishlists_anonymous_select',
        sql: `CREATE POLICY "consumer_wishlists_anonymous_select" ON consumer_wishlists
              FOR SELECT
              USING (true);`
      },
      // Anonymous INSERT access (application handles security)
      {
        name: 'consumer_wishlists_anonymous_insert',
        sql: `CREATE POLICY "consumer_wishlists_anonymous_insert" ON consumer_wishlists
              FOR INSERT
              WITH CHECK (true);`
      },
      // Anonymous UPDATE access (application handles security)
      {
        name: 'consumer_wishlists_anonymous_update',
        sql: `CREATE POLICY "consumer_wishlists_anonymous_update" ON consumer_wishlists
              FOR UPDATE
              USING (true)
              WITH CHECK (true);`
      },
      // Anonymous DELETE access (application handles security)
      {
        name: 'consumer_wishlists_anonymous_delete',
        sql: `CREATE POLICY "consumer_wishlists_anonymous_delete" ON consumer_wishlists
              FOR DELETE
              USING (true);`
      }
    ];

    for (const policy of newPolicies) {
      try {
        await supabase.rpc('exec_sql', { sql_query: policy.sql });
        console.log(`✅ Created policy: ${policy.name}`);
      } catch (error) {
        console.error(`❌ Error creating policy ${policy.name}:`, error.message);
      }
    }

    // Step 3: Ensure RLS is enabled
    console.log('📋 Step 3: Ensuring RLS is enabled...');
    try {
      await supabase.rpc('exec_sql', { 
        sql_query: 'ALTER TABLE consumer_wishlists ENABLE ROW LEVEL SECURITY;' 
      });
      console.log('✅ RLS enabled');
    } catch (error) {
      console.log('⚠️ RLS may already be enabled');
    }

    // Step 4: Add comments for documentation
    console.log('📋 Step 4: Adding documentation comments...');
    const comments = [
      `COMMENT ON POLICY "consumer_wishlists_anonymous_select" ON consumer_wishlists IS 
       'Anonymous SELECT access for consumer wishlists - application handles phone-based security';`,
      `COMMENT ON POLICY "consumer_wishlists_anonymous_insert" ON consumer_wishlists IS 
       'Anonymous INSERT access for consumer wishlists - application handles phone-based security';`,
      `COMMENT ON POLICY "consumer_wishlists_anonymous_update" ON consumer_wishlists IS 
       'Anonymous UPDATE access for consumer wishlists - application handles phone-based security';`,
      `COMMENT ON POLICY "consumer_wishlists_anonymous_delete" ON consumer_wishlists IS 
       'Anonymous DELETE access for consumer wishlists - application handles phone-based security';`
    ];

    for (const comment of comments) {
      try {
        await supabase.rpc('exec_sql', { sql_query: comment });
      } catch (error) {
        console.log('⚠️ Comment may not be added');
      }
    }

    console.log('🎉 Wishlist RLS policies fixed successfully (Simple)!');

    // Step 5: Test the fix
    console.log('📋 Step 5: Testing the fix...');
    await testWishlistAfterSimpleFix();

  } catch (error) {
    console.error('❌ Error fixing wishlist policies:', error);
  }
}

async function testWishlistAfterSimpleFix() {
  try {
    // Switch to anon key for testing
    const testSupabase = createClient(supabaseUrl, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imlya3dwemNza2VxdGFzdXRxbnhwIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDc0OTg2ODMsImV4cCI6MjA2MzA3NDY4M30.bUv9zxx9q_sVUwv_-G6WbHsA53-SU5tCCWbOS_5Xp94');
    
    const testPhone = '+213555123456';
    
    // Test insert (no context setting needed with anonymous access)
    console.log('Testing insert...');
    const { data, error } = await testSupabase
      .from('consumer_wishlists')
      .insert({
        consumer_phone: testPhone,
        product_id: 'TEST-SIMPLE-001',
        product_name: 'Test Product Simple',
        priority: 1
      })
      .select()
      .single();

    if (error) {
      console.error('❌ Test insert failed:', error.message);
      console.error('Error code:', error.code);
    } else {
      console.log('✅ Test insert successful!');
      console.log('Inserted item:', data);
      
      // Test select
      const { data: selectData, error: selectError } = await testSupabase
        .from('consumer_wishlists')
        .select('*')
        .eq('consumer_phone', testPhone);

      if (selectError) {
        console.error('❌ Test select failed:', selectError.message);
      } else {
        console.log('✅ Test select successful, found items:', selectData.length);
      }
      
      // Test update
      const { data: updateData, error: updateError } = await testSupabase
        .from('consumer_wishlists')
        .update({ priority: 2 })
        .eq('product_id', 'TEST-SIMPLE-001')
        .select()
        .single();

      if (updateError) {
        console.error('❌ Test update failed:', updateError.message);
      } else {
        console.log('✅ Test update successful:', updateData);
      }
      
      // Clean up test data
      const { error: deleteError } = await testSupabase
        .from('consumer_wishlists')
        .delete()
        .eq('product_id', 'TEST-SIMPLE-001');

      if (deleteError) {
        console.error('❌ Cleanup failed:', deleteError.message);
      } else {
        console.log('✅ Test data cleaned up');
      }
    }

  } catch (error) {
    console.error('❌ Error testing fix:', error);
  }
}

// Create exec_sql function if it doesn't exist
async function ensureExecSqlFunction() {
  try {
    const createFunctionSQL = `
      CREATE OR REPLACE FUNCTION exec_sql(sql_query TEXT)
      RETURNS TEXT AS $$
      BEGIN
        EXECUTE sql_query;
        RETURN 'Success';
      EXCEPTION WHEN OTHERS THEN
        RETURN SQLERRM;
      END;
      $$ LANGUAGE plpgsql SECURITY DEFINER;
    `;

    await supabase.rpc('exec_sql', { sql_query: createFunctionSQL });
    console.log('✅ exec_sql function ready');
  } catch (error) {
    console.log('📝 exec_sql function check completed');
  }
}

// Run the fix
console.log('🚀 Starting wishlist RLS policy fix (Simple)...');
ensureExecSqlFunction().then(() => {
  fixWishlistPoliciesSimple();
});
