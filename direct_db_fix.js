import pg from 'pg';
const { Client } = pg;

async function directDbFix() {
  console.log('🚨 DIRECT DATABASE FIX...\n');

  const client = new Client({
    host: 'db.irkwpzcskeqtasutqnxp.supabase.co',
    port: 5432,
    database: 'postgres',
    user: 'postgres',
    password: 'De0q8PKgzE7nLOsC',
    ssl: { rejectUnauthorized: false }
  });

  try {
    await client.connect();
    console.log('✅ Connected to database');

    // Step 1: Check current policies
    console.log('\n1. Checking current policies...');
    
    const policiesResult = await client.query(`
      SELECT policyname, cmd, qual, with_check 
      FROM pg_policies 
      WHERE tablename = 'profiles' 
      AND schemaname = 'public'
      ORDER BY policyname;
    `);

    console.log('Current policies:');
    policiesResult.rows.forEach(policy => {
      console.log(`- ${policy.policyname} (${policy.cmd})`);
      if (policy.qual) {
        console.log(`  USING: ${policy.qual}`);
        if (policy.qual.includes('auth.users')) {
          console.log('  ⚠️  RECURSIVE: References auth.users!');
        }
      }
      if (policy.with_check) {
        console.log(`  WITH CHECK: ${policy.with_check}`);
        if (policy.with_check.includes('auth.users')) {
          console.log('  ⚠️  RECURSIVE: WITH CHECK references auth.users!');
        }
      }
    });

    // Step 2: Disable RLS temporarily
    console.log('\n2. Disabling RLS...');
    await client.query('ALTER TABLE profiles DISABLE ROW LEVEL SECURITY;');
    console.log('✅ RLS disabled');

    // Step 3: Drop all policies
    console.log('\n3. Dropping all policies...');
    
    for (const policy of policiesResult.rows) {
      try {
        await client.query(`DROP POLICY IF EXISTS "${policy.policyname}" ON profiles;`);
        console.log(`✅ Dropped: ${policy.policyname}`);
      } catch (error) {
        console.log(`❌ Failed to drop ${policy.policyname}:`, error.message);
      }
    }

    // Step 4: Re-enable RLS
    console.log('\n4. Re-enabling RLS...');
    await client.query('ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;');
    console.log('✅ RLS re-enabled');

    // Step 5: Create ultra-simple policies
    console.log('\n5. Creating ultra-simple policies...');

    // Service role policy
    await client.query(`
      CREATE POLICY "service_role_access" ON profiles
        FOR ALL
        USING (auth.role() = 'service_role')
        WITH CHECK (auth.role() = 'service_role');
    `);
    console.log('✅ Service role policy created');

    // Own profile policy (NO RECURSION)
    await client.query(`
      CREATE POLICY "own_profile_access" ON profiles
        FOR ALL
        USING (auth.uid() = id)
        WITH CHECK (auth.uid() = id);
    `);
    console.log('✅ Own profile policy created');

    // Consumer policy (NO AUTH.USERS REFERENCE)
    await client.query(`
      CREATE POLICY "consumer_access" ON profiles
        FOR ALL
        USING (role = 'consumer')
        WITH CHECK (role = 'consumer');
    `);
    console.log('✅ Consumer policy created');

    // Step 6: Verify the fix
    console.log('\n6. Verifying the fix...');
    
    const testResult = await client.query(`
      SELECT count(*) as profile_count 
      FROM profiles 
      WHERE role IN ('supplier', 'merchant', 'admin');
    `);
    
    console.log('✅ Test query successful:', testResult.rows[0].profile_count, 'profiles found');

    // Step 7: Show final policies
    console.log('\n7. Final policies:');
    
    const finalPoliciesResult = await client.query(`
      SELECT policyname, cmd, qual, with_check 
      FROM pg_policies 
      WHERE tablename = 'profiles' 
      AND schemaname = 'public'
      ORDER BY policyname;
    `);

    finalPoliciesResult.rows.forEach(policy => {
      console.log(`- ${policy.policyname} (${policy.cmd})`);
      if (policy.qual) console.log(`  USING: ${policy.qual}`);
      if (policy.with_check) console.log(`  WITH CHECK: ${policy.with_check}`);
    });

    console.log('\n🎉 DIRECT DATABASE FIX COMPLETED!');
    console.log('👉 Please test admin signup and login in the browser now.');

  } catch (error) {
    console.error('❌ Direct database fix failed:', error);
  } finally {
    await client.end();
  }
}

directDbFix();
