-- EMERGENCY FIX: Restore Admin Panel Access
-- Run this directly in Supabase SQL Editor to fix admin authentication
-- Date: July 8, 2025

-- 1. Drop the problematic restrictive policies for non-consumer roles
DROP POLICY IF EXISTS "profiles_authenticated_select_non_consumer" ON profiles;
DROP POLICY IF EXISTS "profiles_authenticated_update_non_consumer" ON profiles;
DROP POLICY IF EXISTS "profiles_authenticated_insert_non_consumer" ON profiles;

-- 2. Create more permissive policies for admin/merchant/supplier roles
-- These policies allow authenticated users to access their profiles AND admin operations

-- Allow authenticated users to SELECT their own profile OR if they are admin
CREATE POLICY "profiles_authenticated_select_admin_merchant_supplier" ON profiles
  FOR SELECT 
  USING (
    auth.uid() = id 
    OR 
    (
      auth.uid() IS NOT NULL 
      AND role IN ('admin', 'merchant', 'supplier', 'distribution')
      AND EXISTS (
        SELECT 1 FROM profiles admin_check 
        WHERE admin_check.id = auth.uid() 
        AND admin_check.role IN ('admin', 'merchant', 'supplier', 'distribution')
      )
    )
  );

-- Allow authenticated users to UPDATE their own profile OR admin operations
CREATE POLICY "profiles_authenticated_update_admin_merchant_supplier" ON profiles
  FOR UPDATE 
  USING (
    auth.uid() = id 
    OR 
    (
      auth.uid() IS NOT NULL 
      AND role IN ('admin', 'merchant', 'supplier', 'distribution')
      AND EXISTS (
        SELECT 1 FROM profiles admin_check 
        WHERE admin_check.id = auth.uid() 
        AND admin_check.role IN ('admin', 'merchant', 'supplier', 'distribution')
      )
    )
  )
  WITH CHECK (
    auth.uid() = id 
    OR 
    (
      auth.uid() IS NOT NULL 
      AND role IN ('admin', 'merchant', 'supplier', 'distribution')
      AND EXISTS (
        SELECT 1 FROM profiles admin_check 
        WHERE admin_check.id = auth.uid() 
        AND admin_check.role IN ('admin', 'merchant', 'supplier', 'distribution')
      )
    )
  );

-- Allow authenticated users to INSERT their profile during signup
CREATE POLICY "profiles_authenticated_insert_admin_merchant_supplier" ON profiles
  FOR INSERT 
  WITH CHECK (
    auth.uid() = id 
    AND role IN ('admin', 'merchant', 'supplier', 'distribution')
  );

-- 3. Add a simplified admin access policy for emergency situations
CREATE POLICY "profiles_admin_emergency_access" ON profiles
  FOR ALL
  USING (
    EXISTS (
      SELECT 1 FROM auth.users 
      WHERE auth.users.id = auth.uid()
      AND auth.users.email IN (
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>'
      )
    )
  )
  WITH CHECK (
    EXISTS (
      SELECT 1 FROM auth.users 
      WHERE auth.users.id = auth.uid()
      AND auth.users.email IN (
        '<EMAIL>',
        '<EMAIL>', 
        '<EMAIL>'
      )
    )
  );

-- 4. Ensure service role can still manage all profiles (keep existing)
-- This policy should already exist from the previous fix

-- 5. Verify RLS is enabled
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;

-- 6. Add comments for documentation
COMMENT ON POLICY "profiles_authenticated_select_admin_merchant_supplier" ON profiles IS 
'EMERGENCY FIX: Allows authenticated admin/merchant/supplier users to access profiles with proper role checking';

COMMENT ON POLICY "profiles_authenticated_update_admin_merchant_supplier" ON profiles IS 
'EMERGENCY FIX: Allows authenticated admin/merchant/supplier users to update profiles with proper role checking';

COMMENT ON POLICY "profiles_authenticated_insert_admin_merchant_supplier" ON profiles IS 
'EMERGENCY FIX: Allows authenticated users to create admin/merchant/supplier profiles during signup';

COMMENT ON POLICY "profiles_admin_emergency_access" ON profiles IS 
'EMERGENCY FIX: Provides emergency access for specific admin emails to restore system functionality';

-- 7. Test query to verify admin access works
SELECT 'ADMIN_ACCESS_TEST: Admin profiles accessible' as test_result, 
       count(*) as admin_count,
       string_agg(email, ', ') as admin_emails
FROM profiles 
WHERE role IN ('admin', 'merchant', 'supplier') 
LIMIT 10;

-- 8. Test query to verify consumer access still works
SELECT 'CONSUMER_ACCESS_TEST: Consumer profiles accessible' as test_result,
       count(*) as consumer_count
FROM profiles
WHERE role = 'consumer'
LIMIT 5;

-- 9. FIX MARKETPLACE PRODUCT LOADING ISSUES
-- The problem: Restrictive RLS policies on products table are blocking marketplace access
-- Solution: Ensure marketplace policies take precedence over user-only policies

-- Drop conflicting restrictive product policies that block marketplace access
DROP POLICY IF EXISTS "Users can only see their own products" ON products;

-- Create new policy that allows BOTH user access AND marketplace access
CREATE POLICY "products_user_and_marketplace_access" ON products
  FOR SELECT USING (
    -- Allow users to see their own products (admin panel access)
    auth.uid() = user_id
    OR
    -- Allow marketplace access to active/out_of_stock products (marketplace access)
    status IN ('active', 'out_of_stock')
  );

-- Ensure wholesale pricing tiers are accessible for marketplace
DROP POLICY IF EXISTS "Users can only see pricing for their own products" ON wholesale_pricing_tiers;

CREATE POLICY "pricing_tiers_user_and_marketplace_access" ON wholesale_pricing_tiers
  FOR SELECT USING (
    -- Allow users to see pricing for their own products
    EXISTS (
      SELECT 1 FROM products
      WHERE products.id = wholesale_pricing_tiers.product_id
      AND products.user_id = auth.uid()
    )
    OR
    -- Allow marketplace access to pricing for active/out_of_stock products
    EXISTS (
      SELECT 1 FROM products
      WHERE products.id = wholesale_pricing_tiers.product_id
      AND products.status IN ('active', 'out_of_stock')
    )
  );

-- Ensure tyre specifications are accessible for marketplace
DROP POLICY IF EXISTS "Users can only see tyre specs for their own products" ON tyre_specifications;

CREATE POLICY "tyre_specs_user_and_marketplace_access" ON tyre_specifications
  FOR SELECT USING (
    -- Allow users to see specs for their own products
    EXISTS (
      SELECT 1 FROM products
      WHERE products.id = tyre_specifications.product_id
      AND products.user_id = auth.uid()
    )
    OR
    -- Allow marketplace access to specs for active/out_of_stock products
    EXISTS (
      SELECT 1 FROM products
      WHERE products.id = tyre_specifications.product_id
      AND products.status IN ('active', 'out_of_stock')
    )
  );

-- Ensure vehicle compatibility is accessible for marketplace
DROP POLICY IF EXISTS "Users can only see vehicle compatibility for their own products" ON vehicle_compatibility;

CREATE POLICY "vehicle_compat_user_and_marketplace_access" ON vehicle_compatibility
  FOR SELECT USING (
    -- Allow users to see compatibility for their own products
    EXISTS (
      SELECT 1 FROM products
      WHERE products.id = vehicle_compatibility.product_id
      AND products.user_id = auth.uid()
    )
    OR
    -- Allow marketplace access to compatibility for active/out_of_stock products
    EXISTS (
      SELECT 1 FROM products
      WHERE products.id = vehicle_compatibility.product_id
      AND products.status IN ('active', 'out_of_stock')
    )
  );

-- 10. Test marketplace product access
SELECT 'MARKETPLACE_PRODUCTS_TEST: Active products accessible' as test_result,
       count(*) as active_product_count
FROM products
WHERE status IN ('active', 'out_of_stock')
LIMIT 10;
