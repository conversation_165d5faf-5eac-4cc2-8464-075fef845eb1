-- =====================================================================
-- 🚨 EMERGENCY ADMIN AUTHENTICATION FIX
-- =====================================================================
-- This fixes the broken admin signup and login issues
-- Problem: RLS policies are too restrictive for admin users
-- Solution: Create permissive policies for authenticated admin users

-- STEP 1: DROP CONFLICTING POLICIES
-- =====================================================================

-- Drop all existing profiles policies that are causing conflicts
DROP POLICY IF EXISTS "profiles_service_role_access" ON profiles;
DROP POLICY IF EXISTS "profiles_admin_own_access" ON profiles;
DROP POLICY IF EXISTS "profiles_admin_own_update" ON profiles;
DROP POLICY IF EXISTS "profiles_consumer_signup" ON profiles;
DROP POLICY IF EXISTS "profiles_consumer_login_verification" ON profiles;
DROP POLICY IF EXISTS "profiles_authenticated_select_simple" ON profiles;
DROP POLICY IF EXISTS "profiles_authenticated_update_simple" ON profiles;
DROP POLICY IF EXISTS "profiles_authenticated_insert_simple" ON profiles;
DROP POLICY IF EXISTS "profiles_authenticated_insert_admin_merchant_supplier" ON profiles;
DROP POLICY IF EXISTS "profiles_admin_emergency_access" ON profiles;
DROP POLICY IF EXISTS "service_role_all_access" ON profiles;
DROP POLICY IF EXISTS "users_select_own_profile" ON profiles;
DROP POLICY IF EXISTS "users_update_own_profile" ON profiles;
DROP POLICY IF EXISTS "users_insert_own_profile" ON profiles;
DROP POLICY IF EXISTS "profiles_consumer_anonymous_select" ON profiles;
DROP POLICY IF EXISTS "profiles_consumer_anonymous_insert" ON profiles;
DROP POLICY IF EXISTS "Users can view their own profile" ON profiles;
DROP POLICY IF EXISTS "Users can update their own profile" ON profiles;

-- STEP 2: CREATE ULTRA-SIMPLE, WORKING POLICIES
-- =====================================================================

-- Policy 1: Service Role Full Access (HIGHEST PRIORITY)
CREATE POLICY "profiles_service_role_full_access" ON profiles
  FOR ALL
  USING (auth.role() = 'service_role')
  WITH CHECK (auth.role() = 'service_role');

-- Policy 2: Authenticated Users - Own Profile Access (SIMPLE & RELIABLE)
-- This covers ALL authenticated users (admin, supplier, merchant, distribution)
CREATE POLICY "profiles_authenticated_own_access" ON profiles
  FOR SELECT
  USING (auth.uid() IS NOT NULL AND auth.uid() = id);

CREATE POLICY "profiles_authenticated_own_update" ON profiles
  FOR UPDATE
  USING (auth.uid() IS NOT NULL AND auth.uid() = id)
  WITH CHECK (auth.uid() IS NOT NULL AND auth.uid() = id);

-- Policy 3: Profile Creation During Signup (CRITICAL)
-- Allow authenticated users to create their own profile
CREATE POLICY "profiles_authenticated_insert" ON profiles
  FOR INSERT
  WITH CHECK (auth.uid() IS NOT NULL AND auth.uid() = id);

-- Policy 4: Consumer Anonymous Access (SEPARATE & ISOLATED)
-- Consumer signup (anonymous insert)
CREATE POLICY "profiles_consumer_anonymous_signup" ON profiles
  FOR INSERT
  WITH CHECK (
    role = 'consumer'
    AND auth.uid() IS NULL
    AND phone IS NOT NULL
    AND full_name IS NOT NULL
  );

-- Consumer login verification (limited anonymous select)
CREATE POLICY "profiles_consumer_anonymous_login" ON profiles
  FOR SELECT
  USING (
    role = 'consumer'
    AND auth.uid() IS NULL
  );

-- STEP 3: VERIFY POLICIES ARE WORKING
-- =====================================================================

-- Test that we can read profiles as service role
SELECT 'Service role test:' as test_type, count(*) as profile_count 
FROM profiles 
WHERE role IN ('supplier', 'merchant', 'admin');

-- Show current policies
SELECT 
  policyname, 
  cmd, 
  permissive,
  CASE 
    WHEN qual IS NOT NULL THEN 'USING: ' || qual 
    ELSE 'No USING clause' 
  END as using_clause,
  CASE 
    WHEN with_check IS NOT NULL THEN 'WITH CHECK: ' || with_check 
    ELSE 'No WITH CHECK clause' 
  END as with_check_clause
FROM pg_policies 
WHERE tablename = 'profiles' 
ORDER BY policyname;

-- STEP 4: EMERGENCY ADMIN ACCESS (IF NEEDED)
-- =====================================================================

-- Create emergency admin access for specific emails
CREATE POLICY "profiles_emergency_admin_access" ON profiles
  FOR ALL
  USING (
    EXISTS (
      SELECT 1 FROM auth.users 
      WHERE auth.users.id = auth.uid()
      AND auth.users.email IN (
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>'  -- Add the recent signup email
      )
    )
  )
  WITH CHECK (
    EXISTS (
      SELECT 1 FROM auth.users 
      WHERE auth.users.id = auth.uid()
      AND auth.users.email IN (
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>'  -- Add the recent signup email
      )
    )
  );

-- STEP 5: PRODUCTS TABLE FIX (QUICK)
-- =====================================================================

-- Ensure products policies are not blocking admin access
DROP POLICY IF EXISTS "products_admin_management" ON products;

-- Create simple products policy for authenticated users
CREATE POLICY "products_owner_access" ON products
  FOR ALL
  USING (
    auth.uid() IS NOT NULL 
    AND auth.uid() = supplier_id
  )
  WITH CHECK (
    auth.uid() IS NOT NULL 
    AND auth.uid() = supplier_id
  );

-- Keep public browsing
CREATE POLICY "products_public_browse" ON products
  FOR SELECT
  USING (status = 'active');

-- Service role access
CREATE POLICY "products_service_role_access" ON products
  FOR ALL
  USING (auth.role() = 'service_role')
  WITH CHECK (auth.role() = 'service_role');

COMMIT;
