import { createClient } from '@supabase/supabase-js';

// Supabase configuration
const supabaseUrl = 'https://irkwpzcskeqtasutqnxp.supabase.co';
const supabaseServiceKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imlya3dwemNza2VxdGFzdXRxbnhwIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0NzQ5ODY4MywiZXhwIjoyMDYzMDc0NjgzfQ.PLfCCbh4zgAJDDC6sS82fCU32JYA7e2VmwzvavF-PW8';
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imlya3dwemNza2VxdGFzdXRxbnhwIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDc0OTg2ODMsImV4cCI6MjA2MzA3NDY4M30.bUv9zxx9q_sVUwv_-G6WbHsA53-SU5tCCWbOS_5Xp94';

const supabaseService = createClient(supabaseUrl, supabaseServiceKey);
const supabaseAnon = createClient(supabaseUrl, supabaseAnonKey);

async function verifyWishlistFix() {
  try {
    console.log('🧪 VERIFYING WISHLIST FIX...');
    console.log('='.repeat(50));

    let allTestsPassed = true;

    // CRITICAL TEST 1: Verify admin authentication still works
    console.log('\n🔒 CRITICAL TEST 1: Admin authentication');
    const { data: adminTest, error: adminError } = await supabaseService
      .from('profiles')
      .select('id, email, role')
      .eq('role', 'supplier')
      .limit(1);

    if (adminError) {
      console.error('❌ CRITICAL FAILURE: Admin auth broken!', adminError);
      allTestsPassed = false;
    } else {
      console.log('✅ Admin authentication: WORKING');
    }

    // CRITICAL TEST 2: Verify consumer profiles still work
    console.log('\n👤 CRITICAL TEST 2: Consumer profiles');
    const { data: consumerTest, error: consumerError } = await supabaseService
      .from('profiles')
      .select('id, phone, role')
      .eq('role', 'consumer')
      .limit(1);

    if (consumerError) {
      console.error('❌ CRITICAL FAILURE: Consumer profiles broken!', consumerError);
      allTestsPassed = false;
    } else {
      console.log('✅ Consumer profiles: WORKING');
    }

    // TEST 3: Verify wishlist anonymous SELECT works
    console.log('\n📋 TEST 3: Wishlist anonymous SELECT');
    const { data: selectTest, error: selectError } = await supabaseAnon
      .from('consumer_wishlists')
      .select('*')
      .limit(1);

    if (selectError) {
      console.error('❌ Wishlist SELECT failed:', selectError.message);
      allTestsPassed = false;
    } else {
      console.log('✅ Wishlist SELECT: WORKING');
    }

    // TEST 4: Verify wishlist anonymous INSERT works (THE MAIN FIX)
    console.log('\n➕ TEST 4: Wishlist anonymous INSERT');
    const testPhone = '+213555999888';
    const { data: insertTest, error: insertError } = await supabaseAnon
      .from('consumer_wishlists')
      .insert({
        consumer_phone: testPhone,
        product_id: 'VERIFY-FIX-001',
        product_name: 'Verification Test Product',
        priority: 1
      })
      .select()
      .single();

    if (insertError) {
      console.error('❌ WISHLIST INSERT FAILED:', insertError.message);
      console.error('❌ THE FIX DID NOT WORK');
      allTestsPassed = false;
    } else {
      console.log('✅ Wishlist INSERT: WORKING! 🎉');
      console.log('✅ THE FIX IS SUCCESSFUL! 🎉');

      // TEST 5: Verify UPDATE works
      console.log('\n✏️ TEST 5: Wishlist anonymous UPDATE');
      const { data: updateTest, error: updateError } = await supabaseAnon
        .from('consumer_wishlists')
        .update({ priority: 2 })
        .eq('product_id', 'VERIFY-FIX-001')
        .select()
        .single();

      if (updateError) {
        console.error('❌ Wishlist UPDATE failed:', updateError.message);
        allTestsPassed = false;
      } else {
        console.log('✅ Wishlist UPDATE: WORKING');
      }

      // TEST 6: Verify DELETE works
      console.log('\n🗑️ TEST 6: Wishlist anonymous DELETE');
      const { error: deleteError } = await supabaseAnon
        .from('consumer_wishlists')
        .delete()
        .eq('product_id', 'VERIFY-FIX-001');

      if (deleteError) {
        console.error('❌ Wishlist DELETE failed:', deleteError.message);
        allTestsPassed = false;
      } else {
        console.log('✅ Wishlist DELETE: WORKING');
      }
    }

    // FINAL RESULTS
    console.log('\n' + '='.repeat(50));
    console.log('🎯 VERIFICATION RESULTS:');
    console.log('='.repeat(50));

    if (allTestsPassed) {
      console.log('🎉 ALL TESTS PASSED! 🎉');
      console.log('✅ Admin authentication: WORKING');
      console.log('✅ Consumer profiles: WORKING');
      console.log('✅ Wishlist SELECT: WORKING');
      console.log('✅ Wishlist INSERT: WORKING');
      console.log('✅ Wishlist UPDATE: WORKING');
      console.log('✅ Wishlist DELETE: WORKING');
      console.log('');
      console.log('🚀 WISHLIST FUNCTIONALITY IS NOW FULLY RESTORED!');
      console.log('🛡️ All existing authentication systems remain intact');
      console.log('');
      console.log('📱 You can now test the heart icons in the marketplace:');
      console.log('   1. Go to http://localhost:8080');
      console.log('   2. Authenticate as a consumer with phone');
      console.log('   3. Click heart icons on products');
      console.log('   4. Wishlist should work perfectly!');
    } else {
      console.log('❌ SOME TESTS FAILED');
      console.log('🚨 Please check the errors above');
      console.log('🔧 The fix may need additional adjustments');
    }

  } catch (error) {
    console.error('❌ Verification failed:', error);
  }
}

// Run verification
verifyWishlistFix();
