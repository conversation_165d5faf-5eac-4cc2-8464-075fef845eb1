/**
 * Category Image Manager Component
 * Admin interface for managing category and subcategory images
 */

import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  FolderOpen, 
  Image, 
  Upload, 
  CheckCircle, 
  XCircle, 
  ExternalLink,
  RefreshCw,
  Info
} from 'lucide-react';
import { CategoryImageManager, type ImageManagementReport } from '@/utils/categoryImageManager';
// Note: This component manages the new folder-aware image system
// It doesn't need the old getCategoryImageUrl/getSubcategoryImageUrl functions

export const CategoryImageManagerComponent: React.FC = () => {
  const [report, setReport] = useState<ImageManagementReport | null>(null);
  const [loading, setLoading] = useState(false);
  const [setupLoading, setSetupLoading] = useState(false);

  // Load initial report
  useEffect(() => {
    loadReport();
  }, []);

  const loadReport = async () => {
    setLoading(true);
    try {
      const newReport = await CategoryImageManager.generateReport();
      setReport(newReport);
    } catch (error) {
      console.error('Error loading report:', error);
    } finally {
      setLoading(false);
    }
  };

  const setupFolders = async () => {
    setSetupLoading(true);
    try {
      await CategoryImageManager.setupFolders();
      await loadReport(); // Refresh report after setup
    } catch (error) {
      console.error('Error setting up folders:', error);
    } finally {
      setSetupLoading(false);
    }
  };

  const openSupabaseStorage = () => {
    window.open('https://supabase.com/dashboard/project/irkwpzcskeqtasutqnxp/storage/buckets/category-images', '_blank');
  };

  if (loading && !report) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center p-8">
          <RefreshCw className="h-6 w-6 animate-spin mr-2" />
          Loading image report...
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Category Image Manager</h2>
          <p className="text-muted-foreground">Manage images for all categories and subcategories</p>
        </div>
        <div className="flex gap-2">
          <Button onClick={loadReport} variant="outline" disabled={loading}>
            <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
          <Button onClick={openSupabaseStorage} variant="outline">
            <ExternalLink className="h-4 w-4 mr-2" />
            Open Storage
          </Button>
        </div>
      </div>

      {/* Setup Alert */}
      <Alert>
        <Info className="h-4 w-4" />
        <AlertDescription>
          <strong>How it works:</strong> This system automatically creates folders for each category and subcategory. 
          Simply drag and drop images into the correct folders in Supabase Storage, and they'll appear instantly in the app.
        </AlertDescription>
      </Alert>

      {/* Summary Cards */}
      {report && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-2">
                <FolderOpen className="h-5 w-5 text-blue-500" />
                <div>
                  <p className="text-sm text-muted-foreground">Categories</p>
                  <p className="text-2xl font-bold">{report.totalCategories}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-2">
                <FolderOpen className="h-5 w-5 text-purple-500" />
                <div>
                  <p className="text-sm text-muted-foreground">Subcategories</p>
                  <p className="text-2xl font-bold">{report.totalSubcategories}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-2">
                <CheckCircle className="h-5 w-5 text-green-500" />
                <div>
                  <p className="text-sm text-muted-foreground">With Images</p>
                  <p className="text-2xl font-bold">
                    {report.categoriesWithImages + report.subcategoriesWithImages}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-2">
                <XCircle className="h-5 w-5 text-red-500" />
                <div>
                  <p className="text-sm text-muted-foreground">Missing Images</p>
                  <p className="text-2xl font-bold">{report.missingImages.length}</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Setup Button */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Upload className="h-5 w-5" />
            Folder Setup
          </CardTitle>
          <CardDescription>
            Create folder structure in Supabase Storage for all categories and subcategories
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Button onClick={setupFolders} disabled={setupLoading}>
            {setupLoading ? (
              <>
                <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                Setting up folders...
              </>
            ) : (
              <>
                <FolderOpen className="h-4 w-4 mr-2" />
                Setup All Folders
              </>
            )}
          </Button>
        </CardContent>
      </Card>

      {/* Image Status Tabs */}
      {report && (
        <Tabs defaultValue="missing" className="w-full">
          <TabsList>
            <TabsTrigger value="missing">
              Missing Images ({report.missingImages.length})
            </TabsTrigger>
            <TabsTrigger value="categories">
              Categories ({report.totalCategories})
            </TabsTrigger>
            <TabsTrigger value="subcategories">
              Subcategories ({report.totalSubcategories})
            </TabsTrigger>
          </TabsList>

          <TabsContent value="missing" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Missing Images</CardTitle>
                <CardDescription>
                  Categories and subcategories that need images
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {report.missingImages.map((item) => (
                    <Card key={item.id} className="border-red-200">
                      <CardContent className="p-4">
                        <div className="flex items-start justify-between mb-2">
                          <Badge variant={item.type === 'category' ? 'default' : 'secondary'}>
                            {item.type}
                          </Badge>
                          <XCircle className="h-4 w-4 text-red-500" />
                        </div>
                        <h4 className="font-medium mb-1">{item.displayName}</h4>
                        <p className="text-sm text-muted-foreground mb-2">ID: {item.id}</p>
                        <p className="text-xs text-muted-foreground">
                          Upload to: {item.folderPath}
                        </p>
                        <p className="text-xs text-muted-foreground">
                          File name: {item.suggestedFileName}
                        </p>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="categories" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>All Categories</CardTitle>
                <CardDescription>
                  Status of all category images
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {report.allItems
                    .filter(item => item.type === 'category')
                    .map((item) => (
                      <Card key={item.id} className={item.hasImage ? 'border-green-200' : 'border-red-200'}>
                        <CardContent className="p-4">
                          <div className="flex items-start justify-between mb-2">
                            <Badge variant="default">category</Badge>
                            {item.hasImage ? (
                              <CheckCircle className="h-4 w-4 text-green-500" />
                            ) : (
                              <XCircle className="h-4 w-4 text-red-500" />
                            )}
                          </div>
                          <h4 className="font-medium mb-1">{item.displayName}</h4>
                          <p className="text-sm text-muted-foreground mb-2">ID: {item.id}</p>
                          {item.hasImage && item.imageUrl && (
                            <img 
                              src={item.imageUrl} 
                              alt={item.displayName}
                              className="w-12 h-12 object-contain bg-gray-100 rounded"
                            />
                          )}
                        </CardContent>
                      </Card>
                    ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="subcategories" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>All Subcategories</CardTitle>
                <CardDescription>
                  Status of all subcategory images
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {report.allItems
                    .filter(item => item.type === 'subcategory')
                    .map((item) => (
                      <Card key={item.id} className={item.hasImage ? 'border-green-200' : 'border-red-200'}>
                        <CardContent className="p-4">
                          <div className="flex items-start justify-between mb-2">
                            <Badge variant="secondary">subcategory</Badge>
                            {item.hasImage ? (
                              <CheckCircle className="h-4 w-4 text-green-500" />
                            ) : (
                              <XCircle className="h-4 w-4 text-red-500" />
                            )}
                          </div>
                          <h4 className="font-medium mb-1">{item.displayName}</h4>
                          <p className="text-sm text-muted-foreground mb-2">ID: {item.id}</p>
                          {item.hasImage && item.imageUrl && (
                            <img 
                              src={item.imageUrl} 
                              alt={item.displayName}
                              className="w-12 h-12 object-contain bg-gray-100 rounded"
                            />
                          )}
                        </CardContent>
                      </Card>
                    ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      )}
    </div>
  );
};
