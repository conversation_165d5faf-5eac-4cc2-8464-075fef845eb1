/**
 * CATEGORY DROPDOWN ITEM COMPONENT
 *
 * Simple text-only dropdown item for admin panels
 * Fast and reliable - no images, no database calls
 */

import React from 'react';

interface CategoryDropdownItemProps {
  id: string;
  name: string;
  displayName?: string;
  type: 'category' | 'subcategory';
}

export const CategoryDropdownItem: React.FC<CategoryDropdownItemProps> = ({
  id,
  name,
  displayName,
  type
}) => {
  return (
    <div className="flex items-center">
      {/* Simple text display - fast and reliable */}
      <span className="text-sm">{displayName || name}</span>
    </div>
  );
};

export default CategoryDropdownItem;
