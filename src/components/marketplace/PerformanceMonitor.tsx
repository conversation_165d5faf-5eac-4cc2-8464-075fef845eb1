/**
 * PERFORMANCE MONITORING COMPONENT
 * 
 * Real-time performance metrics display for marketplace optimization:
 * - Load times and response metrics
 * - Cache hit rates and prefetch statistics
 * - Virtual scrolling and infinite scroll metrics
 * - User experience indicators
 */

import React, { useState, useEffect, useCallback } from 'react';
import { useQueryClient } from '@tanstack/react-query';
import { usePrefetchMetrics } from '@/features/products/hooks/useSmartPrefetching';
import { cn } from '@/lib/utils';

interface PerformanceMetrics {
  // Load performance
  initialLoadTime: number;
  categoryLoadTime: number;
  averageImageLoadTime: number;
  
  // Cache performance
  cacheHitRate: number;
  prefetchedCategories: number;
  totalCachedQueries: number;
  
  // Scroll performance
  virtualScrollEnabled: boolean;
  renderedItems: number;
  totalItems: number;
  
  // User experience
  timeToFirstProduct: number;
  timeToInteractive: number;
  scrollFPS: number;
}

interface PerformanceMonitorProps {
  visible?: boolean;
  position?: 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right';
  compact?: boolean;
}

export const PerformanceMonitor: React.FC<PerformanceMonitorProps> = ({
  visible = false,
  position = 'bottom-right',
  compact = false,
}) => {
  const [metrics, setMetrics] = useState<PerformanceMetrics>({
    initialLoadTime: 0,
    categoryLoadTime: 0,
    averageImageLoadTime: 0,
    cacheHitRate: 0,
    prefetchedCategories: 0,
    totalCachedQueries: 0,
    virtualScrollEnabled: false,
    renderedItems: 0,
    totalItems: 0,
    timeToFirstProduct: 0,
    timeToInteractive: 0,
    scrollFPS: 0,
  });

  const [isExpanded, setIsExpanded] = useState(!compact);
  const queryClient = useQueryClient();
  const { getMetrics: getPrefetchMetrics } = usePrefetchMetrics();

  // Update metrics periodically
  const updateMetrics = useCallback(() => {
    try {
      // Get prefetch metrics
      const prefetchMetrics = getPrefetchMetrics();
      
      // Get performance timing
      const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
      const initialLoadTime = navigation.loadEventEnd - navigation.fetchStart;
      
      // Get cache metrics
      const cache = queryClient.getQueryCache();
      const allQueries = cache.getAll();
      const marketplaceQueries = allQueries.filter(q => 
        q.queryKey[0]?.toString().includes('marketplace')
      );
      
      setMetrics(prev => ({
        ...prev,
        initialLoadTime,
        cacheHitRate: prefetchMetrics.prefetchHitRate * 100,
        prefetchedCategories: prefetchMetrics.cachedCategories,
        totalCachedQueries: prefetchMetrics.totalCachedQueries,
        timeToFirstProduct: initialLoadTime,
      }));
    } catch (error) {
      console.warn('[PERFORMANCE_MONITOR] Error updating metrics:', error);
    }
  }, [queryClient, getPrefetchMetrics]);

  // Update metrics every 2 seconds
  useEffect(() => {
    if (!visible) return;

    updateMetrics();
    const interval = setInterval(updateMetrics, 2000);
    return () => clearInterval(interval);
  }, [visible, updateMetrics]);

  // FPS monitoring
  useEffect(() => {
    if (!visible) return;

    let frameCount = 0;
    let lastTime = performance.now();
    
    const measureFPS = () => {
      frameCount++;
      const currentTime = performance.now();
      
      if (currentTime - lastTime >= 1000) {
        setMetrics(prev => ({
          ...prev,
          scrollFPS: Math.round(frameCount * 1000 / (currentTime - lastTime)),
        }));
        frameCount = 0;
        lastTime = currentTime;
      }
      
      requestAnimationFrame(measureFPS);
    };
    
    const rafId = requestAnimationFrame(measureFPS);
    return () => cancelAnimationFrame(rafId);
  }, [visible]);

  if (!visible) return null;

  const positionClasses = {
    'top-left': 'top-4 left-4',
    'top-right': 'top-4 right-4',
    'bottom-left': 'bottom-4 left-4',
    'bottom-right': 'bottom-4 right-4',
  };

  const getPerformanceColor = (value: number, thresholds: { good: number; ok: number }) => {
    if (value <= thresholds.good) return 'text-green-600';
    if (value <= thresholds.ok) return 'text-yellow-600';
    return 'text-red-600';
  };

  return (
    <div
      className={cn(
        'fixed z-50 bg-white/95 backdrop-blur-sm border border-gray-200 rounded-lg shadow-lg',
        'transition-all duration-300 ease-in-out',
        positionClasses[position],
        isExpanded ? 'w-80' : 'w-16 h-16'
      )}
    >
      {/* Toggle button */}
      <button
        onClick={() => setIsExpanded(!isExpanded)}
        className="absolute top-2 right-2 w-6 h-6 flex items-center justify-center text-gray-500 hover:text-gray-700 transition-colors"
        title={isExpanded ? 'Collapse' : 'Expand'}
      >
        {isExpanded ? '−' : '📊'}
      </button>

      {isExpanded ? (
        <div className="p-4 space-y-3">
          <h3 className="text-sm font-semibold text-gray-800 mb-2">
            Performance Monitor
          </h3>

          {/* Load Performance */}
          <div className="space-y-1">
            <h4 className="text-xs font-medium text-gray-600">Load Performance</h4>
            <div className="grid grid-cols-2 gap-2 text-xs">
              <div>
                <span className="text-gray-500">Initial Load:</span>
                <span className={cn('ml-1 font-mono', getPerformanceColor(metrics.initialLoadTime, { good: 2000, ok: 5000 }))}>
                  {metrics.initialLoadTime.toFixed(0)}ms
                </span>
              </div>
              <div>
                <span className="text-gray-500">First Product:</span>
                <span className={cn('ml-1 font-mono', getPerformanceColor(metrics.timeToFirstProduct, { good: 1500, ok: 3000 }))}>
                  {metrics.timeToFirstProduct.toFixed(0)}ms
                </span>
              </div>
            </div>
          </div>

          {/* Cache Performance */}
          <div className="space-y-1">
            <h4 className="text-xs font-medium text-gray-600">Cache Performance</h4>
            <div className="grid grid-cols-2 gap-2 text-xs">
              <div>
                <span className="text-gray-500">Hit Rate:</span>
                <span className={cn('ml-1 font-mono', getPerformanceColor(100 - metrics.cacheHitRate, { good: 20, ok: 50 }))}>
                  {metrics.cacheHitRate.toFixed(1)}%
                </span>
              </div>
              <div>
                <span className="text-gray-500">Prefetched:</span>
                <span className="ml-1 font-mono text-blue-600">
                  {metrics.prefetchedCategories}
                </span>
              </div>
            </div>
          </div>

          {/* Scroll Performance */}
          <div className="space-y-1">
            <h4 className="text-xs font-medium text-gray-600">Scroll Performance</h4>
            <div className="grid grid-cols-2 gap-2 text-xs">
              <div>
                <span className="text-gray-500">FPS:</span>
                <span className={cn('ml-1 font-mono', getPerformanceColor(60 - metrics.scrollFPS, { good: 10, ok: 20 }))}>
                  {metrics.scrollFPS}
                </span>
              </div>
              <div>
                <span className="text-gray-500">Virtual:</span>
                <span className={cn('ml-1', metrics.virtualScrollEnabled ? 'text-green-600' : 'text-gray-400')}>
                  {metrics.virtualScrollEnabled ? 'ON' : 'OFF'}
                </span>
              </div>
            </div>
          </div>

          {/* Status indicator */}
          <div className="pt-2 border-t border-gray-100">
            <div className="flex items-center justify-between text-xs">
              <span className="text-gray-500">Status:</span>
              <div className="flex items-center space-x-1">
                <div className={cn(
                  'w-2 h-2 rounded-full',
                  metrics.initialLoadTime < 3000 ? 'bg-green-500' : 
                  metrics.initialLoadTime < 6000 ? 'bg-yellow-500' : 'bg-red-500'
                )} />
                <span className={cn(
                  'font-medium',
                  metrics.initialLoadTime < 3000 ? 'text-green-600' : 
                  metrics.initialLoadTime < 6000 ? 'text-yellow-600' : 'text-red-600'
                )}>
                  {metrics.initialLoadTime < 3000 ? 'Excellent' : 
                   metrics.initialLoadTime < 6000 ? 'Good' : 'Needs Optimization'}
                </span>
              </div>
            </div>
          </div>
        </div>
      ) : (
        <div className="w-full h-full flex items-center justify-center">
          <div className={cn(
            'w-3 h-3 rounded-full',
            metrics.initialLoadTime < 3000 ? 'bg-green-500' : 
            metrics.initialLoadTime < 6000 ? 'bg-yellow-500' : 'bg-red-500'
          )} />
        </div>
      )}
    </div>
  );
};

export default PerformanceMonitor;
