/**
 * SAFE OPTIMIZED MARKETPLACE
 * 
 * Gradual optimization enablement with fallback mechanisms:
 * - Progressive enhancement approach
 * - Automatic fallback on errors
 * - Performance monitoring
 * - Feature flags for safe rollout
 */

import React, { useState, useEffect, useRef } from 'react';
import { ErrorBoundary } from '@/components/ErrorBoundary';
import { InfiniteScrollMarketplace } from './InfiniteScrollMarketplace';
import { PerformanceMonitor } from './PerformanceMonitor';
import { useFilter } from '@/contexts/FilterContext';
import { useMarketplaceProducts } from '@/features/products/hooks/useMarketplaceProducts';
import { useSmartPrefetching } from '@/features/products/hooks/useSmartPrefetching';
import { AirbnbStyleProductCard } from './AirbnbStyleProductCard';
import { cn } from '@/lib/utils';

interface SafeOptimizedMarketplaceProps {
  section: 'retail' | 'wholesale';
  className?: string;
}

/**
 * Feature flags for gradual optimization rollout
 * PERFORMANCE MODE: Full optimizations enabled with safety fallbacks
 */
const OPTIMIZATION_FLAGS = {
  enableInfiniteScroll: true, // ENABLED: Database schema fixed
  enableVirtualScrolling: true, // ENABLED: For large datasets (>50 products)
  enableSmartCaching: true, // ENABLED: Critical for performance
  enablePerformanceMonitoring: true, // ENABLED: Monitor performance gains
  fallbackToLegacy: true, // ENABLED: Safety net remains active
};

/**
 * Legacy Marketplace Component (Fallback)
 */
const LegacyMarketplace: React.FC<{
  selectedCategory: string | null;
  section: 'retail' | 'wholesale';
  className?: string;
}> = ({ selectedCategory, section, className }) => {
  // Use the proven legacy hooks
  const { products: tyreProducts, isLoading: tyresLoading } = useMarketplaceProducts('tyres');
  const { products: brakeProducts, isLoading: brakesLoading } = useMarketplaceProducts('brakes');
  const { products: filterProducts, isLoading: filtersLoading } = useMarketplaceProducts('filters');
  const { products: oilsFluidProducts, isLoading: oilsFluidLoading } = useMarketplaceProducts('oils-fluids');
  const { products: engineProducts, isLoading: engineLoading } = useMarketplaceProducts('engine');

  // Get products for selected category
  const getProductsForCategory = () => {
    switch (selectedCategory) {
      case 'tyres': return tyreProducts;
      case 'brakes': return brakeProducts;
      case 'filters': return filterProducts;
      case 'oils-fluids': return oilsFluidProducts;
      case 'engine': return engineProducts;
      default: return [];
    }
  };

  const products = getProductsForCategory();
  const isLoading = tyresLoading || brakesLoading || filtersLoading || oilsFluidLoading || engineLoading;

  if (isLoading) {
    return (
      <div className={cn("py-12 text-center", className)}>
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
        <p className="text-gray-600">Loading products...</p>
      </div>
    );
  }

  if (!selectedCategory || selectedCategory === 'all') {
    return (
      <div className={cn("py-12 text-center", className)}>
        <p className="text-gray-600">Please select a category to view products.</p>
      </div>
    );
  }

  if (products.length === 0) {
    return (
      <div className={cn("py-12 text-center", className)}>
        <p className="text-gray-600">No products found in this category.</p>
      </div>
    );
  }

  return (
    <div className={cn("space-y-6", className)}>
      <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
        <div className="flex items-center">
          <svg className="w-5 h-5 text-yellow-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 18.5c-.77.833.192 2.5 1.732 2.5z" />
          </svg>
          <span className="text-yellow-800 text-sm">
            Running in legacy mode - {products.length} products loaded
          </span>
        </div>
      </div>
      
      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-6">
        {products.map((product, index) => (
          <AirbnbStyleProductCard
            key={`${product.id}-${index}`}
            product={product}
            section={section}
          />
        ))}
      </div>
    </div>
  );
};

/**
 * Optimization Status Component
 */
const OptimizationStatus: React.FC<{
  isOptimized: boolean;
  hasError: boolean;
  performanceMetrics?: any;
}> = ({ isOptimized, hasError, performanceMetrics }) => {
  if (hasError) {
    return (
      <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
        <div className="flex items-center">
          <svg className="w-5 h-5 text-red-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          <span className="text-red-800 text-sm font-medium">
            Optimization failed - using legacy mode
          </span>
        </div>
      </div>
    );
  }

  if (isOptimized) {
    return (
      <div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <svg className="w-5 h-5 text-green-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
            </svg>
            <span className="text-green-800 text-sm font-medium">
              Optimized mode active - Infinite scroll enabled
            </span>
          </div>
          {performanceMetrics && (
            <span className="text-green-700 text-xs">
              {performanceMetrics.loadedCount} products loaded
            </span>
          )}
        </div>
      </div>
    );
  }

  return null;
};

/**
 * Main Safe Optimized Marketplace Component
 */
export const SafeOptimizedMarketplace: React.FC<SafeOptimizedMarketplaceProps> = ({
  section,
  className,
}) => {
  const { selectedCategory } = useFilter();
  const [optimizationError, setOptimizationError] = useState<Error | null>(null);
  const [useOptimized, setUseOptimized] = useState(OPTIMIZATION_FLAGS.enableInfiniteScroll);

  // Debug logging
  useEffect(() => {
    console.log('[SAFE_MARKETPLACE] Component mounted with:', {
      selectedCategory,
      section,
      useOptimized,
      optimizationFlags: OPTIMIZATION_FLAGS
    });
  }, [selectedCategory, section, useOptimized]);

  // Reset error when category changes
  useEffect(() => {
    setOptimizationError(null);
    console.log('[SAFE_MARKETPLACE] Category changed to:', selectedCategory);
  }, [selectedCategory]);

  // Performance monitoring
  const startTime = useRef<number>(Date.now());
  useEffect(() => {
    startTime.current = Date.now();
  }, [selectedCategory]);

  // Smart prefetching for instant category switching
  const { prefetchCategory } = useSmartPrefetching({
    currentCategory: selectedCategory,
    enabled: OPTIMIZATION_FLAGS.enableSmartCaching,
  });

  // Error handler for optimization failures
  const handleOptimizationError = (error: Error) => {
    const loadTime = Date.now() - startTime.current;
    console.error('[SAFE_OPTIMIZATION] Optimization failed after', loadTime, 'ms, falling back to legacy:', error);
    setOptimizationError(error);
    setUseOptimized(false);

    // Report performance degradation
    if (OPTIMIZATION_FLAGS.enablePerformanceMonitoring) {
      console.warn('[PERFORMANCE] Optimization failure detected:', {
        error: error.message,
        category: selectedCategory,
        loadTime,
        timestamp: new Date().toISOString(),
      });
    }
  };

  // Optimized marketplace with error boundary
  const OptimizedMarketplace = () => (
    <ErrorBoundary
      fallback={
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <p className="text-red-800 text-sm">
            Optimization failed. Switching to legacy mode...
          </p>
        </div>
      }
    >
      <InfiniteScrollMarketplace
        selectedCategory={selectedCategory}
        section={section}
        enableVirtualScrolling={OPTIMIZATION_FLAGS.enableVirtualScrolling}
        batchSize={12} // PERFORMANCE: Smaller initial load for faster response
        loadMoreThreshold={0.7} // PERFORMANCE: Load earlier for smoother UX
        showMetrics={OPTIMIZATION_FLAGS.enablePerformanceMonitoring}
        className={className}
      />
    </ErrorBoundary>
  );

  return (
    <div className="space-y-4">
      {/* Optimization status */}
      <OptimizationStatus
        isOptimized={useOptimized && !optimizationError}
        hasError={!!optimizationError}
      />

      {/* Marketplace content */}
      {useOptimized && !optimizationError && OPTIMIZATION_FLAGS.enableInfiniteScroll ? (
        <OptimizedMarketplace />
      ) : (
        <LegacyMarketplace
          selectedCategory={selectedCategory}
          section={section}
          className={className}
        />
      )}

      {/* Development controls */}
      {process.env.NODE_ENV === 'development' && (
        <div className="fixed bottom-4 right-4 bg-white border border-gray-300 rounded-lg p-4 shadow-lg">
          <h4 className="text-sm font-medium mb-2">Optimization Controls</h4>
          <div className="space-y-2 text-xs">
            <label className="flex items-center">
              <input
                type="checkbox"
                checked={useOptimized}
                onChange={(e) => setUseOptimized(e.target.checked)}
                className="mr-2"
              />
              Enable Optimizations
            </label>
            <div className="text-gray-600">
              Mode: {useOptimized && !optimizationError ? 'Optimized' : 'Legacy'}
            </div>
            {optimizationError && (
              <button
                onClick={() => {
                  setOptimizationError(null);
                  setUseOptimized(true);
                }}
                className="text-blue-600 hover:text-blue-800"
              >
                Retry Optimization
              </button>
            )}
          </div>
        </div>
      )}

      {/* Performance Monitor */}
      {OPTIMIZATION_FLAGS.enablePerformanceMonitoring && (
        <PerformanceMonitor
          visible={true}
          position="bottom-right"
          compact={true}
        />
      )}
    </div>
  );
};

export default SafeOptimizedMarketplace;
