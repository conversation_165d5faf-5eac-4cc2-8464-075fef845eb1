/**
 * INFINITE SCROLL MARKETPLACE COMPONENT
 * 
 * Enhanced marketplace with infinite scroll, virtual scrolling, and optimized performance:
 * - Seamless infinite scrolling
 * - Virtual scrolling for large datasets
 * - Professional loading states
 * - Optimized batch loading
 * - Performance monitoring
 */

import React, { useState, useCallback, useMemo } from 'react';
import { useInfiniteMarketplaceProducts } from '@/features/products/hooks/useOptimizedMarketplaceProducts';
import { useInfiniteScrollPrefetch } from '@/features/products/hooks/useSmartPrefetching';
import { InfiniteScrollLoader, useInfiniteScrollTrigger } from './InfiniteScrollLoader';
import { VirtualizedProductGrid, useResponsiveGrid } from './VirtualizedProductGrid';
import { AirbnbStyleProductCard } from './AirbnbStyleProductCard';
import { TyreProduct, BrakeProduct } from '@/features/products/types/product.types';
import { cn } from '@/lib/utils';

// Type for combined product data
type AnyProduct = TyreProduct | BrakeProduct;

interface InfiniteScrollMarketplaceProps {
  selectedCategory: string | null;
  section: 'retail' | 'wholesale';
  className?: string;
  
  // Performance options
  enableVirtualScrolling?: boolean;
  batchSize?: number;
  loadMoreThreshold?: number;
  
  // UI options
  showMetrics?: boolean;
  gridLayout?: 'standard' | 'compact' | 'large';
}

/**
 * Grid layout configurations
 */
const GRID_LAYOUTS = {
  standard: {
    itemWidth: 280,
    itemHeight: 400,
    gap: 24,
    columns: 'grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5',
  },
  compact: {
    itemWidth: 240,
    itemHeight: 320,
    gap: 16,
    columns: 'grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6',
  },
  large: {
    itemWidth: 320,
    itemHeight: 480,
    gap: 32,
    columns: 'grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4',
  },
};

/**
 * Standard Grid Component (Non-virtualized)
 */
const StandardProductGrid: React.FC<{
  products: AnyProduct[];
  section: 'retail' | 'wholesale';
  layout: keyof typeof GRID_LAYOUTS;
  className?: string;
}> = ({ products, section, layout, className }) => {
  const gridConfig = GRID_LAYOUTS[layout];
  
  return (
    <div className={cn(`grid ${gridConfig.columns} gap-6`, className)}>
      {products.map((product, index) => (
        <AirbnbStyleProductCard
          key={`${product.id}-${index}`}
          product={product}
          section={section}
        />
      ))}
    </div>
  );
};

/**
 * Main Infinite Scroll Marketplace Component
 */
export const InfiniteScrollMarketplace: React.FC<InfiniteScrollMarketplaceProps> = ({
  selectedCategory,
  section,
  className,
  enableVirtualScrolling = false,
  batchSize = 12, // PERFORMANCE: Reduced for faster initial load (3 rows of 4 cards)
  loadMoreThreshold = 0.7, // PERFORMANCE: Load earlier for smoother experience
  showMetrics = true,
  gridLayout = 'standard',
}) => {
  // Responsive grid dimensions for virtual scrolling
  const { width, height, itemWidth } = useResponsiveGrid();
  
  // Infinite scroll hook
  const {
    products,
    totalCount,
    currentPage,
    totalPages,
    hasNextPage,
    canLoadMore,
    fetchNextPage,
    isInitialLoading,
    isLoadingMore,
    isFetching,
    isError,
    error,
    refetch,
    loadedPages,
    isEmpty,
  } = useInfiniteMarketplaceProducts(selectedCategory, batchSize, !!selectedCategory);

  // Performance metrics
  const performanceMetrics = useMemo(() => {
    const loadedCount = products.length;
    const loadingProgress = totalCount > 0 ? (loadedCount / totalCount) * 100 : 0;
    const averageProductsPerPage = loadedPages > 0 ? loadedCount / loadedPages : 0;
    
    return {
      loadedCount,
      totalCount,
      loadingProgress,
      loadedPages,
      averageProductsPerPage,
      isVirtualized: enableVirtualScrolling && loadedCount > 50,
    };
  }, [products.length, totalCount, loadedPages, enableVirtualScrolling]);

  // Load more handler
  const handleLoadMore = useCallback(() => {
    if (canLoadMore && !isLoadingMore) {
      console.log(`[INFINITE_SCROLL] Loading more products... (Page ${currentPage + 1})`);
      fetchNextPage();
    }
  }, [canLoadMore, isLoadingMore, currentPage, fetchNextPage]);

  // Smart prefetching for next page
  useInfiniteScrollPrefetch(
    selectedCategory,
    currentPage,
    hasNextPage,
    !!selectedCategory
  );

  // Intersection observer trigger for automatic loading
  const triggerRef = useInfiniteScrollTrigger(
    handleLoadMore,
    hasNextPage,
    isLoadingMore,
    loadMoreThreshold
  );

  // Log performance metrics
  React.useEffect(() => {
    if (products.length > 0) {
      console.log('[INFINITE_SCROLL_PERFORMANCE]', {
        category: selectedCategory,
        loadedProducts: products.length,
        totalProducts: totalCount,
        loadedPages: loadedPages,
        isVirtualized: performanceMetrics.isVirtualized,
        averageProductsPerPage: performanceMetrics.averageProductsPerPage.toFixed(1),
      });
    }
  }, [products.length, selectedCategory, performanceMetrics]);

  // No category selected
  if (!selectedCategory || selectedCategory === 'all') {
    return (
      <div className={cn("py-12 text-center", className)}>
        <div className="max-w-md mx-auto">
          <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <svg className="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
            </svg>
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            Select a Category
          </h3>
          <p className="text-gray-600">
            Choose a category from the navigation above to browse products.
          </p>
        </div>
      </div>
    );
  }

  // Initial loading state
  if (isInitialLoading) {
    return (
      <InfiniteScrollLoader
        isInitialLoading={true}
        loadingText={`Loading ${selectedCategory} products...`}
        className={className}
      />
    );
  }

  // Error state
  if (isError) {
    return (
      <InfiniteScrollLoader
        isError={true}
        error={error}
        onRetry={() => refetch()}
        className={className}
      />
    );
  }

  // Empty state
  if (isEmpty) {
    return (
      <div className={cn("py-12 text-center", className)}>
        <div className="max-w-md mx-auto">
          <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <svg className="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
            </svg>
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            No Products Found
          </h3>
          <p className="text-gray-600">
            No products available in the {selectedCategory} category.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className={cn("space-y-6", className)}>
      {/* Performance metrics header */}
      {showMetrics && (
        <div className="bg-blue-50 rounded-lg p-4">
          <div className="flex items-center justify-between text-sm">
            <div className="flex items-center space-x-4">
              <span className="font-medium text-blue-900">
                {performanceMetrics.loadedCount.toLocaleString()} products loaded
              </span>
              <span className="text-blue-700">
                Page {currentPage} of {totalPages}
              </span>
              {performanceMetrics.isVirtualized && (
                <span className="bg-blue-200 text-blue-800 px-2 py-1 rounded text-xs font-medium">
                  Virtual Scrolling Active
                </span>
              )}
            </div>
            <div className="text-blue-700">
              {performanceMetrics.loadingProgress.toFixed(1)}% loaded
            </div>
          </div>
        </div>
      )}

      {/* Product grid */}
      {enableVirtualScrolling && products.length > 50 ? (
        <VirtualizedProductGrid
          products={products}
          section={section}
          width={width}
          height={height}
          itemWidth={itemWidth}
          hasNextPage={hasNextPage}
          isLoadingMore={isLoadingMore}
          onLoadMore={handleLoadMore}
        />
      ) : (
        <StandardProductGrid
          products={products}
          section={section}
          layout={gridLayout}
        />
      )}

      {/* Infinite scroll trigger */}
      <div ref={triggerRef} className="h-4" />

      {/* Load more section */}
      <InfiniteScrollLoader
        isLoadingMore={isLoadingMore}
        hasNextPage={hasNextPage}
        canLoadMore={canLoadMore}
        loadedCount={products.length}
        totalCount={totalCount}
        currentPage={currentPage}
        totalPages={totalPages}
        onLoadMore={handleLoadMore}
        showMetrics={showMetrics}
      />
    </div>
  );
};

export default InfiniteScrollMarketplace;
