/**
 * INFINITE SCROLL LOADER COMPONENT
 * 
 * Professional loading states for infinite scroll with:
 * - 3-button loading animation
 * - Smooth transitions
 * - Load more button
 * - Error handling
 * - Performance metrics
 */

import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Loader2, <PERSON>ert<PERSON>ircle, ChevronDown } from 'lucide-react';
import { cn } from '@/lib/utils';

interface InfiniteScrollLoaderProps {
  // Loading states
  isInitialLoading?: boolean;
  isLoadingMore?: boolean;
  hasNextPage?: boolean;
  canLoadMore?: boolean;
  
  // Data
  loadedCount?: number;
  totalCount?: number;
  currentPage?: number;
  totalPages?: number;
  
  // Error handling
  isError?: boolean;
  error?: Error | null;
  
  // Actions
  onLoadMore?: () => void;
  onRetry?: () => void;
  
  // Customization
  className?: string;
  showMetrics?: boolean;
  loadMoreText?: string;
  loadingText?: string;
}

/**
 * 3-Button Loading Animation Component
 */
const ThreeButtonLoader: React.FC<{ className?: string }> = ({ className }) => (
  <div className={cn("flex items-center justify-center space-x-2", className)}>
    <div className="flex space-x-1">
      <div className="w-2 h-2 bg-blue-500 rounded-full animate-bounce [animation-delay:-0.3s]"></div>
      <div className="w-2 h-2 bg-blue-500 rounded-full animate-bounce [animation-delay:-0.15s]"></div>
      <div className="w-2 h-2 bg-blue-500 rounded-full animate-bounce"></div>
    </div>
  </div>
);

/**
 * Loading Skeleton for Products
 */
const ProductLoadingSkeleton: React.FC<{ count?: number }> = ({ count = 4 }) => (
  <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-6">
    {Array.from({ length: count }).map((_, index) => (
      <div key={index} className="animate-pulse">
        <div className="aspect-square bg-gray-200 rounded-xl mb-3"></div>
        <div className="space-y-2">
          <div className="h-4 bg-gray-200 rounded w-3/4"></div>
          <div className="h-3 bg-gray-200 rounded w-1/2"></div>
          <div className="h-4 bg-gray-200 rounded w-1/3"></div>
        </div>
      </div>
    ))}
  </div>
);

/**
 * Main Infinite Scroll Loader Component
 */
export const InfiniteScrollLoader: React.FC<InfiniteScrollLoaderProps> = ({
  isInitialLoading = false,
  isLoadingMore = false,
  hasNextPage = false,
  canLoadMore = false,
  loadedCount = 0,
  totalCount = 0,
  currentPage = 0,
  totalPages = 0,
  isError = false,
  error = null,
  onLoadMore,
  onRetry,
  className,
  showMetrics = true,
  loadMoreText = "Load More Products",
  loadingText = "Loading products..."
}) => {
  // Initial loading state
  if (isInitialLoading) {
    return (
      <div className={cn("py-12", className)}>
        <div className="text-center mb-8">
          <ThreeButtonLoader className="mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            {loadingText}
          </h3>
          <p className="text-gray-600">
            Please wait while we fetch the latest products...
          </p>
        </div>
        <ProductLoadingSkeleton count={8} />
      </div>
    );
  }

  // Error state
  if (isError && error) {
    return (
      <div className={cn("py-12 text-center", className)}>
        <div className="max-w-md mx-auto">
          <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            Failed to Load Products
          </h3>
          <p className="text-gray-600 mb-6">
            {error.message || "Something went wrong while loading products."}
          </p>
          {onRetry && (
            <Button 
              onClick={onRetry}
              className="bg-blue-600 hover:bg-blue-700"
            >
              Try Again
            </Button>
          )}
        </div>
      </div>
    );
  }

  // Load more section
  if (hasNextPage || isLoadingMore) {
    return (
      <div className={cn("py-8 text-center", className)}>
        {/* Progress metrics */}
        {showMetrics && totalCount > 0 && (
          <div className="mb-6">
            <div className="text-sm text-gray-600 mb-2">
              Showing {loadedCount.toLocaleString()} of {totalCount.toLocaleString()} products
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2 max-w-md mx-auto">
              <div 
                className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                style={{ width: `${Math.min((loadedCount / totalCount) * 100, 100)}%` }}
              ></div>
            </div>
            <div className="text-xs text-gray-500 mt-1">
              Page {currentPage} of {totalPages}
            </div>
          </div>
        )}

        {/* Loading more state */}
        {isLoadingMore ? (
          <div className="space-y-4">
            <ThreeButtonLoader />
            <p className="text-gray-600">Loading more products...</p>
            <ProductLoadingSkeleton count={4} />
          </div>
        ) : (
          /* Load more button */
          canLoadMore && onLoadMore && (
            <Button
              onClick={onLoadMore}
              className="bg-blue-600 hover:bg-blue-700 text-white px-8 py-3 rounded-lg font-medium transition-all duration-200 hover:scale-105 active:scale-95"
              disabled={isLoadingMore}
            >
              <ChevronDown className="w-4 h-4 mr-2" />
              {loadMoreText}
            </Button>
          )
        )}
      </div>
    );
  }

  // End of results
  if (loadedCount > 0 && !hasNextPage) {
    return (
      <div className={cn("py-8 text-center", className)}>
        <div className="max-w-md mx-auto">
          <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <svg className="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
            </svg>
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            All Products Loaded
          </h3>
          <p className="text-gray-600">
            You've seen all {loadedCount.toLocaleString()} products in this category.
          </p>
        </div>
      </div>
    );
  }

  return null;
};

/**
 * Intersection Observer Hook for Infinite Scroll
 */
export const useInfiniteScrollTrigger = (
  onLoadMore: () => void,
  hasNextPage: boolean,
  isLoadingMore: boolean,
  threshold: number = 0.8
) => {
  const triggerRef = React.useRef<HTMLDivElement>(null);

  React.useEffect(() => {
    const trigger = triggerRef.current;
    if (!trigger || !hasNextPage || isLoadingMore) return;

    const observer = new IntersectionObserver(
      (entries) => {
        const [entry] = entries;
        if (entry.isIntersecting) {
          console.log('[INFINITE_SCROLL] Trigger reached, loading more...');
          onLoadMore();
        }
      },
      { threshold }
    );

    observer.observe(trigger);

    return () => {
      observer.unobserve(trigger);
    };
  }, [onLoadMore, hasNextPage, isLoadingMore, threshold]);

  return triggerRef;
};

export default InfiniteScrollLoader;
