/**
 * VIRTUALIZED PRODUCT GRID
 * 
 * High-performance virtual scrolling component for handling millions of products:
 * - Only renders visible items
 * - Smooth scrolling performance
 * - Memory efficient
 * - Responsive grid layout
 * - Infinite scroll integration
 */

import React, { useMemo, useCallback } from 'react';
import { FixedSizeGrid as Grid } from 'react-window';
import { AirbnbStyleProductCard } from './AirbnbStyleProductCard';
import { TyreProduct, BrakeProduct } from '@/features/products/types/product.types';
import { cn } from '@/lib/utils';

// Type for combined product data
type AnyProduct = TyreProduct | BrakeProduct;

interface VirtualizedProductGridProps {
  products: AnyProduct[];
  section: 'retail' | 'wholesale';
  className?: string;
  
  // Grid configuration
  itemWidth?: number;
  itemHeight?: number;
  gap?: number;
  
  // Container dimensions
  width?: number;
  height?: number;
  
  // Infinite scroll
  hasNextPage?: boolean;
  isLoadingMore?: boolean;
  onLoadMore?: () => void;
  
  // Performance
  overscan?: number;
  useIsScrolling?: boolean;
}

/**
 * Calculate responsive grid dimensions
 */
const useGridDimensions = (
  containerWidth: number,
  itemWidth: number,
  gap: number
) => {
  return useMemo(() => {
    // Calculate how many columns can fit
    const availableWidth = containerWidth - gap;
    const itemWithGap = itemWidth + gap;
    const columnCount = Math.max(1, Math.floor(availableWidth / itemWithGap));
    
    // Adjust item width to fill available space
    const totalGapWidth = (columnCount - 1) * gap;
    const adjustedItemWidth = (containerWidth - totalGapWidth) / columnCount;
    
    return {
      columnCount,
      adjustedItemWidth: Math.floor(adjustedItemWidth),
    };
  }, [containerWidth, itemWidth, gap]);
};

/**
 * Grid Cell Component
 */
interface GridCellProps {
  columnIndex: number;
  rowIndex: number;
  style: React.CSSProperties;
  data: {
    products: AnyProduct[];
    columnCount: number;
    section: 'retail' | 'wholesale';
    gap: number;
    onLoadMore?: () => void;
    hasNextPage?: boolean;
    isLoadingMore?: boolean;
  };
}

const GridCell: React.FC<GridCellProps> = ({ 
  columnIndex, 
  rowIndex, 
  style, 
  data 
}) => {
  const { 
    products, 
    columnCount, 
    section, 
    gap,
    onLoadMore,
    hasNextPage,
    isLoadingMore
  } = data;
  
  const productIndex = rowIndex * columnCount + columnIndex;
  const product = products[productIndex];
  
  // Trigger load more when approaching the end
  const shouldTriggerLoadMore = useMemo(() => {
    const remainingItems = products.length - productIndex;
    const triggerThreshold = columnCount * 2; // 2 rows before end
    return remainingItems <= triggerThreshold && hasNextPage && !isLoadingMore;
  }, [productIndex, products.length, columnCount, hasNextPage, isLoadingMore]);
  
  // Trigger load more effect
  React.useEffect(() => {
    if (shouldTriggerLoadMore && onLoadMore) {
      console.log('[VIRTUAL_SCROLL] Triggering load more from virtual grid');
      onLoadMore();
    }
  }, [shouldTriggerLoadMore, onLoadMore]);
  
  // Don't render if no product
  if (!product) {
    return <div style={style} />;
  }
  
  return (
    <div
      style={{
        ...style,
        padding: `${gap / 2}px`,
      }}
    >
      <AirbnbStyleProductCard
        product={product}
        section={section}
      />
    </div>
  );
};

/**
 * Main Virtualized Product Grid Component
 */
export const VirtualizedProductGrid: React.FC<VirtualizedProductGridProps> = ({
  products,
  section,
  className,
  itemWidth = 280,
  itemHeight = 400,
  gap = 24,
  width = 1200,
  height = 600,
  hasNextPage = false,
  isLoadingMore = false,
  onLoadMore,
  overscan = 5,
  useIsScrolling = true,
}) => {
  // Calculate grid dimensions
  const { columnCount, adjustedItemWidth } = useGridDimensions(width, itemWidth, gap);
  
  // Calculate row count
  const rowCount = Math.ceil(products.length / columnCount);
  
  // Adjusted item height to maintain aspect ratio
  const aspectRatio = itemHeight / itemWidth;
  const adjustedItemHeight = adjustedItemWidth * aspectRatio;
  
  // Grid data for cells
  const gridData = useMemo(() => ({
    products,
    columnCount,
    section,
    gap,
    onLoadMore,
    hasNextPage,
    isLoadingMore,
  }), [products, columnCount, section, gap, onLoadMore, hasNextPage, isLoadingMore]);
  
  // Handle scroll events for performance monitoring
  const handleScroll = useCallback(({ scrollTop, scrollLeft }: any) => {
    // Optional: Add scroll performance monitoring here
    console.log(`[VIRTUAL_SCROLL] Scrolled to: ${scrollTop}px`);
  }, []);
  
  if (products.length === 0) {
    return (
      <div className={cn("flex items-center justify-center py-12", className)}>
        <div className="text-center">
          <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <svg className="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
            </svg>
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">No Products Found</h3>
          <p className="text-gray-600">Try adjusting your filters or search terms.</p>
        </div>
      </div>
    );
  }
  
  return (
    <div className={cn("w-full", className)}>
      {/* Performance metrics */}
      <div className="mb-4 text-sm text-gray-600">
        <span>Showing {products.length.toLocaleString()} products</span>
        <span className="ml-4">Grid: {columnCount} columns × {rowCount} rows</span>
        <span className="ml-4">Virtual rendering: {Math.min(overscan * 2 + 1, rowCount)} visible rows</span>
      </div>
      
      {/* Virtual Grid */}
      <Grid
        columnCount={columnCount}
        columnWidth={adjustedItemWidth + gap}
        height={height}
        rowCount={rowCount}
        rowHeight={adjustedItemHeight + gap}
        width={width}
        itemData={gridData}
        overscanRowCount={overscan}
        overscanColumnCount={overscan}
        useIsScrolling={useIsScrolling}
        onScroll={handleScroll}
        style={{
          // Custom scrollbar styling
          scrollbarWidth: 'thin',
          scrollbarColor: '#CBD5E0 #F7FAFC',
        }}
      >
        {GridCell}
      </Grid>
      
      {/* Loading indicator at bottom */}
      {(hasNextPage || isLoadingMore) && (
        <div className="mt-6 text-center">
          {isLoadingMore ? (
            <div className="flex items-center justify-center space-x-2">
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
              <span className="text-gray-600">Loading more products...</span>
            </div>
          ) : (
            <p className="text-gray-500 text-sm">Scroll down to load more products</p>
          )}
        </div>
      )}
    </div>
  );
};

/**
 * Hook for responsive grid dimensions
 */
export const useResponsiveGrid = () => {
  const [dimensions, setDimensions] = React.useState({
    width: 1200,
    height: 600,
    itemWidth: 280,
  });
  
  React.useEffect(() => {
    const updateDimensions = () => {
      const width = window.innerWidth - 64; // Account for padding
      const height = window.innerHeight - 200; // Account for header/footer
      
      // Responsive item width
      let itemWidth = 280;
      if (width < 640) itemWidth = 240; // Mobile
      else if (width < 1024) itemWidth = 260; // Tablet
      
      setDimensions({ width, height, itemWidth });
    };
    
    updateDimensions();
    window.addEventListener('resize', updateDimensions);
    
    return () => window.removeEventListener('resize', updateDimensions);
  }, []);
  
  return dimensions;
};

export default VirtualizedProductGrid;
