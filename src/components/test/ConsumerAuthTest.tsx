/**
 * Consumer Authentication Test Component
 * Tests the fixed consumer authentication system
 */

import React, { useState } from 'react';
import { authenticateConsumerSimplified, getCurrentConsumerSession, logoutConsumer } from '@/services/simplifiedConsumerAuth';

const ConsumerAuthTest: React.FC = () => {
  const [testResults, setTestResults] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [testPhone, setTestPhone] = useState('0555123456');
  const [testName, setTestName] = useState('Test User');
  const [testPasscode, setTestPasscode] = useState('1234');

  const addResult = (message: string) => {
    setTestResults(prev => [...prev, `${new Date().toLocaleTimeString()}: ${message}`]);
  };

  const clearResults = () => {
    setTestResults([]);
  };

  const testConsumerAuth = async () => {
    setIsLoading(true);
    addResult('🧪 Starting Consumer Authentication Test...');

    try {
      // Test 1: Consumer Authentication without Passcode
      addResult('📱 Test 1: Consumer Authentication (No Passcode)');
      const authResult1 = await authenticateConsumerSimplified({
        phone: testPhone,
        fullName: testName
      });

      if (authResult1.success) {
        addResult('✅ Test 1 PASSED: Consumer authentication successful');
        addResult(`   User ID: ${authResult1.user?.id}`);
        addResult(`   Action: ${authResult1.action}`);
      } else {
        addResult(`❌ Test 1 FAILED: ${authResult1.error}`);
      }

      // Test 2: Check Session
      addResult('🔍 Test 2: Session Check');
      const session = getCurrentConsumerSession();
      if (session) {
        addResult('✅ Test 2 PASSED: Session found');
        addResult(`   Phone: ${session.phone}`);
        addResult(`   User ID: ${session.userId}`);
      } else {
        addResult('❌ Test 2 FAILED: No session found');
      }

      // Test 3: Consumer Authentication with Passcode
      addResult('🔐 Test 3: Consumer Authentication (With Passcode)');
      const authResult2 = await authenticateConsumerSimplified({
        phone: testPhone + '1', // Different phone
        fullName: testName + ' 2',
        passcode: testPasscode
      });

      if (authResult2.success) {
        addResult('✅ Test 3 PASSED: Consumer authentication with passcode successful');
        addResult(`   User ID: ${authResult2.user?.id}`);
        addResult(`   Action: ${authResult2.action}`);
      } else {
        addResult(`❌ Test 3 FAILED: ${authResult2.error}`);
      }

      // Test 4: Logout
      addResult('🚪 Test 4: Consumer Logout');
      try {
        logoutConsumer();
        const sessionAfterLogout = getCurrentConsumerSession();
        if (!sessionAfterLogout) {
          addResult('✅ Test 4 PASSED: Logout successful');
        } else {
          addResult('❌ Test 4 FAILED: Session still exists after logout');
        }
      } catch (error) {
        addResult(`❌ Test 4 FAILED: Logout error - ${error}`);
      }

      addResult('🏁 Consumer Authentication Test Complete');

    } catch (error) {
      addResult(`💥 Test Suite Error: ${error}`);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="max-w-4xl mx-auto p-6 bg-white rounded-lg shadow-lg">
      <h2 className="text-2xl font-bold text-gray-800 mb-6">
        🧪 Consumer Authentication Test Suite
      </h2>
      
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Test Phone Number
          </label>
          <input
            type="text"
            value={testPhone}
            onChange={(e) => setTestPhone(e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            placeholder="0555123456"
          />
        </div>
        
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Test Name
          </label>
          <input
            type="text"
            value={testName}
            onChange={(e) => setTestName(e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            placeholder="Test User"
          />
        </div>
        
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Test Passcode (4-6 digits)
          </label>
          <input
            type="text"
            value={testPasscode}
            onChange={(e) => setTestPasscode(e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            placeholder="1234"
          />
        </div>
      </div>

      <div className="flex gap-4 mb-6">
        <button
          onClick={testConsumerAuth}
          disabled={isLoading}
          className="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {isLoading ? '🔄 Running Tests...' : '🧪 Run Consumer Auth Tests'}
        </button>
        
        <button
          onClick={clearResults}
          className="px-6 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700"
        >
          🗑️ Clear Results
        </button>
      </div>

      <div className="bg-gray-50 rounded-lg p-4">
        <h3 className="text-lg font-semibold text-gray-800 mb-3">Test Results:</h3>
        <div className="space-y-1 max-h-96 overflow-y-auto">
          {testResults.length === 0 ? (
            <p className="text-gray-500 italic">No test results yet. Click "Run Consumer Auth Tests" to start.</p>
          ) : (
            testResults.map((result, index) => (
              <div
                key={index}
                className={`text-sm font-mono p-2 rounded ${
                  result.includes('✅') ? 'bg-green-100 text-green-800' :
                  result.includes('❌') ? 'bg-red-100 text-red-800' :
                  result.includes('🧪') || result.includes('🏁') ? 'bg-blue-100 text-blue-800' :
                  'bg-gray-100 text-gray-700'
                }`}
              >
                {result}
              </div>
            ))
          )}
        </div>
      </div>
    </div>
  );
};

export default ConsumerAuthTest;
