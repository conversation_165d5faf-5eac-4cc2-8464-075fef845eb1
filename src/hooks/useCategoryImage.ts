import { useState, useEffect } from 'react';
import { getBestCategoryImageUrl, getFallbackImageUrl, getFastCategoryImageUrl } from '@/services/categoryImageService';

interface UseCategoryImageResult {
  imageUrl: string;
  isLoading: boolean;
  hasError: boolean;
}

/**
 * React hook for loading category/subcategory images from the new folder structure
 * Handles async loading, fallbacks, and error states
 */
export const useCategoryImage = (
  categoryId: string,
  type: 'category' | 'subcategory'
): UseCategoryImageResult => {
  const [imageUrl, setImageUrl] = useState<string>(getFallbackImageUrl(type));
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [hasError, setHasError] = useState<boolean>(false);

  useEffect(() => {
    let isMounted = true;

    const loadImage = async () => {
      if (!categoryId) {
        setImageUrl(getFallbackImageUrl(type));
        setIsLoading(false);
        setHasError(false);
        return;
      }

      try {
        setIsLoading(true);
        setHasError(false);

        // Get the best available image URL from the new folder structure
        const url = await getBestCategoryImageUrl(categoryId, type);
        
        if (isMounted) {
          setImageUrl(url);
          setIsLoading(false);
          
          // Check if it's a fallback (data: URL means SVG fallback)
          setHasError(url.startsWith('data:'));
        }
      } catch (error) {
        console.error(`Error loading ${type} image for ${categoryId}:`, error);
        
        if (isMounted) {
          setImageUrl(getFallbackImageUrl(type));
          setIsLoading(false);
          setHasError(true);
        }
      }
    };

    loadImage();

    // Cleanup function to prevent state updates on unmounted component
    return () => {
      isMounted = false;
    };
  }, [categoryId, type]);

  return {
    imageUrl,
    isLoading,
    hasError
  };
};

/**
 * Hook specifically for category images
 */
export const useCategoryImageUrl = (categoryId: string): UseCategoryImageResult => {
  return useCategoryImage(categoryId, 'category');
};

/**
 * Hook specifically for subcategory images
 */
export const useSubcategoryImageUrl = (subcategoryId: string): UseCategoryImageResult => {
  return useCategoryImage(subcategoryId, 'subcategory');
};

/**
 * FAST hooks for admin dropdowns (NO database calls)
 * Uses simple URL structure for performance
 */
export const useFastCategoryImage = (
  categoryId: string,
  type: 'category' | 'subcategory'
): UseCategoryImageResult => {
  const [imageUrl, setImageUrl] = useState<string>(getFallbackImageUrl(type));
  const [isLoading, setIsLoading] = useState<boolean>(false); // No loading for fast mode
  const [hasError, setHasError] = useState<boolean>(false);

  useEffect(() => {
    if (!categoryId) {
      setImageUrl(getFallbackImageUrl(type));
      setHasError(false);
      return;
    }

    // Get fast URL (no async operations)
    const url = getFastCategoryImageUrl(categoryId, type);
    setImageUrl(url);
    setHasError(false);
  }, [categoryId, type]);

  return {
    imageUrl,
    isLoading,
    hasError
  };
};

/**
 * Fast hook specifically for category images (for admin dropdowns)
 */
export const useFastCategoryImageUrl = (categoryId: string): UseCategoryImageResult => {
  return useFastCategoryImage(categoryId, 'category');
};

/**
 * Fast hook specifically for subcategory images (for admin dropdowns)
 */
export const useFastSubcategoryImageUrl = (subcategoryId: string): UseCategoryImageResult => {
  return useFastCategoryImage(subcategoryId, 'subcategory');
};
