/**
 * OPTIMIZED MY VEHICLE PARTS PAGE - PHASE 2
 * 
 * Enhanced marketplace with infinite scroll and advanced optimizations:
 * - Infinite scroll with virtual scrolling
 * - Professional loading states
 * - Optimized performance
 * - Safe fallback mechanisms
 */

import React from 'react';
import { useTranslation } from 'react-i18next';
import { FilterProvider } from '@/contexts/FilterContext';
import { CategoryNavigation } from '@/components/marketplace/CategoryNavigation';
import { SubcategoryNavigation } from '@/components/marketplace/SubcategoryNavigation';
import { SafeOptimizedMarketplace } from '@/components/marketplace/SafeOptimizedMarketplace';
import { MarketplaceHeader } from '@/components/marketplace/MarketplaceHeader';
import { MarketplaceFilters } from '@/components/marketplace/MarketplaceFilters';
import { useFilter } from '@/contexts/FilterContext';

// Internal component that uses the product filter context
function OptimizedMyVehiclePartsContent() {
  const { t } = useTranslation();
  const {
    selectedCategory,
    selectedSubcategory,
    setSelectedSubcategory,
    showTyresModal,
    setShowTyresModal,
    showPartsModal,
    setShowPartsModal,
  } = useFilter();

  const handleSubcategorySelect = (subcategory: string) => {
    setSelectedSubcategory(subcategory);
  };

  const handleRequestQuote = () => {
    // Handle quote request logic
    console.log('Quote requested for:', selectedCategory, selectedSubcategory);
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <MarketplaceHeader 
        title={t('marketplace.partsForMyVehicle')}
        subtitle={t('marketplace.findPartsFromMerchants')}
      />

      {/* Category Navigation */}
      <div className="bg-white border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <CategoryNavigation />
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="flex gap-8">
          {/* Sidebar Filters */}
          <div className="w-80 flex-shrink-0">
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 sticky top-8">
              <h3 className="text-lg font-semibold text-gray-900 mb-6">
                {t('marketplace.filters')}
              </h3>
              
              <MarketplaceFilters
                selectedCategory={selectedCategory}
                onOpenTyresModal={() => setShowTyresModal(true)}
                onOpenPartsModal={() => setShowPartsModal(true)}
              />
            </div>
          </div>

          {/* Main Content Area */}
          <div className="flex-1 min-w-0">
            <div className="space-y-6">
              {/* Subcategory Navigation */}
              {selectedCategory && selectedCategory !== 'all' && (
                <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                  <SubcategoryNavigation
                    selectedCategory={selectedCategory}
                    onSubcategorySelect={handleSubcategorySelect}
                    selectedSubcategory={selectedSubcategory}
                    onRequestQuote={handleRequestQuote}
                  />
                </div>
              )}

              {/* Optimized Marketplace */}
              <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                <SafeOptimizedMarketplace section="retail" />
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Filter Modals */}
      {showTyresModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-xl p-6 max-w-md w-full mx-4">
            <h3 className="text-lg font-semibold mb-4">Tyre Filters</h3>
            <p className="text-gray-600 mb-4">Advanced tyre filtering options will be available here.</p>
            <button
              onClick={() => setShowTyresModal(false)}
              className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700"
            >
              Close
            </button>
          </div>
        </div>
      )}

      {showPartsModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-xl p-6 max-w-md w-full mx-4">
            <h3 className="text-lg font-semibold mb-4">Parts Filters</h3>
            <p className="text-gray-600 mb-4">Advanced parts filtering options will be available here.</p>
            <button
              onClick={() => setShowPartsModal(false)}
              className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700"
            >
              Close
            </button>
          </div>
        </div>
      )}
    </div>
  );
}

// Main component with FilterProvider
export default function OptimizedMyVehicleParts() {
  return (
    <FilterProvider>
      <OptimizedMyVehiclePartsContent />
    </FilterProvider>
  );
}
