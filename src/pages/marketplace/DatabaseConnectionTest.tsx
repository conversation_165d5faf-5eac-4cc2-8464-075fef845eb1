/**
 * DATABASE CONNECTION TEST
 * 
 * Simple test page to verify database connectivity and query functionality
 */

import React, { useState, useEffect } from 'react';
import { createClient } from '@supabase/supabase-js';

const supabaseUrl = 'https://irkwpzcskeqtasutqnxp.supabase.co';
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imlya3dwemNza2VxdGFzdXRxbnhwIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDc0OTg2ODMsImV4cCI6MjA2MzA3NDY4M30.bUv9zxx9q_sVUwv_-G6WbHsA53-SU5tCCWbOS_5Xp94';

const supabase = createClient(supabaseUrl, supabaseAnonKey);

export default function DatabaseConnectionTest() {
  const [testResults, setTestResults] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  const runTests = async () => {
    setIsLoading(true);
    const results: any[] = [];

    try {
      // Test 1: Basic connection
      results.push({
        test: 'Basic Connection',
        status: 'TESTING',
        details: 'Testing Supabase connection...'
      });

      // Test 2: Simple products query
      console.log('[DB_TEST] Starting simple products query...');
      const { data: products, error: productsError, count } = await supabase
        .from('products')
        .select('*', { count: 'exact' })
        .limit(5);

      if (productsError) {
        results.push({
          test: 'Simple Products Query',
          status: 'FAILED',
          error: productsError.message,
          details: productsError
        });
      } else {
        results.push({
          test: 'Simple Products Query',
          status: 'SUCCESS',
          details: `Found ${count} total products, fetched ${products?.length || 0} products`,
          data: products
        });
      }

      // Test 3: Products with status filter
      console.log('[DB_TEST] Testing products with status filter...');
      const { data: activeProducts, error: activeError } = await supabase
        .from('products')
        .select('*')
        .in('status', ['active', 'out_of_stock'])
        .limit(5);

      if (activeError) {
        results.push({
          test: 'Active Products Query',
          status: 'FAILED',
          error: activeError.message,
          details: activeError
        });
      } else {
        results.push({
          test: 'Active Products Query',
          status: 'SUCCESS',
          details: `Found ${activeProducts?.length || 0} active products`,
          data: activeProducts
        });
      }

      // Test 4: Products by category
      console.log('[DB_TEST] Testing products by category...');
      const { data: tyreProducts, error: tyreError } = await supabase
        .from('products')
        .select('*')
        .eq('category', 'tyres')
        .in('status', ['active', 'out_of_stock'])
        .limit(5);

      if (tyreError) {
        results.push({
          test: 'Tyre Products Query',
          status: 'FAILED',
          error: tyreError.message,
          details: tyreError
        });
      } else {
        results.push({
          test: 'Tyre Products Query',
          status: 'SUCCESS',
          details: `Found ${tyreProducts?.length || 0} tyre products`,
          data: tyreProducts
        });
      }

      // Test 5: Products with joins
      console.log('[DB_TEST] Testing products with joins...');
      const { data: joinedProducts, error: joinError } = await supabase
        .from('products')
        .select(`
          *,
          tyre_specifications(*),
          vehicle_compatibility(*),
          wholesale_pricing_tiers(*)
        `)
        .in('status', ['active', 'out_of_stock'])
        .limit(3);

      if (joinError) {
        results.push({
          test: 'Products with Joins',
          status: 'FAILED',
          error: joinError.message,
          details: joinError
        });
      } else {
        results.push({
          test: 'Products with Joins',
          status: 'SUCCESS',
          details: `Found ${joinedProducts?.length || 0} products with related data`,
          data: joinedProducts
        });
      }

      // Test 6: Check available categories
      console.log('[DB_TEST] Testing available categories...');
      const { data: categories, error: catError } = await supabase
        .from('products')
        .select('category')
        .in('status', ['active', 'out_of_stock']);

      if (catError) {
        results.push({
          test: 'Available Categories',
          status: 'FAILED',
          error: catError.message,
          details: catError
        });
      } else {
        const uniqueCategories = [...new Set(categories?.map(p => p.category) || [])];
        results.push({
          test: 'Available Categories',
          status: 'SUCCESS',
          details: `Found categories: ${uniqueCategories.join(', ')}`,
          data: uniqueCategories
        });
      }

    } catch (error: any) {
      results.push({
        test: 'General Error',
        status: 'FAILED',
        error: error.message,
        details: error
      });
    }

    setTestResults(results);
    setIsLoading(false);
  };

  useEffect(() => {
    runTests();
  }, []);

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4">
        <div className="bg-white rounded-lg shadow-lg p-6">
          <h1 className="text-2xl font-bold text-gray-900 mb-6">
            Database Connection Test
          </h1>
          
          <div className="mb-6">
            <button
              onClick={runTests}
              disabled={isLoading}
              className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 disabled:opacity-50"
            >
              {isLoading ? 'Running Tests...' : 'Run Tests Again'}
            </button>
          </div>

          <div className="space-y-4">
            {testResults.map((result, index) => (
              <div
                key={index}
                className={`border rounded-lg p-4 ${
                  result.status === 'SUCCESS' 
                    ? 'border-green-200 bg-green-50' 
                    : result.status === 'FAILED'
                    ? 'border-red-200 bg-red-50'
                    : 'border-yellow-200 bg-yellow-50'
                }`}
              >
                <div className="flex items-center justify-between mb-2">
                  <h3 className="font-medium text-gray-900">{result.test}</h3>
                  <span
                    className={`px-2 py-1 rounded text-xs font-medium ${
                      result.status === 'SUCCESS'
                        ? 'bg-green-100 text-green-800'
                        : result.status === 'FAILED'
                        ? 'bg-red-100 text-red-800'
                        : 'bg-yellow-100 text-yellow-800'
                    }`}
                  >
                    {result.status}
                  </span>
                </div>
                
                <p className="text-gray-600 text-sm mb-2">{result.details}</p>
                
                {result.error && (
                  <div className="bg-red-100 border border-red-200 rounded p-2 mb-2">
                    <p className="text-red-800 text-xs font-mono">{result.error}</p>
                  </div>
                )}
                
                {result.data && (
                  <details className="mt-2">
                    <summary className="cursor-pointer text-blue-600 text-sm">
                      View Data ({Array.isArray(result.data) ? result.data.length : 1} items)
                    </summary>
                    <pre className="mt-2 bg-gray-100 p-2 rounded text-xs overflow-auto max-h-40">
                      {JSON.stringify(result.data, null, 2)}
                    </pre>
                  </details>
                )}
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}
