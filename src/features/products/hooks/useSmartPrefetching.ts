/**
 * SMART PREFETCHING SYSTEM
 * 
 * This hook implements intelligent prefetching strategies for marketplace performance:
 * - Prefetches popular categories based on user behavior
 * - Preloads next page data for infinite scroll
 * - Implements background loading for instant category switching
 * - Uses idle time for prefetching to avoid blocking user interactions
 */

import { useEffect, useRef, useCallback } from 'react';
import { useQueryClient } from '@tanstack/react-query';
import { fetchMarketplaceProductsPaginated } from '@/services/productService';

// Popular categories based on typical automotive marketplace usage
const POPULAR_CATEGORIES = [
  'tyres',
  'brake-parts', 
  'engine-parts',
  'filters',
  'oils-fluids',
  'all-other-categories'
];

// Prefetching configuration
const PREFETCH_CONFIG = {
  // How many categories to prefetch in background
  maxPrefetchCategories: 3,
  
  // Delay before starting prefetch (ms)
  prefetchDelay: 2000,
  
  // How many pages ahead to prefetch for infinite scroll
  prefetchPagesAhead: 1,
  
  // Batch size for prefetched data
  prefetchBatchSize: 12,
  
  // Use idle time for prefetching
  useIdleTime: true,
};

interface SmartPrefetchingOptions {
  currentCategory?: string | null;
  enabled?: boolean;
  priorityCategories?: string[];
}

export const useSmartPrefetching = ({
  currentCategory,
  enabled = true,
  priorityCategories = POPULAR_CATEGORIES
}: SmartPrefetchingOptions = {}) => {
  const queryClient = useQueryClient();
  const prefetchTimeoutRef = useRef<NodeJS.Timeout>();
  const idleCallbackRef = useRef<number>();

  // Prefetch a specific category
  const prefetchCategory = useCallback(async (categoryId: string) => {
    try {
      console.log(`[SMART_PREFETCH] Prefetching category: ${categoryId}`);
      
      await queryClient.prefetchInfiniteQuery({
        queryKey: ['marketplace-products-infinite', categoryId, PREFETCH_CONFIG.prefetchBatchSize],
        queryFn: ({ pageParam = 1 }) => 
          fetchMarketplaceProductsPaginated(categoryId, pageParam, PREFETCH_CONFIG.prefetchBatchSize),
        getNextPageParam: (lastPage) => lastPage.hasMore ? lastPage.nextPage : undefined,
        staleTime: 10 * 60 * 1000, // 10 minutes
        cacheTime: 30 * 60 * 1000, // 30 minutes
      });
      
      console.log(`[SMART_PREFETCH] Successfully prefetched: ${categoryId}`);
    } catch (error) {
      console.warn(`[SMART_PREFETCH] Failed to prefetch ${categoryId}:`, error);
    }
  }, [queryClient]);

  // Prefetch next page for infinite scroll
  const prefetchNextPage = useCallback(async (categoryId: string, currentPage: number) => {
    try {
      const nextPage = currentPage + 1;
      console.log(`[SMART_PREFETCH] Prefetching page ${nextPage} for category: ${categoryId}`);
      
      await queryClient.prefetchQuery({
        queryKey: ['marketplace-products-optimized', categoryId, nextPage, PREFETCH_CONFIG.prefetchBatchSize],
        queryFn: () => fetchMarketplaceProductsPaginated(categoryId, nextPage, PREFETCH_CONFIG.prefetchBatchSize),
        staleTime: 10 * 60 * 1000,
        cacheTime: 30 * 60 * 1000,
      });
      
      console.log(`[SMART_PREFETCH] Successfully prefetched page ${nextPage}`);
    } catch (error) {
      console.warn(`[SMART_PREFETCH] Failed to prefetch page ${nextPage}:`, error);
    }
  }, [queryClient]);

  // Get categories to prefetch (excluding current)
  const getCategoriesToPrefetch = useCallback(() => {
    return priorityCategories
      .filter(cat => cat !== currentCategory)
      .slice(0, PREFETCH_CONFIG.maxPrefetchCategories);
  }, [currentCategory, priorityCategories]);

  // Prefetch popular categories in background
  const prefetchPopularCategories = useCallback(() => {
    const categoriesToPrefetch = getCategoriesToPrefetch();
    
    if (categoriesToPrefetch.length === 0) {
      return;
    }

    const prefetchWithDelay = (categories: string[], index = 0) => {
      if (index >= categories.length) return;
      
      const category = categories[index];
      
      if (PREFETCH_CONFIG.useIdleTime && 'requestIdleCallback' in window) {
        // Use idle time for prefetching
        idleCallbackRef.current = requestIdleCallback(() => {
          prefetchCategory(category).then(() => {
            // Prefetch next category after a delay
            setTimeout(() => prefetchWithDelay(categories, index + 1), 1000);
          });
        }, { timeout: 5000 });
      } else {
        // Fallback to setTimeout
        setTimeout(() => {
          prefetchCategory(category).then(() => {
            prefetchWithDelay(categories, index + 1);
          });
        }, 1000 * (index + 1));
      }
    };

    prefetchWithDelay(categoriesToPrefetch);
  }, [getCategoriesToPrefetch, prefetchCategory]);

  // Start prefetching after delay
  useEffect(() => {
    if (!enabled) return;

    // Clear existing timeout
    if (prefetchTimeoutRef.current) {
      clearTimeout(prefetchTimeoutRef.current);
    }

    // Start prefetching after delay
    prefetchTimeoutRef.current = setTimeout(() => {
      prefetchPopularCategories();
    }, PREFETCH_CONFIG.prefetchDelay);

    return () => {
      if (prefetchTimeoutRef.current) {
        clearTimeout(prefetchTimeoutRef.current);
      }
      if (idleCallbackRef.current) {
        cancelIdleCallback(idleCallbackRef.current);
      }
    };
  }, [enabled, prefetchPopularCategories]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (prefetchTimeoutRef.current) {
        clearTimeout(prefetchTimeoutRef.current);
      }
      if (idleCallbackRef.current) {
        cancelIdleCallback(idleCallbackRef.current);
      }
    };
  }, []);

  return {
    prefetchCategory,
    prefetchNextPage,
    getCategoriesToPrefetch,
    isEnabled: enabled,
  };
};

/**
 * Hook for prefetching next page in infinite scroll
 */
export const useInfiniteScrollPrefetch = (
  categoryId: string | null,
  currentPage: number,
  hasNextPage: boolean,
  enabled: boolean = true
) => {
  const { prefetchNextPage } = useSmartPrefetching({ enabled: false });

  useEffect(() => {
    if (!enabled || !categoryId || !hasNextPage) return;

    // Prefetch next page when user is close to the end
    const timeoutId = setTimeout(() => {
      prefetchNextPage(categoryId, currentPage);
    }, 1000);

    return () => clearTimeout(timeoutId);
  }, [categoryId, currentPage, hasNextPage, enabled, prefetchNextPage]);
};

/**
 * Performance metrics for prefetching
 */
export const usePrefetchMetrics = () => {
  const queryClient = useQueryClient();

  const getMetrics = useCallback(() => {
    const cache = queryClient.getQueryCache();
    const queries = cache.getAll();
    
    const prefetchedQueries = queries.filter(query => 
      query.queryKey[0] === 'marketplace-products-infinite' ||
      query.queryKey[0] === 'marketplace-products-optimized'
    );

    const cachedCategories = new Set(
      prefetchedQueries.map(query => query.queryKey[1])
    ).size;

    return {
      totalCachedQueries: prefetchedQueries.length,
      cachedCategories,
      cacheSize: queries.length,
      prefetchHitRate: prefetchedQueries.length / Math.max(queries.length, 1),
    };
  }, [queryClient]);

  return { getMetrics };
};
