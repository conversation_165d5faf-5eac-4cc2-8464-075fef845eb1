/**
 * Generate Complete Image Upload Guide
 * Creates a comprehensive list of all categories and subcategories with exact upload instructions
 */

import { CATEGORIES } from '@/data/categoryData';

interface ImageUploadItem {
  type: 'category' | 'subcategory';
  id: string;
  name: string;
  displayName: string;
  categoryName?: string;
  filePath: string;
  fileName: string;
  uploadUrl: string;
}

/**
 * Generate complete list of all image upload requirements
 */
export const generateImageUploadGuide = (): ImageUploadItem[] => {
  const items: ImageUploadItem[] = [];

  // Add categories
  CATEGORIES.filter(cat => cat.id !== 'all').forEach(category => {
    items.push({
      type: 'category',
      id: category.id,
      name: category.name,
      displayName: category.displayName,
      filePath: `category/${category.id}.png`,
      fileName: `${category.id}.png`,
      uploadUrl: `https://supabase.com/dashboard/project/irkwpzcskeqtasutqnxp/storage/buckets/category-images`
    });
  });

  // Add subcategories
  CATEGORIES.forEach(category => {
    category.subcategories.forEach(subcategory => {
      items.push({
        type: 'subcategory',
        id: subcategory.id,
        name: subcategory.name,
        displayName: subcategory.displayName,
        categoryName: category.displayName,
        filePath: `subcategory/${subcategory.id}.png`,
        fileName: `${subcategory.id}.png`,
        uploadUrl: `https://supabase.com/dashboard/project/irkwpzcskeqtasutqnxp/storage/buckets/category-images`
      });
    });
  });

  return items;
};

/**
 * Print complete upload guide to console
 */
export const printImageUploadGuide = (): void => {
  const items = generateImageUploadGuide();
  const categories = items.filter(item => item.type === 'category');
  const subcategories = items.filter(item => item.type === 'subcategory');

  console.log('=== 📸 COMPLETE CATEGORY IMAGE UPLOAD GUIDE ===\n');
  
  console.log(`📊 SUMMARY:`);
  console.log(`   Total Categories: ${categories.length}`);
  console.log(`   Total Subcategories: ${subcategories.length}`);
  console.log(`   Total Images Needed: ${items.length}\n`);

  console.log('🎯 QUICK SETUP INSTRUCTIONS:');
  console.log('1. Go to: https://supabase.com/dashboard/project/irkwpzcskeqtasutqnxp/storage/buckets/category-images');
  console.log('2. Create "category" and "subcategory" folders if they don\'t exist');
  console.log('3. Upload images directly to these folders');
  console.log('4. Name files exactly as shown in "File Name" below');
  console.log('5. Images appear instantly in the app!\n');

  console.log('📁 CATEGORIES:\n');
  categories.forEach((item, index) => {
    console.log(`${index + 1}. ${item.displayName}`);
    console.log(`   ID: ${item.id}`);
    console.log(`   File Path: ${item.filePath}`);
    console.log(`   File Name: ${item.fileName}`);
    console.log(`   Upload URL: ${item.uploadUrl}\n`);
  });

  console.log('📂 SUBCATEGORIES:\n');
  subcategories.forEach((item, index) => {
    console.log(`${index + 1}. ${item.displayName}`);
    console.log(`   ID: ${item.id}`);
    console.log(`   Category: ${item.categoryName}`);
    console.log(`   File Path: ${item.filePath}`);
    console.log(`   File Name: ${item.fileName}`);
    console.log(`   Upload URL: ${item.uploadUrl}\n`);
  });

  console.log('💡 TIPS:');
  console.log('• File names MUST match the ID exactly');
  console.log('• Supported formats: .png, .jpg, .jpeg, .webp, .svg');
  console.log('• Recommended: Use .png for best quality');
  console.log('• Max file size: 10MB');
  console.log('• Images appear instantly after upload');
};

/**
 * Generate CSV export for external tools
 */
export const generateImageUploadCSV = (): string => {
  const items = generateImageUploadGuide();
  
  const headers = ['Type', 'ID', 'Display Name', 'Category', 'Folder Path', 'File Name', 'Upload URL'];
  const rows = items.map(item => [
    item.type,
    item.id,
    item.displayName,
    item.categoryName || '',
    item.folderPath,
    item.fileName,
    item.uploadUrl
  ]);

  const csvContent = [
    headers.join(','),
    ...rows.map(row => row.map(cell => `"${cell}"`).join(','))
  ].join('\n');

  return csvContent;
};

/**
 * Generate Markdown documentation
 */
export const generateImageUploadMarkdown = (): string => {
  const items = generateImageUploadGuide();
  const categories = items.filter(item => item.type === 'category');
  const subcategories = items.filter(item => item.type === 'subcategory');

  return `# Category Image Upload Guide

## Summary
- **Total Categories:** ${categories.length}
- **Total Subcategories:** ${subcategories.length}
- **Total Images Needed:** ${items.length}

## Quick Setup
1. Go to [Supabase Storage](https://supabase.com/dashboard/project/irkwpzcskeqtasutqnxp/storage/buckets/category-images)
2. Find the folder with the exact ID name
3. Drag and drop your image
4. Name it exactly as shown in "File Name"
5. Image appears instantly in the app!

## Categories

${categories.map((item, index) => `### ${index + 1}. ${item.displayName}
- **ID:** \`${item.id}\`
- **Folder:** \`${item.folderPath}\`
- **File Name:** \`${item.fileName}\`
- **[Upload Here](${item.uploadUrl})**
`).join('\n')}

## Subcategories

${subcategories.map((item, index) => `### ${index + 1}. ${item.displayName}
- **ID:** \`${item.id}\`
- **Category:** ${item.categoryName}
- **Folder:** \`${item.folderPath}\`
- **File Name:** \`${item.fileName}\`
- **[Upload Here](${item.uploadUrl})**
`).join('\n')}

## Tips
- File names MUST match the ID exactly
- Supported formats: .png, .jpg, .jpeg, .webp, .svg
- Recommended: Use .png for best quality
- Max file size: 10MB
- Images appear instantly after upload
`;
};

/**
 * Export all functions for easy access
 */
export const ImageUploadGuide = {
  generate: generateImageUploadGuide,
  print: printImageUploadGuide,
  csv: generateImageUploadCSV,
  markdown: generateImageUploadMarkdown
};

// Make it available globally for console access
if (typeof window !== 'undefined') {
  (window as any).ImageUploadGuide = ImageUploadGuide;
}

// Export for Node.js usage
export default ImageUploadGuide;
