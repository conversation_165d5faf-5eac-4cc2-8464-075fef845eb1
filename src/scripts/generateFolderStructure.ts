/**
 * Generate Folder Structure for Easy Image Upload
 * Creates a complete guide for folder-based image uploads using display names
 */

import { CATEGORIES } from '@/data/categoryData';

interface FolderStructureItem {
  type: 'category' | 'subcategory';
  id: string;
  displayName: string;
  folderName: string;
  folderPath: string;
  categoryName?: string;
  instructions: string;
}

/**
 * Generate complete folder structure for easy drag-and-drop uploads
 */
export const generateFolderStructure = (): FolderStructureItem[] => {
  const items: FolderStructureItem[] = [];

  // Add categories
  CATEGORIES.filter(cat => cat.id !== 'all').forEach(category => {
    items.push({
      type: 'category',
      id: category.id,
      displayName: category.displayName,
      folderName: category.displayName,
      folderPath: `category/${category.displayName}/`,
      instructions: `Create folder "${category.displayName}" in category/ and drag any image into it`
    });
  });

  // Add subcategories
  CATEGORIES.forEach(category => {
    category.subcategories.forEach(subcategory => {
      items.push({
        type: 'subcategory',
        id: subcategory.id,
        displayName: subcategory.displayName,
        folderName: subcategory.displayName,
        folderPath: `subcategory/${subcategory.displayName}/`,
        categoryName: category.displayName,
        instructions: `Create folder "${subcategory.displayName}" in subcategory/ and drag any image into it`
      });
    });
  });

  return items;
};

/**
 * Print complete folder structure guide
 */
export const printFolderStructureGuide = (): void => {
  const items = generateFolderStructure();
  const categories = items.filter(item => item.type === 'category');
  const subcategories = items.filter(item => item.type === 'subcategory');

  console.log('=== 📁 EASY DRAG-AND-DROP FOLDER STRUCTURE GUIDE ===\n');
  
  console.log(`📊 SUMMARY:`);
  console.log(`   Total Categories: ${categories.length}`);
  console.log(`   Total Subcategories: ${subcategories.length}`);
  console.log(`   Total Folders to Create: ${items.length}\n`);

  console.log('🎯 SUPER EASY SETUP INSTRUCTIONS:');
  console.log('1. Go to: https://supabase.com/dashboard/project/irkwpzcskeqtasutqnxp/storage/buckets/category-images');
  console.log('2. Create main folders: "category" and "subcategory"');
  console.log('3. Inside each main folder, create subfolders with the EXACT DISPLAY NAMES below');
  console.log('4. Drag and drop ANY image file into the correct folder');
  console.log('5. Images appear instantly in the app - NO RENAMING REQUIRED!\n');

  console.log('💡 SUPPORTED IMAGE FORMATS:');
  console.log('   ✅ PNG, JPG, JPEG, WebP, SVG');
  console.log('   ✅ Any file name (image.png, photo.jpg, etc.)');
  console.log('   ✅ Up to 10MB file size\n');

  console.log('📁 CATEGORY FOLDERS TO CREATE:\n');
  categories.forEach((item, index) => {
    console.log(`${index + 1}. ${item.displayName}`);
    console.log(`   📂 Create folder: category/${item.displayName}/`);
    console.log(`   🎯 Then drag any image into this folder\n`);
  });

  console.log('📂 SUBCATEGORY FOLDERS TO CREATE:\n');
  subcategories.forEach((item, index) => {
    console.log(`${index + 1}. ${item.displayName}`);
    console.log(`   📂 Create folder: subcategory/${item.displayName}/`);
    console.log(`   🏷️  Category: ${item.categoryName}`);
    console.log(`   🎯 Then drag any image into this folder\n`);
  });

  console.log('🚀 EXAMPLE WORKFLOW:');
  console.log('1. Create folder: category/Tyres/');
  console.log('2. Drag your tire image (any name like "tire-photo.jpg") into the folder');
  console.log('3. Image automatically appears in the Tyres category!');
  console.log('4. Repeat for all categories and subcategories\n');

  console.log('✅ NO FILE RENAMING NEEDED!');
  console.log('✅ NO EXACT ID MATCHING REQUIRED!');
  console.log('✅ JUST DRAG AND DROP INTO CORRECT FOLDERS!');
};

/**
 * Generate folder creation script for batch operations
 */
export const generateFolderCreationScript = (): string => {
  const items = generateFolderStructure();
  
  let script = `# Folder Creation Script for Category Images\n`;
  script += `# Copy these folder names exactly when creating folders in Supabase Storage\n\n`;
  
  script += `## Main Folders (create these first):\n`;
  script += `category/\n`;
  script += `subcategory/\n\n`;
  
  script += `## Category Subfolders (create inside category/):\n`;
  items.filter(item => item.type === 'category').forEach(item => {
    script += `category/${item.displayName}/\n`;
  });
  
  script += `\n## Subcategory Subfolders (create inside subcategory/):\n`;
  items.filter(item => item.type === 'subcategory').forEach(item => {
    script += `subcategory/${item.displayName}/\n`;
  });
  
  return script;
};

/**
 * Generate CSV for external tools
 */
export const generateFolderStructureCSV = (): string => {
  const items = generateFolderStructure();
  
  const headers = ['Type', 'Display Name', 'Folder Path', 'Category', 'Instructions'];
  const rows = items.map(item => [
    item.type,
    item.displayName,
    item.folderPath,
    item.categoryName || '',
    item.instructions
  ]);

  const csvContent = [
    headers.join(','),
    ...rows.map(row => row.map(cell => `"${cell}"`).join(','))
  ].join('\n');

  return csvContent;
};

/**
 * Generate Markdown documentation
 */
export const generateFolderStructureMarkdown = (): string => {
  const items = generateFolderStructure();
  const categories = items.filter(item => item.type === 'category');
  const subcategories = items.filter(item => item.type === 'subcategory');

  return `# Easy Drag-and-Drop Image Upload Guide

## 🎯 Super Simple Process

1. **Go to**: [Supabase Storage](https://supabase.com/dashboard/project/irkwpzcskeqtasutqnxp/storage/buckets/category-images)
2. **Create main folders**: \`category\` and \`subcategory\`
3. **Create subfolders** with exact display names below
4. **Drag any image** into the correct folder
5. **Images appear instantly** - NO RENAMING REQUIRED!

## 📊 Summary
- **Categories**: ${categories.length} folders to create
- **Subcategories**: ${subcategories.length} folders to create
- **Supported formats**: PNG, JPG, JPEG, WebP, SVG
- **File naming**: Any name works!

## 📁 Category Folders

${categories.map((item, index) => `### ${index + 1}. ${item.displayName}
- **Folder**: \`category/${item.displayName}/\`
- **Action**: Drag any image into this folder
`).join('\n')}

## 📂 Subcategory Folders

${subcategories.map((item, index) => `### ${index + 1}. ${item.displayName}
- **Category**: ${item.categoryName}
- **Folder**: \`subcategory/${item.displayName}/\`
- **Action**: Drag any image into this folder
`).join('\n')}

## ✅ Benefits
- **No file renaming** required
- **No ID matching** needed
- **Any image format** supported
- **Instant appearance** in app
- **Bulk upload** friendly
`;
};

/**
 * Export all functions for easy access
 */
export const FolderStructureGuide = {
  generate: generateFolderStructure,
  print: printFolderStructureGuide,
  script: generateFolderCreationScript,
  csv: generateFolderStructureCSV,
  markdown: generateFolderStructureMarkdown
};

// Make it available globally for console access
if (typeof window !== 'undefined') {
  (window as any).FolderStructureGuide = FolderStructureGuide;
}

export default FolderStructureGuide;
