/**
 * Create Category Folders Script
 * Automatically creates individual folders for each category and subcategory
 * This solves the organization problem by giving each category its own folder
 */

import { createClient } from '@supabase/supabase-js';
import { CATEGORIES } from '@/data/categoryData';

// Initialize Supabase client
const supabaseUrl = import.meta.env.VITE_SUPABASE_URL || 'https://irkwpzcskeqtasutqnxp.supabase.co';
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imlya3dwemNza2VxdGFzdXRxbnhwIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDc0OTg2ODMsImV4cCI6MjA2MzA3NDY4M30.bUv9zxx9q_sVUwv_-G6WbHsA53-SU5tCCWbOS_5Xp94';

const supabase = createClient(supabaseUrl, supabaseAnonKey);

interface FolderCreationResult {
  success: boolean;
  foldersCreated: number;
  foldersSkipped: number;
  errors: string[];
  details: Array<{
    type: 'category' | 'subcategory';
    name: string;
    folderPath: string;
    status: 'created' | 'exists' | 'error';
    error?: string;
  }>;
}

/**
 * Create a placeholder file to establish folder structure
 */
const createPlaceholderFile = (): Uint8Array => {
  // Create a minimal 1x1 transparent PNG
  const pngData = [
    0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A, 0x00, 0x00, 0x00, 0x0D,
    0x49, 0x48, 0x44, 0x52, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x01,
    0x08, 0x06, 0x00, 0x00, 0x00, 0x1F, 0x15, 0xC4, 0x89, 0x00, 0x00, 0x00,
    0x0D, 0x49, 0x44, 0x41, 0x54, 0x78, 0xDA, 0x63, 0x64, 0x60, 0xF8, 0x5F,
    0x0F, 0x00, 0x02, 0x84, 0x01, 0x80, 0xE9, 0x53, 0xFC, 0x21, 0x00, 0x00,
    0x00, 0x00, 0x49, 0x45, 0x4E, 0x44, 0xAE, 0x42, 0x60, 0x82
  ];
  return new Uint8Array(pngData);
};

/**
 * Create individual folders for all categories and subcategories
 */
export const createAllCategoryFolders = async (): Promise<FolderCreationResult> => {
  const result: FolderCreationResult = {
    success: false,
    foldersCreated: 0,
    foldersSkipped: 0,
    errors: [],
    details: []
  };

  const placeholderFile = createPlaceholderFile();

  try {
    console.log('🔄 Creating individual folders for all categories and subcategories...');

    // Create category folders
    for (const category of CATEGORIES.filter(cat => cat.id !== 'all')) {
      const folderPath = `category/${category.displayName}/.placeholder`;
      
      try {
        const { error } = await supabase.storage
          .from('category-images')
          .upload(folderPath, placeholderFile, {
            cacheControl: '3600',
            upsert: false // Don't overwrite if exists
          });

        if (error) {
          if (error.message.includes('already exists')) {
            result.foldersSkipped++;
            result.details.push({
              type: 'category',
              name: category.displayName,
              folderPath: `category/${category.displayName}/`,
              status: 'exists'
            });
          } else {
            result.errors.push(`Category ${category.displayName}: ${error.message}`);
            result.details.push({
              type: 'category',
              name: category.displayName,
              folderPath: `category/${category.displayName}/`,
              status: 'error',
              error: error.message
            });
          }
        } else {
          result.foldersCreated++;
          result.details.push({
            type: 'category',
            name: category.displayName,
            folderPath: `category/${category.displayName}/`,
            status: 'created'
          });
        }
      } catch (err) {
        const errorMsg = err instanceof Error ? err.message : 'Unknown error';
        result.errors.push(`Category ${category.displayName}: ${errorMsg}`);
        result.details.push({
          type: 'category',
          name: category.displayName,
          folderPath: `category/${category.displayName}/`,
          status: 'error',
          error: errorMsg
        });
      }
    }

    // Create subcategory folders
    for (const category of CATEGORIES) {
      for (const subcategory of category.subcategories) {
        const folderPath = `subcategory/${subcategory.displayName}/.placeholder`;
        
        try {
          const { error } = await supabase.storage
            .from('category-images')
            .upload(folderPath, placeholderFile, {
              cacheControl: '3600',
              upsert: false // Don't overwrite if exists
            });

          if (error) {
            if (error.message.includes('already exists')) {
              result.foldersSkipped++;
              result.details.push({
                type: 'subcategory',
                name: subcategory.displayName,
                folderPath: `subcategory/${subcategory.displayName}/`,
                status: 'exists'
              });
            } else {
              result.errors.push(`Subcategory ${subcategory.displayName}: ${error.message}`);
              result.details.push({
                type: 'subcategory',
                name: subcategory.displayName,
                folderPath: `subcategory/${subcategory.displayName}/`,
                status: 'error',
                error: error.message
              });
            }
          } else {
            result.foldersCreated++;
            result.details.push({
              type: 'subcategory',
              name: subcategory.displayName,
              folderPath: `subcategory/${subcategory.displayName}/`,
              status: 'created'
            });
          }
        } catch (err) {
          const errorMsg = err instanceof Error ? err.message : 'Unknown error';
          result.errors.push(`Subcategory ${subcategory.displayName}: ${errorMsg}`);
          result.details.push({
            type: 'subcategory',
            name: subcategory.displayName,
            folderPath: `subcategory/${subcategory.displayName}/`,
            status: 'error',
            error: errorMsg
          });
        }
      }
    }

    result.success = result.errors.length === 0 || (result.foldersCreated + result.foldersSkipped) > 0;

    console.log('✅ Folder creation completed!');
    console.log(`📁 Created: ${result.foldersCreated} folders`);
    console.log(`📁 Skipped (already exist): ${result.foldersSkipped} folders`);
    console.log(`❌ Errors: ${result.errors.length}`);

    return result;

  } catch (error) {
    console.error('❌ Error creating category folders:', error);
    result.errors.push(error instanceof Error ? error.message : 'Unknown error occurred');
    return result;
  }
};

/**
 * Print detailed folder creation report
 */
export const printFolderCreationReport = async (): Promise<void> => {
  const result = await createAllCategoryFolders();
  
  console.log('\n=== 📁 CATEGORY FOLDER CREATION REPORT ===\n');
  
  console.log('📊 SUMMARY:');
  console.log(`   Folders Created: ${result.foldersCreated}`);
  console.log(`   Folders Skipped (already exist): ${result.foldersSkipped}`);
  console.log(`   Errors: ${result.errors.length}`);
  console.log(`   Total Processed: ${result.details.length}\n`);

  if (result.foldersCreated > 0) {
    console.log('✅ FOLDERS CREATED:');
    result.details
      .filter(detail => detail.status === 'created')
      .forEach(detail => {
        console.log(`   ✓ ${detail.type}: ${detail.name} → ${detail.folderPath}`);
      });
    console.log('');
  }

  if (result.foldersSkipped > 0) {
    console.log('📁 FOLDERS ALREADY EXIST:');
    result.details
      .filter(detail => detail.status === 'exists')
      .forEach(detail => {
        console.log(`   📂 ${detail.type}: ${detail.name} → ${detail.folderPath}`);
      });
    console.log('');
  }

  if (result.errors.length > 0) {
    console.log('❌ ERRORS:');
    result.details
      .filter(detail => detail.status === 'error')
      .forEach(detail => {
        console.log(`   ❌ ${detail.type}: ${detail.name} → ${detail.error}`);
      });
    console.log('');
  }

  console.log('🎯 NEXT STEPS:');
  console.log('1. Go to: https://supabase.com/dashboard/project/irkwpzcskeqtasutqnxp/storage/buckets/category-images');
  console.log('2. Navigate to category/ or subcategory/ folders');
  console.log('3. Open any individual folder (e.g., category/Tyres/)');
  console.log('4. Drag and drop your image into that specific folder');
  console.log('5. Image will automatically appear in the app!');
  console.log('');
  console.log('✅ PERFECT ORGANIZATION - EACH CATEGORY HAS ITS OWN FOLDER!');
};

/**
 * Export for easy access
 */
export const CategoryFolderCreator = {
  createAll: createAllCategoryFolders,
  printReport: printFolderCreationReport
};

// Make it available globally for console access
if (typeof window !== 'undefined') {
  (window as any).CategoryFolderCreator = CategoryFolderCreator;
}

export default CategoryFolderCreator;
