/**
 * Category Image Management Utility
 * Comprehensive tool for managing category and subcategory images
 * Provides automatic folder creation, image validation, and bulk operations
 */

import { validateStorageBucket, getBestCategoryImageUrl, uploadCategoryImage, getUploadInstructions } from '@/services/categoryImageService';
import { CATEGORIES } from '@/data/categoryData';

export interface CategoryImageStatus {
  id: string;
  name: string;
  displayName: string;
  type: 'category' | 'subcategory';
  categoryId?: string;
  hasImage: boolean;
  imageUrl?: string;
  filePath: string;
  suggestedFileName: string;
}

export interface ImageManagementReport {
  totalCategories: number;
  totalSubcategories: number;
  categoriesWithImages: number;
  subcategoriesWithImages: number;
  missingImages: CategoryImageStatus[];
  allItems: CategoryImageStatus[];
}

/**
 * Generate complete list of all categories and subcategories with their image status
 */
export const generateCategoryImageReport = async (): Promise<ImageManagementReport> => {
  const allItems: CategoryImageStatus[] = [];
  let categoriesWithImages = 0;
  let subcategoriesWithImages = 0;

  // Process categories
  for (const category of CATEGORIES.filter(cat => cat.id !== 'all')) {
    const imageUrl = await getBestCategoryImageUrl(category.id, 'category');
    const hasImage = !imageUrl.startsWith('data:'); // Not a fallback SVG
    
    if (hasImage) categoriesWithImages++;

    allItems.push({
      id: category.id,
      name: category.name,
      displayName: category.displayName,
      type: 'category',
      hasImage,
      imageUrl: hasImage ? imageUrl : undefined,
      filePath: `category/${category.id}.png`,
      suggestedFileName: `${category.id}.png`
    });
  }

  // Process subcategories
  for (const category of CATEGORIES) {
    for (const subcategory of category.subcategories) {
      const imageUrl = await getBestCategoryImageUrl(subcategory.id, 'subcategory');
      const hasImage = !imageUrl.startsWith('data:'); // Not a fallback SVG
      
      if (hasImage) subcategoriesWithImages++;

      allItems.push({
        id: subcategory.id,
        name: subcategory.name,
        displayName: subcategory.displayName,
        type: 'subcategory',
        categoryId: category.id,
        hasImage,
        imageUrl: hasImage ? imageUrl : undefined,
        filePath: `subcategory/${subcategory.id}.png`,
        suggestedFileName: `${subcategory.id}.png`
      });
    }
  }

  const missingImages = allItems.filter(item => !item.hasImage);

  return {
    totalCategories: CATEGORIES.filter(cat => cat.id !== 'all').length,
    totalSubcategories: allItems.filter(item => item.type === 'subcategory').length,
    categoriesWithImages,
    subcategoriesWithImages,
    missingImages,
    allItems
  };
};

/**
 * Print detailed image management report to console
 */
export const printImageManagementReport = async (): Promise<void> => {
  console.log('🔄 Generating category image report...');
  
  const report = await generateCategoryImageReport();
  
  console.log('\n=== 📊 CATEGORY IMAGE MANAGEMENT REPORT ===\n');
  
  // Summary
  console.log('📈 SUMMARY:');
  console.log(`   Categories: ${report.categoriesWithImages}/${report.totalCategories} have images`);
  console.log(`   Subcategories: ${report.subcategoriesWithImages}/${report.totalSubcategories} have images`);
  console.log(`   Missing images: ${report.missingImages.length} total\n`);
  
  // Categories with images
  const categoriesWithImages = report.allItems.filter(item => item.type === 'category' && item.hasImage);
  if (categoriesWithImages.length > 0) {
    console.log('✅ CATEGORIES WITH IMAGES:');
    categoriesWithImages.forEach(item => {
      console.log(`   ✓ ${item.displayName} (${item.id})`);
    });
    console.log('');
  }
  
  // Subcategories with images
  const subcategoriesWithImages = report.allItems.filter(item => item.type === 'subcategory' && item.hasImage);
  if (subcategoriesWithImages.length > 0) {
    console.log('✅ SUBCATEGORIES WITH IMAGES:');
    subcategoriesWithImages.forEach(item => {
      console.log(`   ✓ ${item.displayName} (${item.id})`);
    });
    console.log('');
  }
  
  // Missing images
  if (report.missingImages.length > 0) {
    console.log('❌ MISSING IMAGES:');
    console.log('\n📁 Categories missing images:');
    report.missingImages
      .filter(item => item.type === 'category')
      .forEach(item => {
        console.log(`   📂 ${item.displayName}`);
        console.log(`      ID: ${item.id}`);
        console.log(`      File path: ${item.filePath}`);
        console.log(`      File name: ${item.suggestedFileName}\n`);
      });
    
    console.log('📁 Subcategories missing images:');
    report.missingImages
      .filter(item => item.type === 'subcategory')
      .forEach(item => {
        console.log(`   📂 ${item.displayName}`);
        console.log(`      ID: ${item.id}`);
        console.log(`      File path: ${item.filePath}`);
        console.log(`      File name: ${item.suggestedFileName}\n`);
      });
  }
  
  console.log('=== 🎯 NEXT STEPS ===');
  console.log('1. Go to Supabase Storage: https://supabase.com/dashboard/project/irkwpzcskeqtasutqnxp/storage/buckets/category-images');
  console.log('2. Navigate to the appropriate folder (category/ or subcategory/)');
  console.log('3. Find the folder with the exact ID name');
  console.log('4. Drag and drop your image file');
  console.log('5. Name the file exactly as shown in "File name" above');
  console.log('6. The image will automatically appear in the app!\n');
};

/**
 * Validate storage bucket configuration
 */
export const validateCategoryImageStorage = async (): Promise<void> => {
  console.log('🔄 Validating category image storage...');

  const result = await validateStorageBucket();

  if (result.success) {
    console.log('✅ Storage validation completed successfully!');
    console.log(`📁 ${result.message}`);
  } else {
    console.error('❌ Storage validation failed:', result.message);
  }

  // Generate report after validation
  await printImageManagementReport();
};

/**
 * Validate that all required folders exist in Supabase Storage
 */
export const validateFolderStructure = async (): Promise<boolean> => {
  console.log('🔍 Validating folder structure...');
  
  try {
    const report = await generateCategoryImageReport();
    
    // Check if we can access the storage (basic validation)
    const hasAnyImages = report.allItems.some(item => item.hasImage);
    
    if (hasAnyImages) {
      console.log('✅ Folder structure is accessible');
      return true;
    } else {
      console.log('⚠️  No images found - folders may need to be created');
      return false;
    }
  } catch (error) {
    console.error('❌ Error validating folder structure:', error);
    return false;
  }
};

/**
 * Get specific instructions for uploading an image
 */
export const getUploadInstructionsForItem = (categoryId: string, type: 'category' | 'subcategory'): string => {
  const item = type === 'category'
    ? CATEGORIES.find(cat => cat.id === categoryId)
    : CATEGORIES.flatMap(cat => cat.subcategories).find(sub => sub.id === categoryId);

  if (!item) {
    return `❌ ${type} with ID "${categoryId}" not found`;
  }

  const instructions = getUploadInstructions(categoryId, type);

  return `📁 UPLOAD INSTRUCTIONS FOR: ${item.displayName}\n${instructions.instructions}`;
};

/**
 * Export all functions for easy access
 */
export const CategoryImageManager = {
  generateReport: generateCategoryImageReport,
  printReport: printImageManagementReport,
  validateStorage: validateCategoryImageStorage,
  validateStructure: validateFolderStructure,
  getInstructions: getUploadInstructionsForItem
};

// Make it available globally for console access
if (typeof window !== 'undefined') {
  (window as any).CategoryImageManager = CategoryImageManager;
}
