/**
 * MARKETPLACE PERFORMANCE MONITORING SYSTEM
 * 
 * This module provides comprehensive performance monitoring for the optimized marketplace system.
 * It tracks loading times, database query performance, and user experience metrics.
 */

export interface PerformanceMetrics {
  // Timing metrics
  loadTime: number;
  queryTime: number;
  renderTime: number;
  
  // Data metrics
  categoryId: string;
  productCount: number;
  productsPerSecond: number;
  
  // System metrics
  isOptimized: boolean;
  cacheHit: boolean;
  errorCount: number;
  
  // User experience metrics
  timeToFirstProduct: number;
  timeToInteractive: number;
  
  // Comparison metrics
  improvementPercentage: number;
  legacyLoadTime?: number;
}

export interface PerformanceComparison {
  before: {
    totalQueries: number;
    loadTime: number;
    networkRequests: number;
  };
  after: {
    totalQueries: number;
    loadTime: number;
    networkRequests: number;
  };
  improvements: {
    queryReduction: number;
    loadTimeImprovement: number;
    networkReduction: number;
  };
}

/**
 * Performance monitoring class for marketplace operations
 */
export class MarketplacePerformanceMonitor {
  private startTime: number;
  private metrics: Partial<PerformanceMetrics> = {};
  private isEnabled: boolean;

  constructor(enabled: boolean = true) {
    this.startTime = performance.now();
    this.isEnabled = enabled;
    this.metrics = {
      isOptimized: false,
      cacheHit: false,
      errorCount: 0,
    };
  }

  /**
   * Start timing a specific operation
   */
  startTiming(operation: string): void {
    if (!this.isEnabled) return;
    
    this.metrics[`${operation}StartTime` as keyof PerformanceMetrics] = performance.now() as any;
    console.log(`[PERFORMANCE] Starting ${operation} timing`);
  }

  /**
   * End timing for a specific operation
   */
  endTiming(operation: string): number {
    if (!this.isEnabled) return 0;
    
    const startTime = this.metrics[`${operation}StartTime` as keyof PerformanceMetrics] as number;
    if (!startTime) return 0;
    
    const duration = performance.now() - startTime;
    this.metrics[`${operation}Time` as keyof PerformanceMetrics] = duration as any;
    
    console.log(`[PERFORMANCE] ${operation} completed in ${duration.toFixed(2)}ms`);
    return duration;
  }

  /**
   * Record product loading metrics
   */
  recordProductLoad(categoryId: string, productCount: number, isOptimized: boolean = false): void {
    if (!this.isEnabled) return;
    
    const loadTime = performance.now() - this.startTime;
    const productsPerSecond = productCount / (loadTime / 1000);
    
    this.metrics = {
      ...this.metrics,
      categoryId,
      productCount,
      loadTime,
      productsPerSecond,
      isOptimized,
      timeToFirstProduct: loadTime,
    };

    console.log(`[PERFORMANCE_METRICS] Category: ${categoryId}`);
    console.log(`[PERFORMANCE_METRICS] Products: ${productCount}`);
    console.log(`[PERFORMANCE_METRICS] Load time: ${loadTime.toFixed(2)}ms`);
    console.log(`[PERFORMANCE_METRICS] Products/sec: ${productsPerSecond.toFixed(2)}`);
    console.log(`[PERFORMANCE_METRICS] Optimized: ${isOptimized}`);
  }

  /**
   * Record cache performance
   */
  recordCacheHit(hit: boolean): void {
    if (!this.isEnabled) return;
    
    this.metrics.cacheHit = hit;
    console.log(`[PERFORMANCE] Cache ${hit ? 'HIT' : 'MISS'}`);
  }

  /**
   * Record error occurrence
   */
  recordError(error: Error): void {
    if (!this.isEnabled) return;
    
    this.metrics.errorCount = (this.metrics.errorCount || 0) + 1;
    console.error(`[PERFORMANCE_ERROR] Error recorded:`, error.message);
  }

  /**
   * Calculate performance improvements
   */
  calculateImprovement(legacyLoadTime: number): PerformanceComparison {
    const currentLoadTime = this.metrics.loadTime || 0;
    const improvement = ((legacyLoadTime - currentLoadTime) / legacyLoadTime) * 100;
    
    const comparison: PerformanceComparison = {
      before: {
        totalQueries: 72, // 18 categories × 4 queries each
        loadTime: legacyLoadTime,
        networkRequests: 18,
      },
      after: {
        totalQueries: 4, // 1 category × 4 queries
        loadTime: currentLoadTime,
        networkRequests: 1,
      },
      improvements: {
        queryReduction: ((72 - 4) / 72) * 100, // 94.4% reduction
        loadTimeImprovement: improvement,
        networkReduction: ((18 - 1) / 18) * 100, // 94.4% reduction
      },
    };

    console.log(`[PERFORMANCE_IMPROVEMENT] Query reduction: ${comparison.improvements.queryReduction.toFixed(1)}%`);
    console.log(`[PERFORMANCE_IMPROVEMENT] Load time improvement: ${comparison.improvements.loadTimeImprovement.toFixed(1)}%`);
    console.log(`[PERFORMANCE_IMPROVEMENT] Network reduction: ${comparison.improvements.networkReduction.toFixed(1)}%`);

    return comparison;
  }

  /**
   * Get current metrics
   */
  getMetrics(): PerformanceMetrics {
    return {
      loadTime: 0,
      queryTime: 0,
      renderTime: 0,
      categoryId: '',
      productCount: 0,
      productsPerSecond: 0,
      isOptimized: false,
      cacheHit: false,
      errorCount: 0,
      timeToFirstProduct: 0,
      timeToInteractive: 0,
      improvementPercentage: 0,
      ...this.metrics,
    } as PerformanceMetrics;
  }

  /**
   * Generate performance report
   */
  generateReport(): string {
    const metrics = this.getMetrics();
    
    return `
🚀 MARKETPLACE PERFORMANCE REPORT
================================

📊 LOADING METRICS:
- Category: ${metrics.categoryId}
- Products loaded: ${metrics.productCount}
- Load time: ${metrics.loadTime.toFixed(2)}ms
- Products per second: ${metrics.productsPerSecond.toFixed(2)}

⚡ OPTIMIZATION STATUS:
- System optimized: ${metrics.isOptimized ? '✅ YES' : '❌ NO'}
- Cache hit: ${metrics.cacheHit ? '✅ YES' : '❌ NO'}
- Errors: ${metrics.errorCount}

🎯 PERFORMANCE TARGETS:
- Initial load: ${metrics.loadTime < 500 ? '✅ PASSED' : '❌ FAILED'} (target: <500ms)
- Products/sec: ${metrics.productsPerSecond > 10 ? '✅ GOOD' : '⚠️ SLOW'} (target: >10/sec)

${metrics.legacyLoadTime ? `
📈 IMPROVEMENTS:
- Load time improvement: ${metrics.improvementPercentage.toFixed(1)}%
- Legacy time: ${metrics.legacyLoadTime.toFixed(2)}ms
- Current time: ${metrics.loadTime.toFixed(2)}ms
` : ''}
================================
    `.trim();
  }

  /**
   * Log performance summary
   */
  logSummary(): void {
    if (!this.isEnabled) return;
    
    console.log(this.generateReport());
  }
}

/**
 * Global performance monitor instance
 */
export const globalPerformanceMonitor = new MarketplacePerformanceMonitor(
  import.meta.env.DEV || import.meta.env.VITE_ENABLE_PERFORMANCE_MONITORING === 'true'
);

/**
 * Performance monitoring hook for React components
 */
export const usePerformanceMonitoring = (categoryId?: string) => {
  const monitor = new MarketplacePerformanceMonitor();
  
  return {
    startTiming: monitor.startTiming.bind(monitor),
    endTiming: monitor.endTiming.bind(monitor),
    recordProductLoad: monitor.recordProductLoad.bind(monitor),
    recordCacheHit: monitor.recordCacheHit.bind(monitor),
    recordError: monitor.recordError.bind(monitor),
    calculateImprovement: monitor.calculateImprovement.bind(monitor),
    getMetrics: monitor.getMetrics.bind(monitor),
    generateReport: monitor.generateReport.bind(monitor),
    logSummary: monitor.logSummary.bind(monitor),
  };
};

/**
 * Performance comparison utility
 */
export const comparePerformance = (
  legacyMetrics: { loadTime: number; queries: number; requests: number },
  optimizedMetrics: { loadTime: number; queries: number; requests: number }
): PerformanceComparison => {
  return {
    before: {
      totalQueries: legacyMetrics.queries,
      loadTime: legacyMetrics.loadTime,
      networkRequests: legacyMetrics.requests,
    },
    after: {
      totalQueries: optimizedMetrics.queries,
      loadTime: optimizedMetrics.loadTime,
      networkRequests: optimizedMetrics.requests,
    },
    improvements: {
      queryReduction: ((legacyMetrics.queries - optimizedMetrics.queries) / legacyMetrics.queries) * 100,
      loadTimeImprovement: ((legacyMetrics.loadTime - optimizedMetrics.loadTime) / legacyMetrics.loadTime) * 100,
      networkReduction: ((legacyMetrics.requests - optimizedMetrics.requests) / legacyMetrics.requests) * 100,
    },
  };
};
