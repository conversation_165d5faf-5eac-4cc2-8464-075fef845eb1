/**
 * Test the new folder-based image system
 * Use this to verify that images load correctly from the new folder structure
 */

import { getBestCategoryImageUrl, getFallbackImageUrl } from '@/services/categoryImageService';

/**
 * Test the new folder-based image loading system
 */
export const testNewImageSystem = async () => {
  console.log('=== TESTING NEW FOLDER-BASED IMAGE SYSTEM ===');
  console.log('');

  // Test categories
  console.log('🗂️ TESTING CATEGORIES:');
  const testCategories = ['tyres', 'brakes', 'filters', 'engine'];
  
  for (const categoryId of testCategories) {
    try {
      const imageUrl = await getBestCategoryImageUrl(categoryId, 'category');
      const isFallback = imageUrl.startsWith('data:');
      
      console.log(`📁 ${categoryId}:`);
      console.log(`   URL: ${isFallback ? 'FALLBACK SVG' : imageUrl}`);
      console.log(`   Status: ${isFallback ? '❌ No image found' : '✅ Image found'}`);
      console.log('');
    } catch (error) {
      console.log(`📁 ${categoryId}: ❌ Error - ${error}`);
      console.log('');
    }
  }

  // Test subcategories
  console.log('🗂️ TESTING SUBCATEGORIES:');
  const testSubcategories = ['brake-pads', 'brake-discs', 'oil-filters', 'air-filters'];
  
  for (const subcategoryId of testSubcategories) {
    try {
      const imageUrl = await getBestCategoryImageUrl(subcategoryId, 'subcategory');
      const isFallback = imageUrl.startsWith('data:');
      
      console.log(`📁 ${subcategoryId}:`);
      console.log(`   URL: ${isFallback ? 'FALLBACK SVG' : imageUrl}`);
      console.log(`   Status: ${isFallback ? '❌ No image found' : '✅ Image found'}`);
      console.log('');
    } catch (error) {
      console.log(`📁 ${subcategoryId}: ❌ Error - ${error}`);
      console.log('');
    }
  }

  console.log('=== TESTING COMPLETE ===');
  console.log('');
  console.log('📋 NEXT STEPS:');
  console.log('1. Go to: https://supabase.com/dashboard/project/irkwpzcskeqtasutqnxp/storage/buckets/category-images');
  console.log('2. Open category/ folder');
  console.log('3. Open any category folder (e.g., Tyres/)');
  console.log('4. Drag and drop ANY image file');
  console.log('5. Refresh the marketplace page');
  console.log('6. The image should appear in the category navigation!');
  console.log('');
  console.log('🎯 FOLDER STRUCTURE CREATED:');
  console.log('   category/Tyres/ ← Upload tire images here');
  console.log('   category/Brakes/ ← Upload brake images here');
  console.log('   subcategory/Brake Pads/ ← Upload brake pad images here');
  console.log('   ... and so on for all categories/subcategories');
};

/**
 * Test image loading for a specific category
 */
export const testCategoryImage = async (categoryId: string) => {
  console.log(`🔍 Testing category: ${categoryId}`);
  
  try {
    const imageUrl = await getBestCategoryImageUrl(categoryId, 'category');
    const isFallback = imageUrl.startsWith('data:');
    
    console.log(`Result: ${isFallback ? 'No image found (using fallback)' : 'Image found!'}`);
    console.log(`URL: ${imageUrl}`);
    
    if (!isFallback) {
      // Test if the image is actually accessible
      try {
        const response = await fetch(imageUrl, { method: 'HEAD' });
        console.log(`Accessibility: ${response.ok ? '✅ Accessible' : '❌ Not accessible'}`);
      } catch (error) {
        console.log(`Accessibility: ❌ Error - ${error}`);
      }
    }
  } catch (error) {
    console.log(`❌ Error: ${error}`);
  }
};

/**
 * Test image loading for a specific subcategory
 */
export const testSubcategoryImage = async (subcategoryId: string) => {
  console.log(`🔍 Testing subcategory: ${subcategoryId}`);
  
  try {
    const imageUrl = await getBestCategoryImageUrl(subcategoryId, 'subcategory');
    const isFallback = imageUrl.startsWith('data:');
    
    console.log(`Result: ${isFallback ? 'No image found (using fallback)' : 'Image found!'}`);
    console.log(`URL: ${imageUrl}`);
    
    if (!isFallback) {
      // Test if the image is actually accessible
      try {
        const response = await fetch(imageUrl, { method: 'HEAD' });
        console.log(`Accessibility: ${response.ok ? '✅ Accessible' : '❌ Not accessible'}`);
      } catch (error) {
        console.log(`Accessibility: ❌ Error - ${error}`);
      }
    }
  } catch (error) {
    console.log(`❌ Error: ${error}`);
  }
};

/**
 * Quick test to run in browser console
 */
export const quickTest = () => {
  console.log('🚀 Running quick test...');
  testNewImageSystem();
};
