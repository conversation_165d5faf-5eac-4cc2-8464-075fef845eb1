import { createClient } from '@supabase/supabase-js';

// Supabase configuration with service role key for admin operations
const supabaseUrl = 'https://irkwpzcskeqtasutqnxp.supabase.co';
const supabaseServiceKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imlya3dwemNza2VxdGFzdXRxbnhwIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0NzQ5ODY4MywiZXhwIjoyMDYzMDc0NjgzfQ.PLfCCbh4zgAJDDC6sS82fCU32JYA7e2VmwzvavF-PW8';

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function debugWishlistTable() {
  try {
    console.log('🔍 Debugging wishlist table structure and constraints...');

    // Step 1: Check table structure
    console.log('📋 Step 1: Checking table structure...');
    const { data: tableInfo, error: tableError } = await supabase.rpc('exec_sql', {
      sql_query: `
        SELECT 
          column_name, 
          data_type, 
          is_nullable, 
          column_default,
          character_maximum_length
        FROM information_schema.columns 
        WHERE table_name = 'consumer_wishlists' 
        ORDER BY ordinal_position;
      `
    });

    if (tableError) {
      console.error('❌ Error checking table structure:', tableError);
    } else {
      console.log('✅ Table structure:');
      console.table(tableInfo);
    }

    // Step 2: Check constraints
    console.log('📋 Step 2: Checking table constraints...');
    const { data: constraints, error: constraintError } = await supabase.rpc('exec_sql', {
      sql_query: `
        SELECT 
          constraint_name, 
          constraint_type, 
          table_name,
          column_name
        FROM information_schema.table_constraints tc
        LEFT JOIN information_schema.key_column_usage kcu 
          ON tc.constraint_name = kcu.constraint_name
        WHERE tc.table_name = 'consumer_wishlists';
      `
    });

    if (constraintError) {
      console.error('❌ Error checking constraints:', constraintError);
    } else {
      console.log('✅ Table constraints:');
      console.table(constraints);
    }

    // Step 3: Check current policies
    console.log('📋 Step 3: Checking current RLS policies...');
    const { data: policies, error: policyError } = await supabase.rpc('exec_sql', {
      sql_query: `
        SELECT 
          policyname, 
          permissive,
          roles,
          cmd, 
          qual, 
          with_check 
        FROM pg_policies 
        WHERE tablename = 'consumer_wishlists' 
        ORDER BY policyname;
      `
    });

    if (policyError) {
      console.error('❌ Error checking policies:', policyError);
    } else {
      console.log('✅ Current RLS policies:');
      console.table(policies);
    }

    // Step 4: Check if RLS is actually enabled
    console.log('📋 Step 4: Checking RLS status...');
    const { data: rlsStatus, error: rlsError } = await supabase.rpc('exec_sql', {
      sql_query: `
        SELECT 
          schemaname,
          tablename,
          rowsecurity
        FROM pg_tables 
        WHERE tablename = 'consumer_wishlists';
      `
    });

    if (rlsError) {
      console.error('❌ Error checking RLS status:', rlsError);
    } else {
      console.log('✅ RLS status:');
      console.table(rlsStatus);
    }

    // Step 5: Check triggers
    console.log('📋 Step 5: Checking triggers...');
    const { data: triggers, error: triggerError } = await supabase.rpc('exec_sql', {
      sql_query: `
        SELECT 
          trigger_name,
          event_manipulation,
          action_timing,
          action_statement
        FROM information_schema.triggers 
        WHERE event_object_table = 'consumer_wishlists';
      `
    });

    if (triggerError) {
      console.error('❌ Error checking triggers:', triggerError);
    } else {
      console.log('✅ Table triggers:');
      console.table(triggers);
    }

    // Step 6: Try direct insert with service role
    console.log('📋 Step 6: Testing direct insert with service role...');
    const { data: serviceInsert, error: serviceError } = await supabase
      .from('consumer_wishlists')
      .insert({
        consumer_phone: '+213555123456',
        product_id: 'SERVICE-TEST-001',
        product_name: 'Service Role Test',
        priority: 1
      })
      .select()
      .single();

    if (serviceError) {
      console.error('❌ Service role insert failed:', serviceError);
    } else {
      console.log('✅ Service role insert successful:', serviceInsert);
      
      // Clean up
      await supabase
        .from('consumer_wishlists')
        .delete()
        .eq('product_id', 'SERVICE-TEST-001');
      console.log('✅ Service role test data cleaned up');
    }

    // Step 7: Check permissions
    console.log('📋 Step 7: Checking table permissions...');
    const { data: permissions, error: permError } = await supabase.rpc('exec_sql', {
      sql_query: `
        SELECT 
          grantee,
          privilege_type,
          is_grantable
        FROM information_schema.table_privileges 
        WHERE table_name = 'consumer_wishlists';
      `
    });

    if (permError) {
      console.error('❌ Error checking permissions:', permError);
    } else {
      console.log('✅ Table permissions:');
      console.table(permissions);
    }

  } catch (error) {
    console.error('❌ Error in debug:', error);
  }
}

// Create exec_sql function if it doesn't exist
async function ensureExecSqlFunction() {
  try {
    const createFunctionSQL = `
      CREATE OR REPLACE FUNCTION exec_sql(sql_query TEXT)
      RETURNS TABLE(result JSONB) AS $$
      DECLARE
        rec RECORD;
        results JSONB := '[]'::JSONB;
      BEGIN
        FOR rec IN EXECUTE sql_query LOOP
          results := results || to_jsonb(rec);
        END LOOP;
        RETURN QUERY SELECT results;
      EXCEPTION WHEN OTHERS THEN
        RETURN QUERY SELECT to_jsonb(SQLERRM);
      END;
      $$ LANGUAGE plpgsql SECURITY DEFINER;
    `;

    await supabase.rpc('exec_sql', { sql_query: createFunctionSQL });
    console.log('✅ exec_sql function ready');
  } catch (error) {
    console.log('📝 exec_sql function check completed');
  }
}

// Run the debug
console.log('🚀 Starting wishlist table debug...');
ensureExecSqlFunction().then(() => {
  debugWishlistTable();
});
