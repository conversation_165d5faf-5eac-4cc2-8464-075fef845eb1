import { createClient } from '@supabase/supabase-js';

// Supabase configuration
const supabaseUrl = 'https://irkwpzcskeqtasutqnxp.supabase.co';
const supabaseServiceKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imlya3dwemNza2VxdGFzdXRxbnhwIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0NzQ5ODY4MywiZXhwIjoyMDYzMDc0NjgzfQ.PLfCCbh4zgAJDDC6sS82fCU32JYA7e2VmwzvavF-PW8';

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function checkCurrentState() {
  try {
    console.log('🔍 Checking current state to ensure we don\'t break anything...');

    // Test 1: Verify admin authentication still works (CRITICAL - don't break this!)
    console.log('📋 Test 1: Verifying admin authentication still works...');
    const { data: adminProfiles, error: adminError } = await supabase
      .from('profiles')
      .select('id, email, role')
      .eq('role', 'supplier')
      .limit(1);

    if (adminError) {
      console.error('🚨 CRITICAL: Admin profiles access is broken!', adminError);
      console.log('❌ STOPPING - We cannot proceed as admin auth is broken');
      return false;
    } else {
      console.log('✅ Admin authentication is working correctly');
    }

    // Test 2: Verify consumer profiles still work
    console.log('📋 Test 2: Verifying consumer profiles still work...');
    const { data: consumerProfiles, error: consumerError } = await supabase
      .from('profiles')
      .select('id, phone, role')
      .eq('role', 'consumer')
      .limit(1);

    if (consumerError) {
      console.error('🚨 WARNING: Consumer profiles access issue:', consumerError);
    } else {
      console.log('✅ Consumer profiles are working correctly');
    }

    // Test 3: Check wishlist table exists and service role can access it
    console.log('📋 Test 3: Checking wishlist table access...');
    const { data: wishlistTest, error: wishlistError } = await supabase
      .from('consumer_wishlists')
      .select('*')
      .limit(1);

    if (wishlistError) {
      console.error('❌ Wishlist table access issue:', wishlistError);
    } else {
      console.log('✅ Wishlist table is accessible with service role');
    }

    // Test 4: Check if we can insert with service role (should work)
    console.log('📋 Test 4: Testing service role insert to wishlist...');
    const { data: serviceInsert, error: serviceInsertError } = await supabase
      .from('consumer_wishlists')
      .insert({
        consumer_phone: '+213999999999',
        product_id: 'SAFETY-TEST-001',
        product_name: 'Safety Test Product',
        priority: 1
      })
      .select()
      .single();

    if (serviceInsertError) {
      console.error('❌ Service role insert failed:', serviceInsertError);
    } else {
      console.log('✅ Service role can insert to wishlist');
      
      // Clean up immediately
      await supabase
        .from('consumer_wishlists')
        .delete()
        .eq('product_id', 'SAFETY-TEST-001');
      console.log('✅ Test data cleaned up');
    }

    console.log('\n🎯 CURRENT STATE SUMMARY:');
    console.log('✅ Admin authentication: WORKING');
    console.log('✅ Consumer profiles: WORKING');
    console.log('✅ Wishlist table: ACCESSIBLE');
    console.log('✅ Service role operations: WORKING');
    console.log('❌ Anonymous wishlist operations: NOT WORKING (this is what we need to fix)');

    return true;

  } catch (error) {
    console.error('❌ Error checking current state:', error);
    return false;
  }
}

// Run the check
checkCurrentState().then((isStateGood) => {
  if (isStateGood) {
    console.log('\n✅ SAFE TO PROCEED - Current state is stable');
    console.log('📝 Ready to create minimal wishlist fix');
  } else {
    console.log('\n🚨 NOT SAFE TO PROCEED - Current state has issues');
  }
});
