/**
 * Generate Exhaust Gas Recirculation Category Migration SQL
 * Following the exact same pattern as previous successful migrations
 */

import { CATEGORIES } from '../src/data/categoryData.js';
import fs from 'fs';
import path from 'path';

function generateExhaustGasRecirculationMigrationSQL(): string {
  // Find the exhaust-gas-recirculation category
  const egrCategory = CATEGORIES.find(cat => cat.id === 'exhaust-gas-recirculation');

  if (!egrCategory) {
    throw new Error('Exhaust gas recirculation category not found in categoryData.ts');
  }

  console.log(`Found Exhaust gas recirculation category with ${egrCategory.subcategories.length} subcategories`);

  // Get current timestamp for migration filename
  const timestamp = new Date().toISOString().replace(/[-:]/g, '').replace(/\..+/, '').replace('T', '');
  
  let sql = `-- =====================================================
-- AROUZ MARKET - Add Exhaust Gas Recirculation Category Migration
-- Generated: ${new Date().toISOString()}
-- Purpose: Add Exhaust gas recirculation category with all subcategories (100% accuracy)
-- Strategy: Follow exact same pattern as previous successful migrations
-- =====================================================

-- Insert Exhaust gas recirculation category
INSERT INTO categories (id, name, display_name, description, id_prefix, sort_order) VALUES
  ('exhaust-gas-recirculation', 'Exhaust gas recirculation', 'Exhaust gas recirculation', 'EGR valves, coolers, gaskets, and related exhaust gas recirculation components', 'PROD', 11)
ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  display_name = EXCLUDED.display_name,
  description = EXCLUDED.description,
  id_prefix = EXCLUDED.id_prefix,
  sort_order = EXCLUDED.sort_order;

-- Insert Exhaust gas recirculation subcategories (${egrCategory.subcategories.length} total)
INSERT INTO subcategories (id, name, display_name, category_id, sort_order) VALUES\n`;

  // Generate subcategories
  const subcategoryValues = egrCategory.subcategories.map((sub, index) => {
    return `  ('${sub.id}', '${sub.name.replace(/'/g, "''")}', '${sub.displayName.replace(/'/g, "''")}', '${sub.categoryId}', ${index + 1})`;
  }).join(',\n');

  sql += subcategoryValues + '\n';
  sql += `ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  display_name = EXCLUDED.display_name,
  category_id = EXCLUDED.category_id,
  sort_order = EXCLUDED.sort_order;

-- Verification queries
SELECT 'Exhaust gas recirculation category migration completed successfully!' as status;
SELECT COUNT(*) as total_categories FROM categories;
SELECT COUNT(*) as total_subcategories FROM subcategories;
SELECT COUNT(*) as egr_subcategories FROM subcategories WHERE category_id = 'exhaust-gas-recirculation';

-- Show the new exhaust gas recirculation category and its subcategories
SELECT 'EXHAUST GAS RECIRCULATION CATEGORY:' as section, id, name, display_name, description FROM categories WHERE id = 'exhaust-gas-recirculation';
SELECT 'EXHAUST GAS RECIRCULATION SUBCATEGORIES:' as section, id, name, display_name, sort_order FROM subcategories WHERE category_id = 'exhaust-gas-recirculation' ORDER BY sort_order;`;

  return sql;
}

// Generate the migration
const migrationSQL = generateExhaustGasRecirculationMigrationSQL();

// Write to file with timestamp
const timestamp = new Date().toISOString().replace(/[-:]/g, '').replace(/\..+/, '').replace('T', '');
const outputPath = path.join(process.cwd(), `supabase/migrations/${timestamp}_add_exhaust_gas_recirculation_category.sql`);
fs.writeFileSync(outputPath, migrationSQL, 'utf8');

console.log('✅ Exhaust gas recirculation category migration SQL generated successfully!');
console.log(`📁 File saved to: ${outputPath}`);
console.log(`📊 Exhaust gas recirculation subcategories: ${CATEGORIES.find(cat => cat.id === 'exhaust-gas-recirculation')?.subcategories.length || 0}`);
