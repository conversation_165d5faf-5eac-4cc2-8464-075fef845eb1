-- EMERGENCY INVESTIGATION: Current Database State
-- Run this to understand EXAC<PERSON><PERSON> what policies exist and what's broken
-- This version bypasses RLS issues by focusing on system tables first
-- Date: July 8, 2025

-- CRITICAL: First show what RLS policies exist (this should always work)
SELECT
  '🔍 CURRENT_RLS_POLICIES' as investigation_type,
  tablename,
  policyname,
  cmd as command_type,
  qual as using_condition
FROM pg_policies
WHERE schemaname = 'public'
AND tablename IN ('profiles', 'products', 'orders', 'wholesale_pricing_tiers')
ORDER BY tablename, policyname;

-- Show which tables have RLS enabled
SELECT
  '🔒 RLS_STATUS' as investigation_type,
  tablename,
  rowsecurity as rls_enabled
FROM pg_tables
WHERE schemaname = 'public'
AND tablename IN ('profiles', 'products', 'orders')
ORDER BY tablename;

-- Check if there are any recursive policy patterns (the problem!)
SELECT
  '🔄 RECURSIVE_POLICIES_CHECK' as investigation_type,
  tablename,
  policyname,
  'CONTAINS_RECURSIVE_PATTERN' as issue
FROM pg_policies
WHERE schemaname = 'public'
AND tablename = 'profiles'
AND (
  qual LIKE '%EXISTS%SELECT%FROM profiles%'
  OR with_check LIKE '%EXISTS%SELECT%FROM profiles%'
);

-- Check auth.users table (this should always work since it's not affected by our RLS)
SELECT
  '👤 AUTH_USERS_SAMPLE' as investigation_type,
  id,
  email,
  created_at,
  email_confirmed_at
FROM auth.users
WHERE email LIKE '%arouzmarket%' OR email LIKE '%hamza%'
ORDER BY created_at DESC
LIMIT 3;

-- Try to test basic access (these might fail due to RLS, but let's see)
-- If these fail, we know RLS is the problem
SELECT
  '📊 TEST_PROFILES_COUNT' as test_type,
  count(*) as profile_count
FROM profiles;

SELECT
  '📦 TEST_PRODUCTS_COUNT' as test_type,
  count(*) as product_count
FROM products;

SELECT
  '📋 TEST_ORDERS_COUNT' as test_type,
  count(*) as order_count
FROM orders;

-- Show current connection info
SELECT
  '🔗 CONNECTION_INFO' as investigation_type,
  current_user as current_user,
  session_user as session_user;

-- FINAL SUMMARY
SELECT
  '✅ INVESTIGATION_COMPLETE' as status,
  'Analysis complete - check results above' as message,
  now() as timestamp;
