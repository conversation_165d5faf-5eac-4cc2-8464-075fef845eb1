import { createClient } from '@supabase/supabase-js';

// Supabase configuration with service role key for admin operations
const supabaseUrl = 'https://irkwpzcskeqtasutqnxp.supabase.co';
const supabaseServiceKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imlya3dwemNza2VxdGFzdXRxbnhwIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0NzQ5ODY4MywiZXhwIjoyMDYzMDc0NjgzfQ.PLfCCbh4zgAJDDC6sS82fCU32JYA7e2VmwzvavF-PW8';

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function fixWishlistPoliciesV2() {
  try {
    console.log('🔧 Fixing wishlist RLS policies (v2 - matching orders pattern)...');

    // Step 1: Drop existing policies
    console.log('📋 Step 1: Dropping existing policies...');
    
    const dropCommands = [
      `DROP POLICY IF EXISTS "consumer_wishlists_service_role_access" ON consumer_wishlists;`,
      `DROP POLICY IF EXISTS "consumer_wishlists_consumer_select" ON consumer_wishlists;`,
      `DROP POLICY IF EXISTS "consumer_wishlists_consumer_insert" ON consumer_wishlists;`,
      `DROP POLICY IF EXISTS "consumer_wishlists_consumer_update" ON consumer_wishlists;`,
      `DROP POLICY IF EXISTS "consumer_wishlists_consumer_delete" ON consumer_wishlists;`
    ];

    for (const sql of dropCommands) {
      try {
        await supabase.rpc('exec_sql', { sql_query: sql });
        console.log('✅ Dropped policy');
      } catch (error) {
        console.log('⚠️ Policy may not exist:', error.message);
      }
    }

    // Step 2: Create new policies matching the orders pattern exactly
    console.log('📋 Step 2: Creating new policies (matching orders pattern)...');
    
    const newPolicies = [
      // Service role access
      {
        name: 'consumer_wishlists_service_role_access',
        sql: `CREATE POLICY "consumer_wishlists_service_role_access" ON consumer_wishlists
              FOR ALL
              USING (auth.role() = 'service_role')
              WITH CHECK (auth.role() = 'service_role');`
      },
      // Consumer SELECT - exact match with orders pattern
      {
        name: 'consumer_wishlists_consumer_access',
        sql: `CREATE POLICY "consumer_wishlists_consumer_access" ON consumer_wishlists
              FOR SELECT
              USING (
                consumer_phone = current_setting('app.consumer_phone', true)
              );`
      },
      // Consumer INSERT - using the orders pattern (check if setting is not null)
      {
        name: 'consumer_wishlists_creation_access',
        sql: `CREATE POLICY "consumer_wishlists_creation_access" ON consumer_wishlists
              FOR INSERT
              WITH CHECK (
                current_setting('app.consumer_phone', true) IS NOT NULL
              );`
      },
      // Consumer UPDATE - match orders pattern
      {
        name: 'consumer_wishlists_update_access',
        sql: `CREATE POLICY "consumer_wishlists_update_access" ON consumer_wishlists
              FOR UPDATE
              USING (
                consumer_phone = current_setting('app.consumer_phone', true)
              )
              WITH CHECK (
                consumer_phone = current_setting('app.consumer_phone', true)
              );`
      },
      // Consumer DELETE - match orders pattern
      {
        name: 'consumer_wishlists_delete_access',
        sql: `CREATE POLICY "consumer_wishlists_delete_access" ON consumer_wishlists
              FOR DELETE
              USING (
                consumer_phone = current_setting('app.consumer_phone', true)
              );`
      }
    ];

    for (const policy of newPolicies) {
      try {
        await supabase.rpc('exec_sql', { sql_query: policy.sql });
        console.log(`✅ Created policy: ${policy.name}`);
      } catch (error) {
        console.error(`❌ Error creating policy ${policy.name}:`, error.message);
      }
    }

    // Step 3: Ensure RLS is enabled
    console.log('📋 Step 3: Ensuring RLS is enabled...');
    try {
      await supabase.rpc('exec_sql', { 
        sql_query: 'ALTER TABLE consumer_wishlists ENABLE ROW LEVEL SECURITY;' 
      });
      console.log('✅ RLS enabled');
    } catch (error) {
      console.log('⚠️ RLS may already be enabled:', error.message);
    }

    console.log('🎉 Wishlist RLS policies fixed successfully (v2)!');

    // Step 4: Test the fix
    console.log('📋 Step 4: Testing the fix...');
    await testWishlistAfterFixV2();

  } catch (error) {
    console.error('❌ Error fixing wishlist policies:', error);
  }
}

async function testWishlistAfterFixV2() {
  try {
    // Switch to anon key for testing
    const testSupabase = createClient(supabaseUrl, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imlya3dwemNza2VxdGFzdXRxbnhwIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDc0OTg2ODMsImV4cCI6MjA2MzA3NDY4M30.bUv9zxx9q_sVUwv_-G6WbHsA53-SU5tCCWbOS_5Xp94');
    
    const testPhone = '+213555123456';
    
    // Set context
    console.log('Setting context...');
    const { data: contextResult, error: contextError } = await testSupabase.rpc('set_app_config', {
      setting_name: 'app.consumer_phone',
      setting_value: testPhone,
      is_local: false
    });

    if (contextError) {
      console.error('❌ Error setting context:', contextError);
      return;
    }
    console.log('✅ Context set:', contextResult);

    // Test insert
    console.log('Testing insert...');
    const { data, error } = await testSupabase
      .from('consumer_wishlists')
      .insert({
        consumer_phone: testPhone,
        product_id: 'TEST-V2-001',
        product_name: 'Test Product V2',
        priority: 1
      })
      .select()
      .single();

    if (error) {
      console.error('❌ Test insert failed:', error.message);
      console.error('Error code:', error.code);
    } else {
      console.log('✅ Test insert successful!');
      console.log('Inserted item:', data);
      
      // Test select
      const { data: selectData, error: selectError } = await testSupabase
        .from('consumer_wishlists')
        .select('*')
        .eq('product_id', 'TEST-V2-001');

      if (selectError) {
        console.error('❌ Test select failed:', selectError.message);
      } else {
        console.log('✅ Test select successful:', selectData);
      }
      
      // Clean up test data
      const { error: deleteError } = await testSupabase
        .from('consumer_wishlists')
        .delete()
        .eq('product_id', 'TEST-V2-001');

      if (deleteError) {
        console.error('❌ Cleanup failed:', deleteError.message);
      } else {
        console.log('✅ Test data cleaned up');
      }
    }

  } catch (error) {
    console.error('❌ Error testing fix:', error);
  }
}

// Create exec_sql function if it doesn't exist
async function ensureExecSqlFunction() {
  try {
    const createFunctionSQL = `
      CREATE OR REPLACE FUNCTION exec_sql(sql_query TEXT)
      RETURNS TEXT AS $$
      BEGIN
        EXECUTE sql_query;
        RETURN 'Success';
      EXCEPTION WHEN OTHERS THEN
        RETURN SQLERRM;
      END;
      $$ LANGUAGE plpgsql SECURITY DEFINER;
    `;

    await supabase.rpc('exec_sql', { sql_query: createFunctionSQL });
    console.log('✅ exec_sql function ready');
  } catch (error) {
    // Function might already exist
    console.log('📝 exec_sql function check completed');
  }
}

// Run the fix
console.log('🚀 Starting wishlist RLS policy fix (v2)...');
ensureExecSqlFunction().then(() => {
  fixWishlistPoliciesV2();
});
