# 🔐 AROUZ MARKET - COMPLETE AUTHENTICATION ARCHITECTURE

## 🎯 **CRITICAL FINDINGS FROM INVESTIGATION**

### **THE ROOT PROBLEM:**
The current RLS policies are **CONFLICTING** and **RECURSIVE**, causing:
- ❌ Admin panels to fail (suppliers/merchants can't access their data)
- ❌ Anonymous users to access sensitive data (security breach)
- ❌ Connection failures due to infinite recursion in policies

---

## 🏗️ **COMPLETE USER ARCHITECTURE**

### **1. ADMIN USERS (Suppliers & Merchants)**
- **Authentication Method:** Supabase Auth (email + password)
- **Session Storage:** Supabase session + localStorage flags
- **Database Access:** Full access to their own data via `auth.uid()`
- **Tables Accessed:** profiles, products, orders, order_items, shipments
- **RLS Requirements:** Must access data where `auth.uid() = profile.id`

### **2. CONSUMER USERS**
- **Authentication Method:** Simplified phone auth (NO Supabase auth)
- **Session Storage:** localStorage only (`phone_auth_session`)
- **Database Access:** Limited access via phone number matching
- **Tables Accessed:** profiles (own), orders (own), order_items (own orders)
- **RLS Requirements:** Must access data where `phone = consumer_phone`

### **3. SHIPPING COMPANY USERS**
- **Authentication Method:** Login code system (NO Supabase auth)
- **Session Storage:** localStorage only (`shipping_company_session`)
- **Database Access:** Access to assigned shipments only
- **Tables Accessed:** shipments, orders (assigned), shipping_companies
- **RLS Requirements:** Must access data via `current_setting('app.shipping_company_code')`

---

## 🚨 **CURRENT BROKEN POLICIES**

### **Profiles Table Issues:**
1. **RECURSIVE POLICY:** `profiles_consumer_login_check` references profiles table within itself
2. **OVERLY PERMISSIVE:** Anonymous users can read all consumer profiles
3. **CONFLICTING CONDITIONS:** Multiple policies with overlapping conditions

### **Orders/Shipments Issues:**
1. **CIRCULAR DEPENDENCY:** Orders policies reference order_items, which reference orders
2. **MISSING AUTH CHECKS:** Some policies don't properly validate user authentication
3. **INCONSISTENT PATTERNS:** Different tables use different authentication patterns

---

## ✅ **PERFECT RLS POLICY REQUIREMENTS**

### **Profiles Table - MUST SUPPORT:**
1. **Admin Access:** `auth.uid() = id` (suppliers/merchants)
2. **Consumer Signup:** Anonymous insert for `role = 'consumer'`
3. **Consumer Login Check:** Anonymous select for phone verification (LIMITED FIELDS ONLY)
4. **Service Role:** Full access for triggers and admin operations

### **Orders Table - MUST SUPPORT:**
1. **Consumer Access:** `consumer_phone` matches authenticated consumer
2. **Supplier Access:** `auth.uid()` matches supplier in order_items
3. **Shipping Access:** Via shipping company session settings

### **Products Table - MUST SUPPORT:**
1. **Admin Access:** `auth.uid() = supplier_id` (suppliers/merchants)
2. **Public Read:** Anonymous users can view active products
3. **No Consumer Write:** Consumers cannot modify products

---

## 🎯 **EXACT AUTHENTICATION PATTERNS**

### **Pattern 1: Supabase Auth Users (Suppliers/Merchants)**
```sql
-- Standard pattern for admin users
auth.uid() IS NOT NULL AND auth.uid() = profiles.id
```

### **Pattern 2: Consumer Phone Auth**
```sql
-- Pattern for consumer access via phone
role = 'consumer' AND phone = [consumer_phone_from_session]
```

### **Pattern 3: Shipping Company Code Auth**
```sql
-- Pattern for shipping company access
current_setting('app.shipping_company_code', true) = shipping_companies.login_code
```

### **Pattern 4: Anonymous Access (LIMITED)**
```sql
-- Only for specific operations like product browsing
auth.uid() IS NULL AND [specific_conditions]
```

---

## 🔧 **REQUIRED POLICY STRUCTURE**

### **Profiles Table Policies:**
1. `admin_full_access` - Supabase auth users access their own profiles
2. `consumer_signup` - Anonymous insert for new consumers
3. `consumer_login_verification` - Anonymous select for phone/email check (LIMITED)
4. `service_role_access` - Service role full access

### **Orders Table Policies:**
1. `consumer_orders_access` - Consumers access their orders via phone
2. `supplier_orders_access` - Suppliers access orders with their products
3. `shipping_orders_access` - Shipping companies access assigned orders
4. `order_creation_access` - Allow order creation

### **Products Table Policies:**
1. `admin_products_access` - Suppliers/merchants manage their products
2. `public_products_read` - Anonymous users browse active products
3. `service_role_access` - Service role full access

---

## 🚀 **NEXT STEPS**

1. **Create Perfect RLS Policy Matrix** - Map exact conditions for each table/role
2. **Design Non-Conflicting Policies** - Ensure no recursion or conflicts
3. **Test All Scenarios** - Verify each user type works perfectly
4. **Deploy Final Solution** - Apply the perfect policies

**CRITICAL:** The current policies are causing production issues. We need to fix this immediately with a comprehensive, non-conflicting solution.
