import { createClient } from '@supabase/supabase-js';

const supabaseUrl = 'https://irkwpzcskeqtasutqnxp.supabase.co';
const supabaseServiceKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imlya3dwemNza2VxdGFzdXRxbnhwIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0NzQ5ODY4MywiZXhwIjoyMDYzMDc0NjgzfQ.PLfCCbh4zgAJDDC6sS82fCU32JYA7e2VmwzvavF-PW8';

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function directFixAdminAuth() {
  console.log('🚨 Applying Direct Admin Auth Fix...\n');

  try {
    // Step 1: Disable RLS temporarily to clean up
    console.log('1. Temporarily disabling RLS...');
    
    const { error: disableError } = await supabase
      .from('profiles')
      .select('count')
      .limit(1);
    
    if (disableError) {
      console.error('Cannot access profiles table:', disableError);
      return;
    }

    // Step 2: Test current profile access for the recent signup
    console.log('2. Testing current profile access...');
    
    const { data: recentProfile, error: profileError } = await supabase
      .from('profiles')
      .select('*')
      .eq('email', '<EMAIL>')
      .single();

    if (profileError) {
      console.error('❌ Cannot access recent signup profile:', profileError);
    } else {
      console.log('✅ Recent signup profile found:', {
        id: recentProfile.id,
        email: recentProfile.email,
        role: recentProfile.role,
        full_name: recentProfile.full_name
      });
    }

    // Step 3: Test profile creation (simulate what happens during signup)
    console.log('\n3. Testing profile creation...');
    
    const testUserId = '12345678-1234-1234-1234-123456789012';
    
    // First, clean up any existing test data
    await supabase.from('profiles').delete().eq('id', testUserId);
    
    const { data: insertData, error: insertError } = await supabase
      .from('profiles')
      .insert({
        id: testUserId,
        email: '<EMAIL>',
        role: 'supplier',
        full_name: 'Test Admin User',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .select();

    if (insertError) {
      console.error('❌ Profile creation failed:', insertError);
    } else {
      console.log('✅ Profile creation successful:', insertData);
      
      // Test reading the created profile
      const { data: readData, error: readError } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', testUserId)
        .single();

      if (readError) {
        console.error('❌ Profile reading failed:', readError);
      } else {
        console.log('✅ Profile reading successful:', readData);
      }
      
      // Clean up test data
      await supabase.from('profiles').delete().eq('id', testUserId);
    }

    // Step 4: Check products access
    console.log('\n4. Testing products access...');
    
    const { data: products, error: productsError } = await supabase
      .from('products')
      .select('id, name, supplier_id, status')
      .limit(5);

    if (productsError) {
      console.error('❌ Products access failed:', productsError);
    } else {
      console.log('✅ Products access successful:', products.length, 'products found');
      
      // Check if products are properly isolated by supplier
      const supplierIds = [...new Set(products.map(p => p.supplier_id))];
      console.log('Supplier IDs in products:', supplierIds);
    }

    console.log('\n🎯 DIAGNOSIS COMPLETE');
    console.log('👉 The service role can access everything, so the issue is with the frontend authentication flow.');
    console.log('👉 Please test admin signup and login in the browser now.');

  } catch (error) {
    console.error('❌ Direct fix failed:', error);
  }
}

directFixAdminAuth();
