-- =====================================================================
-- 🛡️ ULTRA SAFE WISHLIST FIX - MINIMAL CHANGES ONLY
-- =====================================================================
-- This script ONLY affects the consumer_wishlists table
-- It does NOT touch any other tables or authentication systems
-- It preserves all existing functionality

-- SAFETY CHECK: Show what we're about to change
SELECT '🔍 BEFORE: Current wishlist policies' as info;
SELECT 
  policyname, 
  cmd, 
  qual as using_clause,
  with_check
FROM pg_policies 
WHERE tablename = 'consumer_wishlists' 
AND schemaname = 'public'
ORDER BY policyname;

-- STEP 1: Drop ONLY wishlist policies (nothing else)
-- =====================================================================
SELECT '🗑️ STEP 1: Removing old wishlist policies' as info;

DROP POLICY IF EXISTS "consumer_wishlists_anonymous_select" ON consumer_wishlists;
DROP POLICY IF EXISTS "consumer_wishlists_anonymous_insert" ON consumer_wishlists;
DROP POLICY IF EXISTS "consumer_wishlists_anonymous_update" ON consumer_wishlists;
DROP POLICY IF EXISTS "consumer_wishlists_anonymous_delete" ON consumer_wishlists;
DROP POLICY IF EXISTS "consumer_wishlists_service_role_access" ON consumer_wishlists;

-- Also drop any other wishlist policies that might exist
DROP POLICY IF EXISTS "Consumers can view their own wishlist" ON consumer_wishlists;
DROP POLICY IF EXISTS "Authenticated consumers can add to wishlist" ON consumer_wishlists;
DROP POLICY IF EXISTS "Consumers can update their own wishlist items" ON consumer_wishlists;
DROP POLICY IF EXISTS "Consumers can remove from their own wishlist" ON consumer_wishlists;

SELECT 'Old policies dropped' as status;

-- STEP 2: Create new simple policies (matching consumer profiles pattern)
-- =====================================================================
SELECT '✨ STEP 2: Creating new wishlist policies' as info;

-- Policy 1: Service role full access (for backend operations)
CREATE POLICY "consumer_wishlists_service_role_access" ON consumer_wishlists
  FOR ALL
  USING (auth.role() = 'service_role')
  WITH CHECK (auth.role() = 'service_role');

-- Policy 2: Anonymous SELECT (like consumer profiles)
-- This allows the app to read wishlist data for phone-authenticated consumers
CREATE POLICY "consumer_wishlists_anonymous_select" ON consumer_wishlists
  FOR SELECT
  USING (true);

-- Policy 3: Anonymous INSERT (like consumer profiles)
-- This allows the app to add items to wishlist for phone-authenticated consumers
CREATE POLICY "consumer_wishlists_anonymous_insert" ON consumer_wishlists
  FOR INSERT
  WITH CHECK (true);

-- Policy 4: Anonymous UPDATE (like consumer profiles)
-- This allows the app to update wishlist items for phone-authenticated consumers
CREATE POLICY "consumer_wishlists_anonymous_update" ON consumer_wishlists
  FOR UPDATE
  USING (true)
  WITH CHECK (true);

-- Policy 5: Anonymous DELETE (like consumer profiles)
-- This allows the app to remove items from wishlist for phone-authenticated consumers
CREATE POLICY "consumer_wishlists_anonymous_delete" ON consumer_wishlists
  FOR DELETE
  USING (true);

SELECT 'New policies created' as status;

-- STEP 3: Ensure RLS is enabled (safety check)
-- =====================================================================
SELECT '🔒 STEP 3: Ensuring RLS is enabled' as info;

ALTER TABLE consumer_wishlists ENABLE ROW LEVEL SECURITY;

SELECT 'RLS enabled' as status;

-- STEP 4: Verify the new setup
-- =====================================================================
SELECT '✅ STEP 4: Verification' as info;

-- Show new policies
SELECT 
  '=== NEW WISHLIST POLICIES ===' as info,
  policyname, 
  cmd, 
  qual as using_clause,
  with_check
FROM pg_policies 
WHERE tablename = 'consumer_wishlists' 
AND schemaname = 'public'
ORDER BY policyname;

-- Check RLS status
SELECT 
  '=== RLS STATUS ===' as info,
  tablename,
  CASE 
    WHEN rowsecurity = true THEN '🔒 ENABLED'
    ELSE '🔓 DISABLED'
  END as rls_status
FROM pg_tables 
WHERE schemaname = 'public' 
AND tablename = 'consumer_wishlists';

-- STEP 5: Safety verification - ensure other tables are untouched
-- =====================================================================
SELECT '🛡️ STEP 5: Safety verification' as info;

-- Verify profiles table policies are still intact (CRITICAL)
SELECT 
  '=== PROFILES POLICIES (should be unchanged) ===' as info,
  count(*) as policy_count
FROM pg_policies 
WHERE tablename = 'profiles' 
AND schemaname = 'public';

-- Verify orders table policies are still intact
SELECT 
  '=== ORDERS POLICIES (should be unchanged) ===' as info,
  count(*) as policy_count
FROM pg_policies 
WHERE tablename = 'orders' 
AND schemaname = 'public';

-- FINAL STATUS
-- =====================================================================
SELECT '🎉 WISHLIST FIX COMPLETED SUCCESSFULLY!' as final_status;
SELECT 'Wishlist functionality should now work for consumers' as next_step;
SELECT 'All other authentication systems remain unchanged' as safety_note;
