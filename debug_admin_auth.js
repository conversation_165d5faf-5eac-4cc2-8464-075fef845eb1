import { createClient } from '@supabase/supabase-js';

const supabaseUrl = 'https://irkwpzcskeqtasutqnxp.supabase.co';
const supabaseServiceKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imlya3dwemNza2VxdGFzdXRxbnhwIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0NzQ5ODY4MywiZXhwIjoyMDYzMDc0NjgzfQ.PLfCCbh4zgAJDDC6sS82fCU32JYA7e2VmwzvavF-PW8';

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function debugAdminAuth() {
  console.log('🔍 Debugging Admin Authentication Issues...\n');

  try {
    // 1. Apply the emergency fix
    console.log('1. Applying emergency admin auth fix...');

    const fs = await import('fs');
    const fixSQL = fs.readFileSync('EMERGENCY_ADMIN_AUTH_FIX.sql', 'utf8');

    // Split SQL into individual statements and execute them
    const statements = fixSQL.split(';').filter(stmt => stmt.trim() && !stmt.trim().startsWith('--'));

    for (const statement of statements) {
      if (statement.trim()) {
        try {
          const { error } = await supabase.rpc('exec_sql', { sql: statement.trim() });
          if (error) {
            console.error('SQL Error:', error);
          }
        } catch (err) {
          console.error('Statement error:', err);
        }
      }
    }

    // 2. Check recent profiles
    console.log('\n2. Checking recent profiles:');
    const { data: profiles, error: profilesError } = await supabase
      .from('profiles')
      .select('id, email, role, full_name, created_at')
      .order('created_at', { ascending: false })
      .limit(5);

    if (profilesError) {
      console.error('Error fetching profiles:', profilesError);
    } else {
      console.log('Recent profiles:', profiles);
    }

    // 3. Test profile creation (simulate signup)
    console.log('\n3. Testing profile creation...');
    const testUserId = '12345678-1234-1234-1234-123456789012';
    
    const { data: insertData, error: insertError } = await supabase
      .from('profiles')
      .insert({
        id: testUserId,
        email: '<EMAIL>',
        role: 'supplier',
        full_name: 'Test User',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      });

    if (insertError) {
      console.error('Profile creation failed:', insertError);
    } else {
      console.log('Profile creation successful:', insertData);
      
      // Clean up test data
      await supabase.from('profiles').delete().eq('id', testUserId);
    }

  } catch (error) {
    console.error('Debug error:', error);
  }
}

debugAdminAuth();
