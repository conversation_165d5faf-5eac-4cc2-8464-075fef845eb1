#!/usr/bin/env node

/**
 * AROUZ MARKET - Emergency Authentication Fix Application
 * 
 * This script applies the emergency authentication isolation fix to production
 * using the service role key for administrative access.
 */

import { createClient } from '@supabase/supabase-js';
import fs from 'fs';

// Production Supabase credentials
const SUPABASE_URL = 'https://irkwpzcskeqtasutqnxp.supabase.co';
const SUPABASE_SERVICE_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imlya3dwemNza2VxdGFzdXRxbnhwIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0NzQ5ODY4MywiZXhwIjoyMDYzMDc0NjgzfQ.PLfCCbh4zgAJDDC6sS82fCU32JYA7e2VmwzvavF-PW8';

const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_KEY);

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  bright: '\x1b[1m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logSection(title) {
  console.log('\n' + '='.repeat(80));
  log(`🚨 ${title}`, 'bright');
  console.log('='.repeat(80));
}

async function applyEmergencyFix() {
  try {
    logSection('AROUZ MARKET - EMERGENCY AUTHENTICATION FIX');
    log('🚨 Applying critical authentication isolation fix...', 'red');
    log(`📡 Target: ${SUPABASE_URL}`, 'cyan');

    // 1. Test connection with service role
    logSection('1. CONNECTION VERIFICATION');
    const { data: connectionTest, error: connectionError } = await supabase
      .from('profiles')
      .select('count')
      .limit(1);
    
    if (connectionError) {
      log(`❌ Service role connection failed: ${connectionError.message}`, 'red');
      return;
    }
    log('✅ Service role connection successful', 'green');

    // 2. Read the migration file
    logSection('2. LOADING MIGRATION');
    const migrationPath = 'supabase/migrations/20250709000000_emergency_auth_isolation_fix.sql';
    
    if (!fs.existsSync(migrationPath)) {
      log(`❌ Migration file not found: ${migrationPath}`, 'red');
      return;
    }
    
    const migrationSQL = fs.readFileSync(migrationPath, 'utf8');
    log(`📄 Migration loaded: ${migrationSQL.length} characters`, 'blue');

    // 3. Apply the migration
    logSection('3. APPLYING EMERGENCY FIX');
    log('⚠️  Executing SQL migration...', 'yellow');
    
    const { data: migrationResult, error: migrationError } = await supabase.rpc('exec_sql', {
      sql: migrationSQL
    });

    if (migrationError) {
      log(`❌ Migration failed: ${migrationError.message}`, 'red');
      
      // Try alternative method - split into smaller chunks
      log('🔄 Trying alternative application method...', 'yellow');
      
      // Split the SQL into individual statements
      const statements = migrationSQL
        .split(';')
        .map(stmt => stmt.trim())
        .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'));
      
      log(`📝 Executing ${statements.length} SQL statements...`, 'blue');
      
      let successCount = 0;
      let errorCount = 0;
      
      for (let i = 0; i < statements.length; i++) {
        const statement = statements[i];
        if (statement.length === 0) continue;
        
        try {
          const { error: stmtError } = await supabase.rpc('exec_sql', {
            sql: statement + ';'
          });
          
          if (stmtError) {
            log(`⚠️  Statement ${i + 1} warning: ${stmtError.message}`, 'yellow');
            errorCount++;
          } else {
            successCount++;
          }
        } catch (error) {
          log(`❌ Statement ${i + 1} failed: ${error.message}`, 'red');
          errorCount++;
        }
      }
      
      log(`📊 Results: ${successCount} successful, ${errorCount} errors`, 'blue');
      
    } else {
      log('✅ Migration applied successfully', 'green');
    }

    // 4. Verify the fix
    logSection('4. VERIFICATION');
    
    // Test anonymous access to profiles
    const anonClient = createClient(SUPABASE_URL, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imlya3dwemNza2VxdGFzdXRxbnhwIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDc0OTg2ODMsImV4cCI6MjA2MzA3NDY4M30.bUv9zxx9q_sVUwv_-G6WbHsA53-SU5tCCWbOS_5Xp94');
    
    const { data: anonTest, error: anonError } = await anonClient
      .from('profiles')
      .select('id, role')
      .limit(3);
    
    if (anonError) {
      log(`⚠️  Anonymous access test: ${anonError.message}`, 'yellow');
    } else {
      log(`✅ Anonymous access working: Found ${anonTest?.length || 0} profiles`, 'green');
    }

    // Test service role access
    const { data: serviceTest, error: serviceError } = await supabase
      .from('profiles')
      .select('id, role, email')
      .limit(5);
    
    if (serviceError) {
      log(`❌ Service role access failed: ${serviceError.message}`, 'red');
    } else {
      log(`✅ Service role access working: Found ${serviceTest?.length || 0} profiles`, 'green');
      if (serviceTest && serviceTest.length > 0) {
        const roleCounts = serviceTest.reduce((acc, p) => {
          acc[p.role] = (acc[p.role] || 0) + 1;
          return acc;
        }, {});
        log(`📊 Role distribution: ${JSON.stringify(roleCounts)}`, 'cyan');
      }
    }

    logSection('EMERGENCY FIX COMPLETE');
    log('🎯 Authentication isolation fix has been applied!', 'bright');
    log('🔄 Please test the admin panels and consumer authentication now.', 'green');
    
  } catch (error) {
    log(`💥 Emergency fix failed: ${error.message}`, 'red');
    console.error(error);
  }
}

// Run the emergency fix
applyEmergencyFix();
