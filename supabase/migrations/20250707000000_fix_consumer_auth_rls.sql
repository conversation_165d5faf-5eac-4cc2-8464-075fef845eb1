-- Migration: Fix Consumer Authentication RLS Policies
-- Date: July 7, 2025
-- Issue: Consumer authentication failing due to RLS policies blocking anonymous access

-- Drop existing consumer-related policies that might be conflicting
DROP POLICY IF EXISTS "profiles_consumer_select" ON profiles;
DROP POLICY IF EXISTS "profiles_consumer_update" ON profiles;
DROP POLICY IF EXISTS "profiles_consumer_insert" ON profiles;

-- Create new permissive policies for consumer authentication
-- These policies allow anonymous access for consumer operations since consumers don't have auth.users records

-- Allow anonymous SELECT for consumer profiles (needed for login check)
CREATE POLICY "profiles_consumer_anonymous_select" ON profiles
  FOR SELECT 
  USING (role = 'consumer');

-- Allow anonymous INSERT for consumer profiles (needed for signup)
CREATE POLICY "profiles_consumer_anonymous_insert" ON profiles
  FOR INSERT 
  WITH CHECK (role = 'consumer');

-- Allow anonymous UPDATE for consumer profiles (needed for profile updates)
CREATE POLICY "profiles_consumer_anonymous_update" ON profiles
  FOR UPDATE 
  USING (role = 'consumer')
  WITH CHECK (role = 'consumer');

-- Ensure other roles still require authentication
-- Keep existing authenticated policies for non-consumer roles
CREATE POLICY "profiles_authenticated_select_non_consumer" ON profiles
  FOR SELECT 
  USING (auth.uid() = id AND role != 'consumer');

CREATE POLICY "profiles_authenticated_update_non_consumer" ON profiles
  FOR UPDATE 
  USING (auth.uid() = id AND role != 'consumer')
  WITH CHECK (auth.uid() = id AND role != 'consumer');

CREATE POLICY "profiles_authenticated_insert_non_consumer" ON profiles
  FOR INSERT 
  WITH CHECK (auth.uid() = id AND role != 'consumer');

-- Ensure service role can still manage all profiles
CREATE POLICY "profiles_service_role_all_access" ON profiles
  FOR ALL 
  USING (auth.role() = 'service_role')
  WITH CHECK (auth.role() = 'service_role');

-- Add comment for documentation
COMMENT ON POLICY "profiles_consumer_anonymous_select" ON profiles IS 
'Allows anonymous access to consumer profiles for phone-based authentication';

COMMENT ON POLICY "profiles_consumer_anonymous_insert" ON profiles IS 
'Allows anonymous creation of consumer profiles during phone-based signup';

COMMENT ON POLICY "profiles_consumer_anonymous_update" ON profiles IS 
'Allows anonymous updates to consumer profiles for phone-based authentication';
