-- =====================================================
-- AROUZ MARKET - Add Exhaust Gas Recirculation Category Migration
-- Generated: 2025-07-15T12:34:31.167Z
-- Purpose: Add Exhaust gas recirculation category with all subcategories (100% accuracy)
-- Strategy: Follow exact same pattern as previous successful migrations
-- =====================================================

-- Insert Exhaust gas recirculation category
INSERT INTO categories (id, name, display_name, description, id_prefix, sort_order) VALUES
  ('exhaust-gas-recirculation', 'Exhaust gas recirculation', 'Exhaust gas recirculation', 'EGR valves, coolers, gaskets, and related exhaust gas recirculation components', 'PROD', 11)
ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  display_name = EXCLUDED.display_name,
  description = EXCLUDED.description,
  id_prefix = EXCLUDED.id_prefix,
  sort_order = EXCLUDED.sort_order;

-- Insert Exhaust gas recirculation subcategories (3 total)
INSERT INTO subcategories (id, name, display_name, category_id, sort_order) VALUES
  ('exhaust-gas-recirculation-egr-valve', 'EGR valve', 'EGR valve', 'exhaust-gas-recirculation', 1),
  ('exhaust-gas-recirculation-egr-cooler', 'EGR cooler', 'EGR cooler', 'exhaust-gas-recirculation', 2),
  ('exhaust-gas-recirculation-egr-valve-gasket', 'Egr valve gasket', 'Egr valve gasket', 'exhaust-gas-recirculation', 3)
ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  display_name = EXCLUDED.display_name,
  category_id = EXCLUDED.category_id,
  sort_order = EXCLUDED.sort_order;

-- Verification queries
SELECT 'Exhaust gas recirculation category migration completed successfully!' as status;
SELECT COUNT(*) as total_categories FROM categories;
SELECT COUNT(*) as total_subcategories FROM subcategories;
SELECT COUNT(*) as egr_subcategories FROM subcategories WHERE category_id = 'exhaust-gas-recirculation';

-- Show the new exhaust gas recirculation category and its subcategories
SELECT 'EXHAUST GAS RECIRCULATION CATEGORY:' as section, id, name, display_name, description FROM categories WHERE id = 'exhaust-gas-recirculation';
SELECT 'EXHAUST GAS RECIRCULATION SUBCATEGORIES:' as section, id, name, display_name, sort_order FROM subcategories WHERE category_id = 'exhaust-gas-recirculation' ORDER BY sort_order;