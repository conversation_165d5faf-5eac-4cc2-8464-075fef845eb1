-- =====================================================
-- AROUZ MARKET - Add Damping Category Migration
-- Generated: 2025-07-15T12:09:35.298Z
-- Purpose: Add Damping category with all subcategories (100% accuracy)
-- Strategy: Follow exact same pattern as previous successful migrations
-- =====================================================

-- Insert Damping category
INSERT INTO categories (id, name, display_name, description, id_prefix, sort_order) VALUES
  ('damping', 'Damping', 'Damping', 'Shock absorbers, coil springs, leaf springs, and related components', 'PROD', 10)
ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  display_name = EXCLUDED.display_name,
  description = EXCLUDED.description,
  id_prefix = EXCLUDED.id_prefix,
  sort_order = EXCLUDED.sort_order;

-- Insert Damping subcategories (20 total)
INSERT INTO subcategories (id, name, display_name, category_id, sort_order) VALUES
  ('shock-absorber', 'Shock absorber', 'Shock absorber', 'damping', 1),
  ('coil-spring', 'Coil spring', 'Coil spring', 'damping', 2),
  ('suspension-kit-coil-springs', 'Suspension kit, coil springs', 'Suspension kit, coil springs', 'damping', 3),
  ('spring-cap', 'Spring cap', 'Spring cap', 'damping', 4),
  ('suspension-kit-shock-absorber-and-coil-spring', 'Suspension kit, shock absorber and coil spring', 'Suspension kit, shock absorber and coil spring', 'damping', 5),
  ('coil-spring-shock-absorber-tool', 'Coil spring / shock absorber tool', 'Coil spring / shock absorber tool', 'damping', 6),
  ('air-suspension-boot', 'Air suspension boot', 'Air suspension boot', 'damping', 7),
  ('air-suspension', 'Air suspension', 'Air suspension', 'damping', 8),
  ('repair-kit-wheel-suspension', 'Repair kit, wheel suspension', 'Repair kit, wheel suspension', 'damping', 9),
  ('air-suspension-compressor', 'Air suspension compressor', 'Air suspension compressor', 'damping', 10),
  ('top-strut-mount-and-bearing', 'Top strut mount and bearing', 'Top strut mount and bearing', 'damping', 11),
  ('shock-absorber-mount', 'Shock absorber mount', 'Shock absorber mount', 'damping', 12),
  ('damping-leaf-spring', 'Leaf spring', 'Leaf spring', 'damping', 13),
  ('shock-absorber-dust-cover-and-bump-stops', 'Shock absorber dust cover and bump stops', 'Shock absorber dust cover and bump stops', 'damping', 14),
  ('camber-bolts', 'Camber bolts', 'Camber bolts', 'damping', 15),
  ('suspension-strut', 'Suspension Strut', 'Suspension Strut', 'damping', 16),
  ('suspension-spheres', 'Suspension spheres', 'Suspension spheres', 'damping', 17),
  ('hydraulic-oil', 'Hydraulic oil', 'Hydraulic oil', 'damping', 18),
  ('shock-absorber-mounting-brackets', 'Shock absorber mounting brackets', 'Shock absorber mounting brackets', 'damping', 19),
  ('relay-leveling-control', 'Relay, leveling control', 'Relay, leveling control', 'damping', 20)
ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  display_name = EXCLUDED.display_name,
  category_id = EXCLUDED.category_id,
  sort_order = EXCLUDED.sort_order;

-- Verification queries
SELECT 'Damping category migration completed successfully!' as status;
SELECT COUNT(*) as total_categories FROM categories;
SELECT COUNT(*) as total_subcategories FROM subcategories;
SELECT COUNT(*) as damping_subcategories FROM subcategories WHERE category_id = 'damping';

-- Show the new damping category and its subcategories
SELECT 'DAMPING CATEGORY:' as section, id, name, display_name, description FROM categories WHERE id = 'damping';
SELECT 'DAMPING SUBCATEGORIES:' as section, id, name, display_name, sort_order FROM subcategories WHERE category_id = 'damping' ORDER BY sort_order;