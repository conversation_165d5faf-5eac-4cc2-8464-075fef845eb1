import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Create a Supabase client with service role key
    const supabaseAdmin = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? '',
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    )

    console.log('🚨 FIXING RLS POLICIES...')

    // Step 1: Get current policies
    const { data: policies, error: policiesError } = await supabaseAdmin
      .rpc('sql', {
        query: `
          SELECT policyname, cmd, qual, with_check 
          FROM pg_policies 
          WHERE tablename = 'profiles' 
          AND schemaname = 'public'
          ORDER BY policyname;
        `
      })

    if (policiesError) {
      console.error('Error getting policies:', policiesError)
      return new Response(
        JSON.stringify({ error: 'Failed to get policies', details: policiesError }),
        { 
          status: 500,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      )
    }

    console.log('Current policies:', policies)

    // Step 2: Disable RLS
    const { error: disableError } = await supabaseAdmin
      .rpc('sql', {
        query: 'ALTER TABLE profiles DISABLE ROW LEVEL SECURITY;'
      })

    if (disableError) {
      console.error('Error disabling RLS:', disableError)
    } else {
      console.log('✅ RLS disabled')
    }

    // Step 3: Drop all policies
    if (policies && Array.isArray(policies)) {
      for (const policy of policies) {
        const { error: dropError } = await supabaseAdmin
          .rpc('sql', {
            query: `DROP POLICY IF EXISTS "${policy.policyname}" ON profiles;`
          })

        if (dropError) {
          console.error(`Error dropping ${policy.policyname}:`, dropError)
        } else {
          console.log(`✅ Dropped: ${policy.policyname}`)
        }
      }
    }

    // Step 4: Re-enable RLS
    const { error: enableError } = await supabaseAdmin
      .rpc('sql', {
        query: 'ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;'
      })

    if (enableError) {
      console.error('Error enabling RLS:', enableError)
    } else {
      console.log('✅ RLS re-enabled')
    }

    // Step 5: Create new simple policies
    const newPolicies = [
      {
        name: 'service_role_access',
        sql: `CREATE POLICY "service_role_access" ON profiles
              FOR ALL
              USING (auth.role() = 'service_role')
              WITH CHECK (auth.role() = 'service_role');`
      },
      {
        name: 'own_profile_access',
        sql: `CREATE POLICY "own_profile_access" ON profiles
              FOR ALL
              USING (auth.uid() = id)
              WITH CHECK (auth.uid() = id);`
      },
      {
        name: 'consumer_access',
        sql: `CREATE POLICY "consumer_access" ON profiles
              FOR ALL
              USING (role = 'consumer')
              WITH CHECK (role = 'consumer');`
      }
    ]

    for (const policy of newPolicies) {
      const { error: createError } = await supabaseAdmin
        .rpc('sql', {
          query: policy.sql
        })

      if (createError) {
        console.error(`Error creating ${policy.name}:`, createError)
      } else {
        console.log(`✅ Created: ${policy.name}`)
      }
    }

    // Step 6: Test the fix
    const { data: testData, error: testError } = await supabaseAdmin
      .from('profiles')
      .select('id, email, role')
      .limit(3)

    if (testError) {
      console.error('Test failed:', testError)
    } else {
      console.log('✅ Test passed:', testData.length, 'profiles accessible')
    }

    return new Response(
      JSON.stringify({ 
        success: true, 
        message: 'RLS policies fixed successfully',
        policiesDropped: policies?.length || 0,
        policiesCreated: newPolicies.length,
        testResult: testData?.length || 0
      }),
      { 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      }
    )

  } catch (error) {
    console.error('Function error:', error)
    return new Response(
      JSON.stringify({ error: 'Function failed', details: error.message }),
      { 
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      }
    )
  }
})
