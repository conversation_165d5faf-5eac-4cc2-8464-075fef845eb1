<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AROUZ MARKET - Authentication Investigation</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background: #d4edda; border-color: #c3e6cb; color: #155724; }
        .error { background: #f8d7da; border-color: #f5c6cb; color: #721c24; }
        .warning { background: #fff3cd; border-color: #ffeaa7; color: #856404; }
        .info { background: #d1ecf1; border-color: #bee5eb; color: #0c5460; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 4px; overflow-x: auto; }
        button { padding: 10px 20px; margin: 5px; border: none; border-radius: 4px; cursor: pointer; }
        .btn-primary { background: #007bff; color: white; }
        .btn-danger { background: #dc3545; color: white; }
        .btn-success { background: #28a745; color: white; }
        .loading { opacity: 0.6; pointer-events: none; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 AROUZ MARKET - Critical Authentication Investigation</h1>
        <p><strong>Project:</strong> irkwpzcskeqtasutqnxp.supabase.co</p>
        
        <div class="section info">
            <h3>🎯 Investigation Status</h3>
            <div id="status">Initializing...</div>
        </div>

        <div class="section">
            <h3>🔧 Controls</h3>
            <button class="btn-primary" onclick="runFullInvestigation()">🔍 Run Full Investigation</button>
            <button class="btn-success" onclick="testAnonymousAccess()">🔓 Test Anonymous Access</button>
            <button class="btn-danger" onclick="testAuthenticatedAccess()">🔐 Test Authenticated Access</button>
        </div>

        <div id="results"></div>
    </div>

    <script type="module">
        import { createClient } from 'https://cdn.skypack.dev/@supabase/supabase-js@2';

        // Production Supabase configuration
        const SUPABASE_URL = 'https://irkwpzcskeqtasutqnxp.supabase.co';
        const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imlya3dwemNza2VxdGFzdXRxbnhwIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDc0OTg2ODMsImV4cCI6MjA2MzA3NDY4M30.bUv9zxx9q_sVUwv_-G6WbHsA53-SU5tCCWbOS_5Xp94';

        // Create Supabase client
        const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

        // Global functions for buttons
        window.runFullInvestigation = runFullInvestigation;
        window.testAnonymousAccess = testAnonymousAccess;
        window.testAuthenticatedAccess = testAuthenticatedAccess;

        function addResult(title, content, type = 'info') {
            const resultsDiv = document.getElementById('results');
            const section = document.createElement('div');
            section.className = `section ${type}`;
            section.innerHTML = `
                <h3>${title}</h3>
                <pre>${typeof content === 'object' ? JSON.stringify(content, null, 2) : content}</pre>
            `;
            resultsDiv.appendChild(section);
        }

        function updateStatus(message) {
            document.getElementById('status').textContent = message;
        }

        async function runFullInvestigation() {
            document.getElementById('results').innerHTML = '';
            updateStatus('🔍 Running comprehensive investigation...');

            try {
                // 1. Test basic connection
                updateStatus('📡 Testing connection...');
                const { data: connectionTest, error: connectionError } = await supabase
                    .from('profiles')
                    .select('count')
                    .limit(1);

                if (connectionError) {
                    addResult('❌ Connection Test', `Failed: ${connectionError.message}`, 'error');
                    return;
                }
                addResult('✅ Connection Test', 'Successfully connected to Supabase', 'success');

                // 2. Test profiles access
                updateStatus('👥 Testing profiles access...');
                const { data: profilesData, error: profilesError } = await supabase
                    .from('profiles')
                    .select('id, email, role, created_at')
                    .limit(10);

                if (profilesError) {
                    addResult('🔒 Profiles Access', `Blocked: ${profilesError.message}`, 'success');
                } else {
                    addResult('🚨 CRITICAL: Profiles Access', `Anonymous access allowed! Found ${profilesData?.length || 0} profiles`, 'error');
                    if (profilesData && profilesData.length > 0) {
                        const roleCounts = profilesData.reduce((acc, p) => {
                            acc[p.role] = (acc[p.role] || 0) + 1;
                            return acc;
                        }, {});
                        addResult('📊 Exposed Data', `Role distribution: ${JSON.stringify(roleCounts)}`, 'warning');
                    }
                }

                // 3. Test orders access
                updateStatus('📦 Testing orders access...');
                const { data: ordersData, error: ordersError } = await supabase
                    .from('orders')
                    .select('id, consumer_phone, supplier_id, created_at')
                    .limit(5);

                if (ordersError) {
                    addResult('🔒 Orders Access', `Blocked: ${ordersError.message}`, 'success');
                } else {
                    addResult('🚨 CRITICAL: Orders Access', `Anonymous access allowed! Found ${ordersData?.length || 0} orders`, 'error');
                }

                // 4. Test products access
                updateStatus('🛍️ Testing products access...');
                const { data: productsData, error: productsError } = await supabase
                    .from('products')
                    .select('id, name, supplier_id, created_at')
                    .limit(5);

                if (productsError) {
                    addResult('🔒 Products Access', `Blocked: ${productsError.message}`, 'success');
                } else {
                    addResult('🚨 CRITICAL: Products Access', `Anonymous access allowed! Found ${productsData?.length || 0} products`, 'error');
                }

                // 5. Test shipments access
                updateStatus('🚚 Testing shipments access...');
                const { data: shipmentsData, error: shipmentsError } = await supabase
                    .from('shipments')
                    .select('id, order_id, shipping_company_id, created_at')
                    .limit(5);

                if (shipmentsError) {
                    addResult('🔒 Shipments Access', `Blocked: ${shipmentsError.message}`, 'success');
                } else {
                    addResult('🚨 CRITICAL: Shipments Access', `Anonymous access allowed! Found ${shipmentsData?.length || 0} shipments`, 'error');
                }

                updateStatus('✅ Investigation complete!');

            } catch (error) {
                addResult('💥 Investigation Error', error.message, 'error');
                updateStatus('❌ Investigation failed');
            }
        }

        async function testAnonymousAccess() {
            updateStatus('🔓 Testing anonymous access patterns...');
            // This function tests what anonymous users can access
            await runFullInvestigation();
        }

        async function testAuthenticatedAccess() {
            updateStatus('🔐 Testing authenticated access...');
            addResult('ℹ️ Authentication Test', 'This would require implementing a login flow. For now, check the anonymous access results above.', 'info');
        }

        // Initialize
        updateStatus('✅ Ready for investigation');
        console.log('🔍 AROUZ MARKET Authentication Investigation Tool Ready');
        console.log('📡 Connected to:', SUPABASE_URL);
    </script>
</body>
</html>
