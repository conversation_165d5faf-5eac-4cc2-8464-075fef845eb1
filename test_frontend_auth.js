import { createClient } from '@supabase/supabase-js';

const supabaseUrl = 'https://irkwpzcskeqtasutqnxp.supabase.co';
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imlya3dwemNza2VxdGFzdXRxbnhwIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDc0OTg2ODMsImV4cCI6MjA2MzA3NDY4M30.bUv9zxx9q_sVUwv_-G6WbHsA53-SU5tCCWbOS_5Xp94';

// This simulates the frontend client
const supabase = createClient(supabaseUrl, supabaseAnonKey);

async function testFrontendAuth() {
  console.log('🔍 Testing Frontend Authentication Flow...\n');

  try {
    // Step 1: Test login with the existing user
    console.log('1. Testing login with existing user...');
    
    const { data: loginData, error: loginError } = await supabase.auth.signInWithPassword({
      email: '<EMAIL>',
      password: 'Ascfigh123@'  // This is the password from the signup form
    });

    if (loginError) {
      console.error('❌ Login failed:', loginError);
      return;
    }

    console.log('✅ Login successful:', {
      user_id: loginData.user?.id,
      email: loginData.user?.email,
      role: loginData.user?.role
    });

    // Step 2: Test profile access with authenticated user
    console.log('\n2. Testing profile access with authenticated user...');
    
    const { data: profileData, error: profileError } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', loginData.user.id)
      .single();

    if (profileError) {
      console.error('❌ Profile access failed:', profileError);
      console.log('This is the exact error the frontend is getting!');
    } else {
      console.log('✅ Profile access successful:', {
        id: profileData.id,
        email: profileData.email,
        role: profileData.role,
        full_name: profileData.full_name
      });
    }

    // Step 3: Test current session
    console.log('\n3. Testing current session...');
    
    const { data: sessionData, error: sessionError } = await supabase.auth.getSession();
    
    if (sessionError) {
      console.error('❌ Session error:', sessionError);
    } else {
      console.log('✅ Session active:', {
        user_id: sessionData.session?.user?.id,
        access_token_present: !!sessionData.session?.access_token,
        expires_at: sessionData.session?.expires_at
      });
    }

    // Step 4: Test products access (to check the supplier_id issue)
    console.log('\n4. Testing products access...');
    
    const { data: productsData, error: productsError } = await supabase
      .from('products')
      .select('id, name, supplier_account_id, status')
      .limit(3);

    if (productsError) {
      console.error('❌ Products access failed:', productsError);
    } else {
      console.log('✅ Products access successful:', productsData.length, 'products found');
      
      // Check if user can see their own products
      const userProducts = productsData.filter(p => p.supplier_account_id === loginData.user.id);
      console.log('User\'s own products:', userProducts.length);
    }

    // Step 5: Logout
    console.log('\n5. Logging out...');
    await supabase.auth.signOut();
    console.log('✅ Logout successful');

  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

testFrontendAuth();
