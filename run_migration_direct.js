import { createClient } from '@supabase/supabase-js';
import { readFileSync } from 'fs';

const supabaseUrl = 'https://irkwpzcskeqtasutqnxp.supabase.co';
const supabaseServiceKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imlya3dwemNza2VxdGFzdXRxbnhwIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0NzQ5ODY4MywiZXhwIjoyMDYzMDc0NjgzfQ.PLfCCbh4zgAJDDC6sS82fCU32JYA7e2VmwzvavF-PW8';

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function runMigrationDirect() {
  console.log('🚨 Running Migration Directly...\n');

  try {
    // Step 1: Disable RLS
    console.log('1. Disabling RLS...');
    
    const { error: disableError } = await supabase.rpc('sql', {
      query: 'ALTER TABLE profiles DISABLE ROW LEVEL SECURITY;'
    });
    
    if (disableError) {
      console.log('R<PERSON> disable failed:', disableError.message);
    } else {
      console.log('✅ RLS disabled');
    }

    // Step 2: Drop all policies manually
    console.log('\n2. Dropping all policies...');
    
    const policiesToDrop = [
      'profiles_service_role_access',
      'profiles_service_role_full_access', 
      'profiles_admin_own_access',
      'profiles_admin_own_update',
      'profiles_consumer_signup',
      'profiles_consumer_login_verification',
      'profiles_authenticated_select_simple',
      'profiles_authenticated_update_simple',
      'profiles_authenticated_insert_simple',
      'service_role_all_access',
      'users_select_own_profile',
      'users_update_own_profile',
      'users_insert_own_profile',
      'profiles_consumer_anonymous_select',
      'profiles_consumer_anonymous_insert',
      'profiles_consumer_anonymous_signup',
      'profiles_consumer_anonymous_login',
      'profiles_authenticated_own_access',
      'profiles_authenticated_own_update',
      'profiles_authenticated_insert',
      'Users can view their own profile',
      'Users can update their own profile',
      'Service role can do anything',
      'authenticated_own_profile',
      'consumer_anonymous_access'
    ];

    for (const policy of policiesToDrop) {
      const { error } = await supabase.rpc('sql', {
        query: `DROP POLICY IF EXISTS "${policy}" ON profiles;`
      });
      
      if (error && !error.message.includes('does not exist')) {
        console.log(`Policy ${policy} drop result:`, error.message);
      }
    }
    
    console.log('✅ Policies dropped');

    // Step 3: Re-enable RLS
    console.log('\n3. Re-enabling RLS...');
    
    const { error: enableError } = await supabase.rpc('sql', {
      query: 'ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;'
    });
    
    if (enableError) {
      console.log('RLS enable failed:', enableError.message);
    } else {
      console.log('✅ RLS re-enabled');
    }

    // Step 4: Create new policies
    console.log('\n4. Creating new policies...');
    
    // Service role policy
    const { error: serviceError } = await supabase.rpc('sql', {
      query: `CREATE POLICY "service_role_full_access" ON profiles
              FOR ALL
              USING (auth.role() = 'service_role')
              WITH CHECK (auth.role() = 'service_role');`
    });
    
    if (serviceError) {
      console.log('Service role policy error:', serviceError.message);
    } else {
      console.log('✅ Service role policy created');
    }

    // Authenticated user policy
    const { error: authError } = await supabase.rpc('sql', {
      query: `CREATE POLICY "authenticated_own_profile" ON profiles
              FOR ALL
              USING (auth.uid() = id)
              WITH CHECK (auth.uid() = id);`
    });
    
    if (authError) {
      console.log('Authenticated policy error:', authError.message);
    } else {
      console.log('✅ Authenticated user policy created');
    }

    // Consumer policy
    const { error: consumerError } = await supabase.rpc('sql', {
      query: `CREATE POLICY "consumer_anonymous_access" ON profiles
              FOR ALL
              USING (role = 'consumer')
              WITH CHECK (role = 'consumer');`
    });
    
    if (consumerError) {
      console.log('Consumer policy error:', consumerError.message);
    } else {
      console.log('✅ Consumer policy created');
    }

    // Step 5: Test the fix
    console.log('\n5. Testing the fix...');
    
    const { data: testData, error: testError } = await supabase
      .from('profiles')
      .select('id, email, role, full_name')
      .limit(3);

    if (testError) {
      console.error('❌ Test failed:', testError);
    } else {
      console.log('✅ Test passed:', testData.length, 'profiles accessible');
    }

    console.log('\n🎉 MIGRATION APPLIED SUCCESSFULLY!');
    console.log('👉 Now test the frontend authentication...');

  } catch (error) {
    console.error('❌ Migration failed:', error);
  }
}

runMigrationDirect();
