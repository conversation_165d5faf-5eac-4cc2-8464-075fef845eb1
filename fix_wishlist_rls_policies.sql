-- Fix Wishlist RLS Policies to Use Consumer Phone Context
-- This migration updates the consumer_wishlists RLS policies to use the current_setting pattern
-- similar to how orders and other consumer features work.

-- 1. Drop existing wishlist policies
DROP POLICY IF EXISTS "Consumers can view their own wishlist" ON consumer_wishlists;
DROP POLICY IF EXISTS "Authenticated consumers can add to wishlist" ON consumer_wishlists;
DROP POLICY IF EXISTS "Consumers can update their own wishlist items" ON consumer_wishlists;
DROP POLICY IF EXISTS "Consumers can remove from their own wishlist" ON consumer_wishlists;

-- 2. Create new policies using current_setting pattern

-- Policy 1: Service Role Full Access (always needed)
CREATE POLICY "consumer_wishlists_service_role_access" ON consumer_wishlists
  FOR ALL
  USING (auth.role() = 'service_role')
  WITH CHECK (auth.role() = 'service_role');

-- Policy 2: Consumer Wishlist Access (via phone context)
CREATE POLICY "consumer_wishlists_consumer_access" ON consumer_wishlists
  FOR SELECT
  USING (
    consumer_phone = current_setting('app.consumer_phone', true)
  );

-- Policy 3: Consumer Wishlist Insert (via phone context)
CREATE POLICY "consumer_wishlists_consumer_insert" ON consumer_wishlists
  FOR INSERT
  WITH CHECK (
    consumer_phone = current_setting('app.consumer_phone', true)
  );

-- Policy 4: Consumer Wishlist Update (via phone context)
CREATE POLICY "consumer_wishlists_consumer_update" ON consumer_wishlists
  FOR UPDATE
  USING (
    consumer_phone = current_setting('app.consumer_phone', true)
  )
  WITH CHECK (
    consumer_phone = current_setting('app.consumer_phone', true)
  );

-- Policy 5: Consumer Wishlist Delete (via phone context)
CREATE POLICY "consumer_wishlists_consumer_delete" ON consumer_wishlists
  FOR DELETE
  USING (
    consumer_phone = current_setting('app.consumer_phone', true)
  );

-- 3. Ensure RLS is enabled
ALTER TABLE consumer_wishlists ENABLE ROW LEVEL SECURITY;

-- 4. Add comments for documentation
COMMENT ON POLICY "consumer_wishlists_service_role_access" ON consumer_wishlists IS 
'Service role full access for system operations';

COMMENT ON POLICY "consumer_wishlists_consumer_access" ON consumer_wishlists IS 
'Consumers can view their wishlist items using phone context from current_setting';

COMMENT ON POLICY "consumer_wishlists_consumer_insert" ON consumer_wishlists IS 
'Consumers can add items to their wishlist using phone context from current_setting';

COMMENT ON POLICY "consumer_wishlists_consumer_update" ON consumer_wishlists IS 
'Consumers can update their wishlist items using phone context from current_setting';

COMMENT ON POLICY "consumer_wishlists_consumer_delete" ON consumer_wishlists IS 
'Consumers can remove items from their wishlist using phone context from current_setting';

-- 5. Verify the policies are working
SELECT 
  schemaname, 
  tablename, 
  policyname, 
  permissive, 
  roles, 
  cmd, 
  qual, 
  with_check 
FROM pg_policies 
WHERE tablename = 'consumer_wishlists' 
ORDER BY policyname;
