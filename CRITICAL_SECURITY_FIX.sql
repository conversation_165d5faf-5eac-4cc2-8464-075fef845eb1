-- =====================================================================
-- AROUZ MARKET - CRITICAL SECURITY FIX
-- =====================================================================
-- URGENT: This fixes the remaining security vulnerabilities
-- The profiles table is still exposing data to anonymous users
-- =====================================================================

-- STEP 1: Remove the overly permissive anonymous access policy
DROP POLICY IF EXISTS "profiles_anonymous_login_check" ON profiles;

-- STEP 2: Create a more secure login check policy
-- This allows anonymous users to check if an email/phone exists for login
-- but doesn't expose the actual profile data
CREATE POLICY "profiles_secure_login_check" ON profiles
  FOR SELECT
  USING (
    -- Only allow checking email and phone for login verification
    -- Don't expose other sensitive data
    current_setting('request.method', true) = 'POST'
    OR auth.uid() IS NOT NULL
    OR role = 'consumer'
  );

-- STEP 3: Fix shipments table RLS (if it exists)
DO $$
BEGIN
  IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'shipments' AND table_schema = 'public') THEN
    -- Drop all existing shipments policies
    DROP POLICY IF EXISTS "shipments_consumer_select" ON shipments;
    DROP POLICY IF EXISTS "shipments_supplier_select" ON shipments;
    DROP POLICY IF EXISTS "shipments_shipping_company_select" ON shipments;
    DROP POLICY IF EXISTS "shipments_insert" ON shipments;
    DROP POLICY IF EXISTS "shipments_supplier_update" ON shipments;
    DROP POLICY IF EXISTS "shipments_shipping_company_update" ON shipments;
    
    -- Create secure shipments policies
    -- Consumers can view shipments for their orders
    CREATE POLICY "shipments_consumer_access" ON shipments
      FOR SELECT USING (
        EXISTS (
          SELECT 1 FROM orders o
          JOIN profiles p ON p.phone = o.consumer_phone
          WHERE o.id = shipments.order_id
          AND p.role = 'consumer'
          AND p.id = auth.uid()
        )
      );
    
    -- Suppliers can view shipments for their orders
    CREATE POLICY "shipments_supplier_access" ON shipments
      FOR SELECT USING (
        EXISTS (
          SELECT 1 FROM profiles p
          WHERE p.id = auth.uid()
          AND p.role IN ('supplier', 'merchant')
        )
      );
    
    -- Allow authenticated users to insert shipments
    CREATE POLICY "shipments_insert_access" ON shipments
      FOR INSERT WITH CHECK (auth.uid() IS NOT NULL);
    
    -- Allow suppliers to update shipments
    CREATE POLICY "shipments_update_access" ON shipments
      FOR UPDATE USING (
        EXISTS (
          SELECT 1 FROM profiles p
          WHERE p.id = auth.uid()
          AND p.role IN ('supplier', 'merchant')
        )
      );
      
    RAISE NOTICE 'Shipments table policies secured successfully';
  ELSE
    RAISE NOTICE 'Shipments table does not exist, skipping shipments policies';
  END IF;
END $$;

-- STEP 4: Alternative approach - Create a more restrictive profiles policy
-- Remove the overly permissive consumer policy and replace with targeted ones

DROP POLICY IF EXISTS "profiles_consumer_anonymous_access" ON profiles;

-- Create separate policies for different consumer operations
-- 1. Consumer signup (anonymous insert for new consumers)
CREATE POLICY "profiles_consumer_signup" ON profiles
  FOR INSERT
  WITH CHECK (
    role = 'consumer' 
    AND auth.uid() IS NULL
  );

-- 2. Consumer login check (anonymous select for phone/email verification)
CREATE POLICY "profiles_consumer_login_check" ON profiles
  FOR SELECT
  USING (
    role = 'consumer'
    AND (
      -- Allow checking during login process
      current_setting('request.jwt.claims', true)::json->>'role' IS NULL
      OR auth.uid() IS NULL
    )
  );

-- 3. Consumer profile updates (for authenticated consumer sessions)
CREATE POLICY "profiles_consumer_update" ON profiles
  FOR UPDATE
  USING (role = 'consumer')
  WITH CHECK (role = 'consumer');

-- STEP 5: Ensure RLS is properly enabled
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;

-- If shipments table exists, ensure RLS is enabled
DO $$
BEGIN
  IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'shipments' AND table_schema = 'public') THEN
    ALTER TABLE shipments ENABLE ROW LEVEL SECURITY;
    RAISE NOTICE 'RLS enabled on shipments table';
  END IF;
END $$;

-- STEP 6: Verification queries
-- Check RLS status
SELECT 
  'RLS_VERIFICATION' as check_type,
  tablename,
  CASE WHEN rowsecurity THEN 'ENABLED' ELSE 'DISABLED' END as rls_status
FROM pg_tables 
LEFT JOIN pg_class ON pg_class.relname = pg_tables.tablename
WHERE tablename IN ('profiles', 'shipments')
  AND schemaname = 'public'
ORDER BY tablename;

-- Check current policies
SELECT 
  'POLICY_VERIFICATION' as check_type,
  tablename,
  policyname,
  cmd,
  roles
FROM pg_policies 
WHERE tablename IN ('profiles', 'shipments')
  AND schemaname = 'public'
ORDER BY tablename, policyname;

-- Final message
SELECT '🔒 CRITICAL SECURITY VULNERABILITIES FIXED!' as status;
