# 🔐 AROUZ MARKET - COMPREHENSIVE RLS POLICY MATRIX

## 📋 **COMPLETE ACCESS MATRIX**

| Table | User Type | Operation | Condition | Policy Name |
|-------|-----------|-----------|-----------|-------------|
| **profiles** | Admin (Supplier/Merchant) | SELECT/UPDATE | `auth.uid() = id` | `admin_own_profile` |
| **profiles** | Consumer | INSERT | `role = 'consumer' AND auth.uid() IS NULL` | `consumer_signup` |
| **profiles** | Consumer | SELECT | `role = 'consumer' AND auth.uid() IS NULL` (LIMITED) | `consumer_login_check` |
| **profiles** | Service Role | ALL | `auth.role() = 'service_role'` | `service_role_access` |
| **products** | Admin | ALL | `auth.uid() = supplier_id` | `admin_products_management` |
| **products** | Anonymous | SELECT | `status = 'active'` | `public_products_browse` |
| **products** | Service Role | ALL | `auth.role() = 'service_role'` | `service_role_access` |
| **orders** | Consumer | SELECT | Phone matches via session | `consumer_orders_access` |
| **orders** | Admin | SELECT | Via order_items.supplier_account_id | `admin_orders_access` |
| **orders** | Shipping | SELECT | Via shipping company session | `shipping_orders_access` |
| **orders** | All Auth | INSERT | `auth.uid() IS NOT NULL OR consumer auth` | `order_creation` |
| **order_items** | Consumer | SELECT | Via orders.consumer_phone | `consumer_items_access` |
| **order_items** | Admin | SELECT | `supplier_account_id = auth.uid()` | `admin_items_access` |
| **order_items** | All Auth | INSERT | Order creation process | `items_creation` |
| **shipments** | Consumer | SELECT | Via orders.consumer_phone | `consumer_shipments` |
| **shipments** | Admin | SELECT/UPDATE | Via order_items.supplier_account_id | `admin_shipments` |
| **shipments** | Shipping | ALL | Via shipping company session | `shipping_management` |

---

## 🎯 **EXACT POLICY CONDITIONS**

### **PROFILES TABLE**

#### **Policy 1: admin_own_profile**
```sql
-- Suppliers and merchants access their own profiles
FOR SELECT/UPDATE USING (auth.uid() = id)
```

#### **Policy 2: consumer_signup**
```sql
-- Allow anonymous consumer registration
FOR INSERT WITH CHECK (
  role = 'consumer' 
  AND auth.uid() IS NULL
)
```

#### **Policy 3: consumer_login_check**
```sql
-- Allow limited anonymous access for login verification
FOR SELECT USING (
  role = 'consumer'
  AND auth.uid() IS NULL
  -- Application must limit fields returned
)
```

#### **Policy 4: service_role_access**
```sql
-- Service role full access for triggers
FOR ALL USING (auth.role() = 'service_role')
WITH CHECK (auth.role() = 'service_role')
```

---

### **PRODUCTS TABLE**

#### **Policy 1: admin_products_management**
```sql
-- Suppliers/merchants manage their products
FOR ALL USING (auth.uid() = supplier_id)
WITH CHECK (auth.uid() = supplier_id)
```

#### **Policy 2: public_products_browse**
```sql
-- Anonymous users browse active products
FOR SELECT USING (
  status = 'active'
  AND auth.uid() IS NULL
)
```

#### **Policy 3: service_role_access**
```sql
-- Service role full access
FOR ALL USING (auth.role() = 'service_role')
WITH CHECK (auth.role() = 'service_role')
```

---

### **ORDERS TABLE**

#### **Policy 1: consumer_orders_access**
```sql
-- Consumers access their orders via phone
FOR SELECT USING (
  consumer_phone IN (
    SELECT phone FROM profiles 
    WHERE role = 'consumer'
    AND phone = current_setting('app.consumer_phone', true)
  )
)
```

#### **Policy 2: admin_orders_access**
```sql
-- Admins access orders containing their products
FOR SELECT USING (
  EXISTS (
    SELECT 1 FROM order_items oi
    WHERE oi.order_id = orders.id
    AND oi.supplier_account_id = auth.uid()
  )
)
```

#### **Policy 3: shipping_orders_access**
```sql
-- Shipping companies access assigned orders
FOR SELECT USING (
  EXISTS (
    SELECT 1 FROM shipments s
    JOIN shipping_companies sc ON sc.id = s.shipping_company_id
    WHERE s.order_id = orders.id
    AND sc.login_code = current_setting('app.shipping_company_code', true)
  )
)
```

#### **Policy 4: order_creation**
```sql
-- Allow order creation for authenticated users
FOR INSERT WITH CHECK (
  auth.uid() IS NOT NULL 
  OR current_setting('app.consumer_phone', true) IS NOT NULL
)
```

---

### **ORDER_ITEMS TABLE**

#### **Policy 1: consumer_items_access**
```sql
-- Consumers see items in their orders
FOR SELECT USING (
  EXISTS (
    SELECT 1 FROM orders o
    WHERE o.id = order_items.order_id
    AND o.consumer_phone = current_setting('app.consumer_phone', true)
  )
)
```

#### **Policy 2: admin_items_access**
```sql
-- Admins see their own items
FOR SELECT USING (supplier_account_id = auth.uid())
```

#### **Policy 3: items_creation**
```sql
-- Allow item creation during order process
FOR INSERT WITH CHECK (
  auth.uid() IS NOT NULL 
  OR current_setting('app.consumer_phone', true) IS NOT NULL
)
```

---

### **SHIPMENTS TABLE**

#### **Policy 1: consumer_shipments**
```sql
-- Consumers see shipments for their orders
FOR SELECT USING (
  EXISTS (
    SELECT 1 FROM orders o
    WHERE o.id = shipments.order_id
    AND o.consumer_phone = current_setting('app.consumer_phone', true)
  )
)
```

#### **Policy 2: admin_shipments**
```sql
-- Admins manage shipments for their orders
FOR SELECT/UPDATE USING (
  EXISTS (
    SELECT 1 FROM order_items oi
    WHERE oi.order_id = shipments.order_id
    AND oi.supplier_account_id = auth.uid()
  )
)
```

#### **Policy 3: shipping_management**
```sql
-- Shipping companies manage their shipments
FOR ALL USING (
  EXISTS (
    SELECT 1 FROM shipping_companies sc
    WHERE sc.id = shipments.shipping_company_id
    AND sc.login_code = current_setting('app.shipping_company_code', true)
  )
)
```

---

## 🔧 **CRITICAL IMPLEMENTATION NOTES**

### **1. NO RECURSIVE REFERENCES**
- ❌ **NEVER** reference the same table in its own policy
- ✅ Use external references or session settings

### **2. CONSUMER AUTHENTICATION**
- Uses `current_setting('app.consumer_phone', true)` 
- Application sets this during consumer login
- No Supabase auth.uid() for consumers

### **3. SHIPPING COMPANY AUTHENTICATION**
- Uses `current_setting('app.shipping_company_code', true)`
- Application sets this during shipping company login
- No Supabase auth.uid() for shipping companies

### **4. SERVICE ROLE ACCESS**
- Always allow service role full access
- Required for triggers and admin operations
- Uses `auth.role() = 'service_role'`

### **5. ANONYMOUS ACCESS LIMITATIONS**
- Only for product browsing and consumer signup/login
- Must be carefully controlled to prevent data exposure
- Application must limit returned fields

---

## ✅ **VALIDATION CHECKLIST**

- [ ] No circular references between tables
- [ ] No recursive policy conditions
- [ ] Each user type has clear access patterns
- [ ] Service role has full access everywhere
- [ ] Anonymous access is limited and secure
- [ ] Consumer auth uses session settings
- [ ] Shipping auth uses session settings
- [ ] Admin auth uses Supabase auth.uid()

**NEXT:** Design the perfect policies based on this matrix!
