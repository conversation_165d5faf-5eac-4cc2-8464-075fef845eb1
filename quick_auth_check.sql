-- AROUZ MARKET - Quick Authentication & RLS Investigation
-- This SQL script performs a comprehensive analysis of the current authentication state

-- 1. Check RLS status on critical tables
SELECT 
  schemaname,
  tablename,
  rowsecurity as rls_enabled,
  CASE WHEN rowsecurity THEN '✅ ENABLED' ELSE '❌ DISABLED' END as status
FROM pg_tables 
LEFT JOIN pg_class ON pg_class.relname = pg_tables.tablename
WHERE tablename IN ('profiles', 'orders', 'order_items', 'shipments', 'products')
  AND schemaname = 'public'
ORDER BY tablename;

-- 2. List all RLS policies on critical tables
SELECT 
  schemaname,
  tablename,
  policyname,
  cmd,
  roles,
  qual,
  with_check
FROM pg_policies 
WHERE tablename IN ('profiles', 'orders', 'order_items', 'shipments', 'products')
  AND schemaname = 'public'
ORDER BY tablename, policyname;

-- 3. Check profiles table structure and sample data
SELECT 
  'PROFILES_STRUCTURE' as check_type,
  column_name,
  data_type,
  is_nullable,
  column_default
FROM information_schema.columns 
WHERE table_name = 'profiles' 
  AND table_schema = 'public'
ORDER BY ordinal_position;

-- 4. Check profiles data distribution
SELECT 
  'PROFILES_DATA' as check_type,
  role,
  COUNT(*) as count,
  MIN(created_at) as earliest,
  MAX(created_at) as latest
FROM profiles 
GROUP BY role
ORDER BY count DESC;

-- 5. Check for potential data isolation issues
SELECT 
  'ISOLATION_CHECK' as check_type,
  'profiles' as table_name,
  COUNT(DISTINCT id) as unique_users,
  COUNT(DISTINCT email) as unique_emails,
  COUNT(DISTINCT phone) as unique_phones,
  COUNT(*) as total_records
FROM profiles;

-- 6. Check recent auth.users vs profiles alignment
SELECT 
  'AUTH_ALIGNMENT' as check_type,
  (SELECT COUNT(*) FROM auth.users) as auth_users_count,
  (SELECT COUNT(*) FROM profiles WHERE role != 'consumer') as non_consumer_profiles,
  (SELECT COUNT(*) FROM profiles WHERE role = 'consumer') as consumer_profiles,
  (SELECT COUNT(*) FROM profiles) as total_profiles;

-- 7. Check for orphaned profiles (profiles without auth.users)
SELECT 
  'ORPHANED_PROFILES' as check_type,
  COUNT(*) as orphaned_count
FROM profiles p
LEFT JOIN auth.users u ON u.id = p.id
WHERE u.id IS NULL AND p.role != 'consumer';

-- 8. Check orders table access patterns
SELECT 
  'ORDERS_ACCESS' as check_type,
  COUNT(*) as total_orders,
  COUNT(DISTINCT consumer_phone) as unique_consumers,
  COUNT(DISTINCT supplier_id) as unique_suppliers
FROM orders;

-- 9. Check for any obvious security issues
SELECT 
  'SECURITY_CHECK' as check_type,
  CASE 
    WHEN EXISTS (
      SELECT 1 FROM pg_policies 
      WHERE tablename = 'profiles' 
        AND policyname LIKE '%anonymous%'
        AND cmd = 'SELECT'
    ) THEN 'CONSUMER_ANON_ACCESS_ENABLED'
    ELSE 'NO_ANON_ACCESS'
  END as consumer_access_status;

-- 10. Check migration history
SELECT 
  'MIGRATION_HISTORY' as check_type,
  version,
  name,
  executed_at
FROM supabase_migrations 
ORDER BY version DESC 
LIMIT 10;
