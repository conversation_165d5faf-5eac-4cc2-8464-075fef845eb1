import{q as y,r as l,j as e,v as u,C as m,k as g,Q as p,m as N,l as x,B as d,aE as h,az as A}from"./index-BrxEt-yR.js";import{a as v}from"./centralizedProductData-14xCp_S-.js";import{i as D}from"./productUtils-DuBNZRf0.js";import"./idGenerator-CIYG0XcT.js";import"./categories-k_ueMvEL.js";function E(){const{productId:s}=y(),[i,f]=l.useState({}),[P,j]=l.useState(!0);l.useEffect(()=>{(async()=>{console.log("🔍 DIAGNOSTIC: Starting comprehensive ProductPage diagnostics");const a={timestamp:new Date().toISOString(),productId:s,tests:{}};if(a.tests.componentRendering={status:"PASS",message:"ProductPageDiagnostic component is rendering correctly"},a.tests.urlParameter={status:s?"PASS":"FAIL",productId:s,message:s?`Product ID extracted: ${s}`:"No product ID in URL"},s){const t=D(s);a.tests.idValidation={status:t?"PASS":"FAIL",isValid:t,message:t?"Product ID format is valid":"Product ID format is invalid"}}try{const t=v();if(a.tests.centralizedData={status:"PASS",totalProducts:t.length,sampleIds:t.slice(0,5).map(r=>r.id),message:`Successfully loaded ${t.length} products from centralized data`},s){const r=t.find(n=>n.id===s);a.tests.centralizedLookup={status:r?"PASS":"FAIL",found:!!r,productName:r?.name,message:r?`Product found in centralized data: ${r.name}`:"Product not found in centralized data"}}}catch(t){a.tests.centralizedData={status:"FAIL",error:t.message,message:"Failed to access centralized data"}}try{console.log("🔍 DIAGNOSTIC: Testing real marketplace data");const t=await h("tyres"),r=await h("brakes"),n=[...t,...r];if(a.tests.marketplaceData={status:n.length>0?"PASS":"FAIL",totalProducts:n.length,tyreCount:t.length,brakeCount:r.length,sampleIds:n.slice(0,10).map(c=>c.id),message:`Found ${n.length} real marketplace products (${t.length} tyres, ${r.length} brakes)`},s){const c=n.find(b=>b.id===s);a.tests.marketplaceLookup={status:c?"PASS":"FAIL",found:!!c,productName:c?.name,productCategory:c?.category,productSection:c?.marketplaceSection,message:c?`Product found in marketplace data: ${c.name} (${c.category}/${c.marketplaceSection})`:"Product not found in marketplace data"}}}catch(t){a.tests.marketplaceData={status:"FAIL",error:t.message,message:"Failed to access marketplace data"}}if(s)try{console.log("🔍 DIAGNOSTIC: Testing getProductById service");const t=await A(s);a.tests.serviceFetch={status:t?"PASS":"FAIL",found:!!t,productName:t?.name,productCategory:t?.category,productSection:t?.marketplaceSection,message:t?`Service successfully returned product: ${t.name} (${t.category}/${t.marketplaceSection})`:"Service returned null/undefined"}}catch(t){a.tests.serviceFetch={status:"FAIL",error:t.message,message:"Service layer threw an error"}}a.tests.routing={status:"PASS",currentPath:window.location.pathname,expectedPattern:"/:productId",message:"React Router is working - component loaded via dynamic route"},console.log("🔍 DIAGNOSTIC: Complete results:",a),f(a),j(!1)})()},[s]);const S=o=>{switch(o){case"PASS":return"bg-green-100 text-green-800 border-green-200";case"FAIL":return"bg-red-100 text-red-800 border-red-200";default:return"bg-gray-100 text-gray-800 border-gray-200"}},k=()=>{["TYR-100000-WHOLESALE","TYR-100000-RETAIL","TYR-100001-WHOLESALE","TYR-100001-RETAIL","BRK-100000-WHOLESALE","BRK-100000-RETAIL"].forEach(a=>{window.open(`/${a}`,"_blank")})},I=()=>{const o=i.tests?.marketplaceData?.sampleIds||[];if(o.length===0){alert("No marketplace products found. Please run the diagnostic first.");return}o.slice(0,5).forEach(a=>{window.open(`/${a}`,"_blank")})};return P?e.jsx(u,{children:e.jsx("div",{className:"container py-8",children:e.jsxs("div",{className:"text-center",children:[e.jsx("h1",{className:"text-2xl font-bold mb-4",children:"Running ProductPage Diagnostics..."}),e.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-[#fa7b00] mx-auto"})]})})}):e.jsx(u,{children:e.jsx("div",{className:"container py-8",children:e.jsxs("div",{className:"max-w-4xl mx-auto",children:[e.jsxs("div",{className:"mb-8",children:[e.jsx("h1",{className:"text-3xl font-bold mb-4",children:"ProductPage Diagnostic Report"}),e.jsx("p",{className:"text-gray-600",children:"Comprehensive analysis of ProductPage functionality and data flow"}),e.jsxs("div",{className:"mt-4 p-4 bg-blue-50 rounded-lg",children:[e.jsxs("p",{className:"text-sm text-blue-800",children:[e.jsx("strong",{children:"Test URL:"})," ",window.location.href]}),e.jsxs("p",{className:"text-sm text-blue-800",children:[e.jsx("strong",{children:"Timestamp:"})," ",i.timestamp]})]})]}),e.jsx("div",{className:"space-y-4 mb-8",children:Object.entries(i.tests||{}).map(([o,a])=>e.jsxs(m,{children:[e.jsx(g,{className:"pb-3",children:e.jsxs(p,{className:"flex items-center justify-between",children:[e.jsx("span",{className:"capitalize",children:o.replace(/([A-Z])/g," $1").trim()}),e.jsx(N,{className:S(a.status),children:a.status})]})}),e.jsxs(x,{children:[e.jsx("p",{className:"text-sm text-gray-700 mb-2",children:a.message}),a.error&&e.jsxs("div",{className:"mt-2 p-2 bg-red-50 rounded text-xs text-red-700",children:[e.jsx("strong",{children:"Error:"})," ",a.error]}),a.productName&&e.jsxs("div",{className:"mt-2 p-2 bg-green-50 rounded text-xs text-green-700",children:[e.jsx("strong",{children:"Product:"})," ",a.productName]})]})]},o))}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs(m,{children:[e.jsx(g,{children:e.jsx(p,{children:"Test Real Marketplace Products"})}),e.jsxs(x,{children:[e.jsx("p",{className:"text-sm text-gray-600 mb-4",children:"Click to test actual products from the marketplace database:"}),e.jsxs("div",{className:"flex gap-3",children:[e.jsx(d,{onClick:I,className:"bg-[#fa7b00] hover:bg-[#fa7b00]/90",children:"Test Marketplace Products"}),e.jsx(d,{onClick:k,variant:"outline",children:"Test Mock Product IDs"})]})]})]}),e.jsxs("div",{className:"flex space-x-4",children:[e.jsx(d,{variant:"outline",onClick:()=>window.location.href="/product-debug",children:"View Product Debug Page"}),e.jsx(d,{variant:"outline",onClick:()=>window.location.href="/",children:"Back to Marketplace"})]})]})]})})})}export{E as default};
