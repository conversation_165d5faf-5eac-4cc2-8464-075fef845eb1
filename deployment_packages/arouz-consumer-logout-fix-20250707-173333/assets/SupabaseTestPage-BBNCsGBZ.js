import{a7 as F,r as u,aC as A,j as e,v as R,C as l,k as i,Q as o,l as d,a6 as w,U as B,ab as J,_ as M,m as x,B as f,ay as h}from"./index-BrxEt-yR.js";import{g as z}from"./features-CbSLR5ud.js";import{D as O}from"./database-CDiulvn7.js";import{S as _}from"./settings-3dK106XO.js";function G(){const{isAuthenticated:N,user:j,profile:y}=F(),[b,g]=u.useState("checking"),[v,r]=u.useState({}),[m,c]=u.useState(!1),C="https://irkwpzcskeqtasutqnxp.supabase.co",S="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imlya3dwemNza2VxdGFzdXRxbnhwIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDc0OTg2ODMsImV4cCI6MjA2MzA3NDY4M30.bUv9zxx9q_sVUwv_-G6WbHsA53-SU5tCCWbOS_5Xp94",p=A(C,S);u.useEffect(()=>{k()},[]);const k=async()=>{c(!0);try{const{data:a,error:t}=await p.from("products").select("count",{count:"exact",head:!0});t?(console.error("Supabase connection error:",t),g("error"),r(s=>({...s,connectionError:t.message}))):(g("connected"),r(s=>({...s,connectionSuccess:!0,productCount:a})))}catch(a){console.error("Connection test failed:",a),g("error"),r(t=>({...t,connectionError:a.message}))}finally{c(!1)}},U=async()=>{c(!0);try{const a=["products","tyre_specifications","vehicle_compatibility","pricing_tiers"],t={};for(const s of a)try{const{data:I,error:T,count:D}=await p.from(s).select("*",{count:"exact",head:!0});t[s]={exists:!T,count:D||0,error:T?.message}}catch(I){t[s]={exists:!1,error:I.message}}r(s=>({...s,tables:t})),h.success("Database tables tested successfully")}catch(a){console.error("Table test failed:",a),h.error("Failed to test database tables")}finally{c(!1)}},E=async()=>{c(!0);try{const{data:a,error:t}=await p.storage.listBuckets();t?(r(s=>({...s,storageError:t.message})),h.error("Failed to test storage buckets")):(r(s=>({...s,buckets:a})),h.success("Storage buckets tested successfully"))}catch(a){console.error("Storage test failed:",a),h.error("Storage test failed")}finally{c(!1)}},n=z();return e.jsx(R,{children:e.jsxs("div",{className:"container py-8",children:[e.jsxs("div",{className:"mb-8",children:[e.jsx("h1",{className:"text-3xl font-bold mb-4",children:"Supabase Connection Test"}),e.jsx("p",{className:"text-gray-600 mb-6",children:"Test and verify Supabase backend connection, authentication, and database access."})]}),e.jsxs(l,{className:"mb-6",children:[e.jsx(i,{children:e.jsxs(o,{className:"flex items-center gap-2",children:[e.jsx(O,{className:"h-5 w-5"}),"Connection Status"]})}),e.jsxs(d,{children:[e.jsxs("div",{className:"flex items-center gap-3 mb-4",children:[b==="checking"&&e.jsxs(e.Fragment,{children:[e.jsx(w,{className:"h-5 w-5 text-blue-600 animate-spin"}),e.jsx("span",{children:"Checking connection..."})]}),b==="connected"&&e.jsxs(e.Fragment,{children:[e.jsx(B,{className:"h-5 w-5 text-green-600"}),e.jsx("span",{className:"text-green-800",children:"Connected to Supabase"})]}),b==="error"&&e.jsxs(e.Fragment,{children:[e.jsx(J,{className:"h-5 w-5 text-red-600"}),e.jsx("span",{className:"text-red-800",children:"Connection failed"})]})]}),e.jsxs("div",{className:"grid grid-cols-2 gap-4 text-sm",children:[e.jsxs("div",{children:[e.jsx("strong",{children:"Supabase URL:"}),e.jsx("br",{}),e.jsx("code",{className:"text-xs bg-gray-100 px-2 py-1 rounded",children:C})]}),e.jsxs("div",{children:[e.jsx("strong",{children:"Anon Key:"}),e.jsx("br",{}),e.jsx("code",{className:"text-xs bg-gray-100 px-2 py-1 rounded",children:`${S.substring(0,20)}...`})]})]})]})]}),e.jsxs(l,{className:"mb-6",children:[e.jsx(i,{children:e.jsxs(o,{className:"flex items-center gap-2",children:[e.jsx(M,{className:"h-5 w-5"}),"Authentication Status"]})}),e.jsx(d,{children:e.jsxs("div",{className:"space-y-3",children:[e.jsx("div",{className:"flex items-center gap-2",children:e.jsx(x,{variant:N?"default":"secondary",children:N?"Authenticated":"Not Authenticated"})}),j&&e.jsxs("div",{className:"text-sm space-y-1",children:[e.jsxs("div",{children:[e.jsx("strong",{children:"User ID:"})," ",j.id]}),e.jsxs("div",{children:[e.jsx("strong",{children:"Email:"})," ",j.email]}),y&&e.jsxs("div",{children:[e.jsx("strong",{children:"Role:"})," ",y.role]})]})]})})]}),e.jsxs(l,{className:"mb-6",children:[e.jsx(i,{children:e.jsxs(o,{className:"flex items-center gap-2",children:[e.jsx(_,{className:"h-5 w-5"}),"Feature Flags"]})}),e.jsx(d,{children:e.jsxs("div",{className:"grid grid-cols-2 gap-4 text-sm",children:[e.jsxs("div",{children:[e.jsx("strong",{children:"Use Supabase Backend:"}),e.jsx(x,{variant:n.useSupabaseBackend?"default":"secondary",className:"ml-2",children:n.useSupabaseBackend?"Enabled":"Disabled"})]}),e.jsxs("div",{children:[e.jsx("strong",{children:"Enable Image Upload:"}),e.jsx(x,{variant:n.enableImageUpload?"default":"secondary",className:"ml-2",children:n.enableImageUpload?"Enabled":"Disabled"})]}),e.jsxs("div",{children:[e.jsx("strong",{children:"Enable Real-time Updates:"}),e.jsx(x,{variant:n.enableRealTimeUpdates?"default":"secondary",className:"ml-2",children:n.enableRealTimeUpdates?"Enabled":"Disabled"})]}),e.jsxs("div",{children:[e.jsx("strong",{children:"Migration Tools:"}),e.jsx(x,{variant:n.enableMigrationTools?"default":"secondary",className:"ml-2",children:n.enableMigrationTools?"Enabled":"Disabled"})]})]})})]}),e.jsxs(l,{className:"mb-6",children:[e.jsx(i,{children:e.jsx(o,{children:"Test Actions"})}),e.jsx(d,{children:e.jsxs("div",{className:"flex gap-4",children:[e.jsxs(f,{onClick:k,disabled:m,variant:"outline",children:[m?e.jsx(w,{className:"h-4 w-4 animate-spin mr-2"}):null,"Test Connection"]}),e.jsx(f,{onClick:U,disabled:m,variant:"outline",children:"Test Database Tables"}),e.jsx(f,{onClick:E,disabled:m,variant:"outline",children:"Test Storage Buckets"})]})})]}),Object.keys(v).length>0&&e.jsxs(l,{children:[e.jsx(i,{children:e.jsx(o,{children:"Test Results"})}),e.jsx(d,{children:e.jsx("pre",{className:"bg-gray-100 p-4 rounded text-sm overflow-auto",children:JSON.stringify(v,null,2)})})]})]})})}export{G as default};
