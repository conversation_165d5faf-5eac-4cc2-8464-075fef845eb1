import"./index-BrxEt-yR.js";const l=3,f=60,d=new Map;function p(r){const e=r.replace(/\D/g,"");return e.startsWith("213")&&e.length===12?{isValid:!0,formatted:`+${e}`}:e.startsWith("0")&&e.length===10?{isValid:!0,formatted:`+213${e.substring(1)}`}:e.length===9?{isValid:!0,formatted:`+213${e}`}:{isValid:!1,formatted:r,error:"Invalid Algerian phone number. Must be in format +213XXXXXXXXX or 0XXXXXXXXX"}}function g(r){const e=Date.now(),s=f*60*1e3,t=d.get(r);return t?e-t.windowStart>s?(d.set(r,{attempts:1,windowStart:e}),{allowed:!0,remainingAttempts:l-1}):t.attempts>=l?{allowed:!1,remainingAttempts:0,resetTime:t.windowStart+s}:(t.attempts++,d.set(r,t),{allowed:!0,remainingAttempts:l-t.attempts}):(d.set(r,{attempts:1,windowStart:e}),{allowed:!0,remainingAttempts:l-1})}async function y(r){try{const e=p(r);if(!e.isValid)return{success:!1,error:e.error};const s=e.formatted,t=g(s);if(!t.allowed){const o=new Date(t.resetTime);return{success:!1,error:`Too many attempts. Try again after ${o.toLocaleTimeString()}`,rateLimitInfo:{remainingAttempts:0,resetTime:o.toISOString()}}}console.log("🔐 Sending OTP via Supabase Edge Function:",{to:s});const c="https://irkwpzcskeqtasutqnxp.supabase.co",a="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imlya3dwemNza2VxdGFzdXRxbnhwIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDc0OTg2ODMsImV4cCI6MjA2MzA3NDY4M30.bUv9zxx9q_sVUwv_-G6WbHsA53-SU5tCCWbOS_5Xp94",n=await fetch(`${c}/functions/v1/send-sms-otp`,{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${a}`,apikey:a},body:JSON.stringify({phone:s})});if(console.log("📡 Supabase Edge Function response status:",n.status),!n.ok){const o=await n.text();console.error("❌ Supabase Edge Function error:",o);let u="Failed to send SMS";try{u=JSON.parse(o).error||u}catch{u=`HTTP ${n.status}: ${o}`}return{success:!1,error:u,rateLimitInfo:{remainingAttempts:t.remainingAttempts,resetTime:t.resetTime}}}const i=await n.json();return console.log("✅ SMS OTP sent successfully:",i),i.success&&i.data?{success:!0,data:i.data,rateLimitInfo:{remainingAttempts:t.remainingAttempts-1,resetTime:t.resetTime}}:{success:!1,error:i.error||"Failed to send SMS",rateLimitInfo:{remainingAttempts:t.remainingAttempts,resetTime:t.resetTime}}}catch(e){return console.error("❌ Error sending OTP:",e),{success:!1,error:"Network error. Please check your connection and try again."}}}async function I(r,e){try{if(!r||!e)return console.error("❌ Missing required parameters:",{verificationId:!!r,code:!!e}),{success:!1,error:"Verification ID and code are required"};if(!/^\d{6}$/.test(e))return{success:!1,error:"Invalid code format. Please enter a 6-digit code."};console.log("🔐 Verifying OTP via Supabase Edge Function:",{verificationId:r,code:"******",verificationIdType:typeof r,codeType:typeof e,verificationIdLength:r?.length||0,codeLength:e?.length||0});const s="https://irkwpzcskeqtasutqnxp.supabase.co",t="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imlya3dwemNza2VxdGFzdXRxbnhwIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDc0OTg2ODMsImV4cCI6MjA2MzA3NDY4M30.bUv9zxx9q_sVUwv_-G6WbHsA53-SU5tCCWbOS_5Xp94",c={verificationId:r,code:e};console.log("📤 Sending verification request:",{...c,code:"******"});const a=await fetch(`${s}/functions/v1/verify-sms-otp`,{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${t}`,apikey:t},body:JSON.stringify(c)});if(console.log("📡 Supabase Edge Function verify response status:",a.status),!a.ok){const i=await a.text();console.error("❌ Supabase Edge Function verify error:",i);let o="Invalid verification code";try{o=JSON.parse(i).error||o}catch{o=`HTTP ${a.status}: ${i}`}return{success:!1,error:o}}const n=await a.json();return console.log("✅ SMS OTP verification response:",n),n.success&&n.data?(console.log("🎉 OTP verified successfully:",{id:r}),{success:!0,data:n.data}):{success:!1,error:n.error||"Invalid verification code"}}catch(s){return console.error("❌ Error verifying OTP:",s),{success:!1,error:"Network error. Please check your connection and try again."}}}export{I as a,y as s,p as v};
