import{r as y,j as e,v as I,C as m,k as x,Q as h,B as b,a6 as T,l as u,aA as M,U as v,aB as S,ab as $,m as f,ay as p}from"./index-BrxEt-yR.js";import{P as C}from"./progress-BHNvXqqQ.js";import{isValidProductId as R}from"./idGenerator-CIYG0XcT.js";import{D as k}from"./database-CDiulvn7.js";import{Z as O}from"./zap-BMtwEZ7O.js";import"./categories-k_ueMvEL.js";const E=(s,l="retail")=>{if(R(s.id)&&(s.id.endsWith("-WHOLESALE")||s.id.endsWith("-RETAIL")))return s;let t=l;"marketplaceSection"in s&&s.marketplaceSection?t=s.marketplaceSection:s.wholesalePricingTiers&&s.wholesalePricingTiers.length>0&&!s.retailPrice?t="wholesale":s.retailPrice&&!s.wholesalePricingTiers&&(t="retail");const i=t==="wholesale"?"WHOLESALE":"RETAIL",r=`${s.id}-${i}`;return{...s,id:r,marketplaceSection:t}},A=(s,l="retail")=>{const t=`products-${s}`,i=localStorage.getItem(t);if(!i)return console.log(`No products found in localStorage for category: ${s}`),0;try{let r=JSON.parse(i);r=r.map(c=>({...c,createdAt:new Date(c.createdAt),updatedAt:new Date(c.updatedAt),inventoryUpdateDate:c.inventoryUpdateDate?new Date(c.inventoryUpdateDate):void 0}));let g=0;const a=r.map(c=>{const d=c.id,o=E(c,l);return o.id!==d&&(g++,console.log(`Migrated product ID: ${d} → ${o.id}`)),o});return localStorage.setItem(t,JSON.stringify(a)),console.log(`Migration completed for category ${s}: ${g} products migrated`),g}catch(r){return console.error(`Error migrating products in category ${s}:`,r),0}},W=(s="wholesale",l="retail")=>{console.log("Starting product ID migration for all categories...");const t=A("tyres",s),i=A("brakes",s),r=t+i;return console.log("Migration summary:",{tyres:t,brakes:i,total:r}),{tyres:t,brakes:i,total:r}},P=s=>{const l=`products-${s}`,t=localStorage.getItem(l);if(!t)return console.log(`No products found in localStorage for category: ${s}`),0;try{let i=JSON.parse(t);i=i.map(a=>({...a,createdAt:new Date(a.createdAt),updatedAt:new Date(a.updatedAt),inventoryUpdateDate:a.inventoryUpdateDate?new Date(a.inventoryUpdateDate):void 0}));let r=0;const g=i.map((a,c)=>{const d=a.id;if(d.endsWith("-WHOLESALE")||d.endsWith("-RETAIL"))return a;let o;a.wholesalePricingTiers&&a.wholesalePricingTiers.length>0&&!a.retailPrice?o="wholesale":a.retailPrice&&!a.wholesalePricingTiers?o="retail":o=c<i.length/2?"wholesale":"retail";const j=E(a,o);return j.id!==d&&(r++,console.log(`Smart migrated product ID: ${d} → ${j.id} (${o})`)),j});return localStorage.setItem(l,JSON.stringify(g)),console.log(`Smart migration completed for category ${s}: ${r} products migrated`),r}catch(i){return console.error(`Error in smart migration for category ${s}:`,i),0}},B=()=>{console.log("Starting smart product ID migration for all categories...");const s=P("tyres"),l=P("brakes"),t=s+l;return console.log("Smart migration summary:",{tyres:s,brakes:l,total:t}),{tyres:s,brakes:l,total:t}},H=()=>{const s=l=>{const t=`products-${l}`,i=localStorage.getItem(t);if(!i)return{total:0,migrated:0,needsMigration:0};try{const r=JSON.parse(i),g=r.length,a=r.filter(d=>d.id.endsWith("-WHOLESALE")||d.id.endsWith("-RETAIL")).length,c=g-a;return{total:g,migrated:a,needsMigration:c}}catch(r){return console.error(`Error checking migration status for ${l}:`,r),{total:0,migrated:0,needsMigration:0}}};return{tyres:s("tyres"),brakes:s("brakes")}};function F(){const[s,l]=y.useState(null),[t,i]=y.useState(!1),[r,g]=y.useState(null);y.useEffect(()=>{a()},[]);const a=()=>{const n=H();l(n)},c=async()=>{i(!0);try{const n=B();g(n),a(),n.total>0?p.success(`Successfully migrated ${n.total} products!`,{description:`Tyres: ${n.tyres}, Brakes: ${n.brakes}`}):p.info("No products needed migration")}catch(n){console.error("Migration error:",n),p.error("Migration failed. Check console for details.")}finally{i(!1)}},d=async()=>{i(!0);try{const n=W("wholesale","retail");g(n),a(),n.total>0?p.success(`Successfully migrated ${n.total} products!`,{description:`Tyres: ${n.tyres}, Brakes: ${n.brakes}`}):p.info("No products needed migration")}catch(n){console.error("Migration error:",n),p.error("Migration failed. Check console for details.")}finally{i(!1)}},o=()=>s?s.tyres.total+s.brakes.total:0,j=()=>s?s.tyres.migrated+s.brakes.migrated:0,w=()=>s?s.tyres.needsMigration+s.brakes.needsMigration:0,L=()=>{const n=o(),D=j();return n>0?D/n*100:0},N=()=>w()===0&&o()>0;return e.jsx(I,{children:e.jsxs("div",{className:"container py-8",children:[e.jsxs("div",{className:"mb-8",children:[e.jsx("h1",{className:"text-3xl font-bold mb-4",children:"Product ID Migration"}),e.jsx("p",{className:"text-gray-600 mb-6",children:"Migrate existing products from the old ID format (TYR-100001) to the new format with marketplace section suffixes (TYR-100001-WHOLESALE/RETAIL)."})]}),e.jsxs(m,{className:"mb-6",children:[e.jsx(x,{children:e.jsxs(h,{className:"flex items-center gap-2",children:[e.jsx(k,{className:"h-5 w-5"}),"Migration Status",e.jsxs(b,{variant:"outline",size:"sm",onClick:a,className:"ml-auto",children:[e.jsx(T,{className:"h-4 w-4 mr-2"}),"Refresh"]})]})}),e.jsx(u,{children:s?e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-sm font-medium",children:"Overall Progress"}),e.jsxs("span",{className:"text-sm text-gray-600",children:[j()," / ",o()," products migrated"]})]}),e.jsx(C,{value:L(),className:"h-2"}),N()?e.jsxs(M,{className:"border-green-200 bg-green-50",children:[e.jsx(v,{className:"h-4 w-4 text-green-600"}),e.jsx(S,{className:"text-green-800",children:"All products have been successfully migrated to the new ID format!"})]}):e.jsxs(M,{className:"border-orange-200 bg-orange-50",children:[e.jsx($,{className:"h-4 w-4 text-orange-600"}),e.jsxs(S,{className:"text-orange-800",children:[w()," products still need to be migrated to the new ID format."]})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 mt-4",children:[e.jsxs(m,{children:[e.jsx(x,{className:"pb-2",children:e.jsx(h,{className:"text-lg",children:"Tyres Category"})}),e.jsx(u,{children:e.jsxs("div",{className:"space-y-2",children:[e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{children:"Total Products:"}),e.jsx(f,{variant:"outline",children:s.tyres.total})]}),e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{children:"Migrated:"}),e.jsx(f,{variant:"secondary",children:s.tyres.migrated})]}),e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{children:"Needs Migration:"}),e.jsx(f,{variant:s.tyres.needsMigration>0?"destructive":"default",children:s.tyres.needsMigration})]})]})})]}),e.jsxs(m,{children:[e.jsx(x,{className:"pb-2",children:e.jsx(h,{className:"text-lg",children:"Brakes Category"})}),e.jsx(u,{children:e.jsxs("div",{className:"space-y-2",children:[e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{children:"Total Products:"}),e.jsx(f,{variant:"outline",children:s.brakes.total})]}),e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{children:"Migrated:"}),e.jsx(f,{variant:"secondary",children:s.brakes.migrated})]}),e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{children:"Needs Migration:"}),e.jsx(f,{variant:s.brakes.needsMigration>0?"destructive":"default",children:s.brakes.needsMigration})]})]})})]})]})]}):e.jsx("div",{className:"text-center py-4",children:e.jsx("p",{className:"text-gray-500",children:"Loading migration status..."})})})]}),e.jsxs(m,{className:"mb-6",children:[e.jsx(x,{children:e.jsx(h,{children:"Migration Actions"})}),e.jsx(u,{children:e.jsx("div",{className:"space-y-4",children:e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsxs(m,{className:"border-blue-200",children:[e.jsx(x,{className:"pb-3",children:e.jsxs(h,{className:"text-lg flex items-center gap-2",children:[e.jsx(O,{className:"h-5 w-5 text-blue-600"}),"Smart Migration (Recommended)"]})}),e.jsxs(u,{children:[e.jsx("p",{className:"text-sm text-gray-600 mb-4",children:"Intelligently assigns marketplace sections based on product characteristics. Products with wholesale pricing → WHOLESALE, products with retail pricing → RETAIL."}),e.jsx(b,{onClick:c,disabled:t||N(),className:"w-full",children:t?"Migrating...":"Run Smart Migration"})]})]}),e.jsxs(m,{className:"border-gray-200",children:[e.jsx(x,{className:"pb-3",children:e.jsxs(h,{className:"text-lg flex items-center gap-2",children:[e.jsx(k,{className:"h-5 w-5 text-gray-600"}),"Basic Migration"]})}),e.jsxs(u,{children:[e.jsx("p",{className:"text-sm text-gray-600 mb-4",children:"Assigns all products to WHOLESALE suffix by default. Use this if you want manual control over marketplace sections."}),e.jsx(b,{variant:"outline",onClick:d,disabled:t||N(),className:"w-full",children:t?"Migrating...":"Run Basic Migration"})]})]})]})})})]}),r&&e.jsxs(m,{children:[e.jsx(x,{children:e.jsxs(h,{className:"flex items-center gap-2",children:[e.jsx(v,{className:"h-5 w-5 text-green-600"}),"Last Migration Result"]})}),e.jsx(u,{children:e.jsxs("div",{className:"grid grid-cols-3 gap-4 text-center",children:[e.jsxs("div",{children:[e.jsx("div",{className:"text-2xl font-bold text-blue-600",children:r.tyres}),e.jsx("div",{className:"text-sm text-gray-600",children:"Tyres Migrated"})]}),e.jsxs("div",{children:[e.jsx("div",{className:"text-2xl font-bold text-green-600",children:r.brakes}),e.jsx("div",{className:"text-sm text-gray-600",children:"Brakes Migrated"})]}),e.jsxs("div",{children:[e.jsx("div",{className:"text-2xl font-bold text-purple-600",children:r.total}),e.jsx("div",{className:"text-sm text-gray-600",children:"Total Migrated"})]})]})})]}),e.jsxs(m,{className:"mt-6",children:[e.jsx(x,{children:e.jsx(h,{children:"Migration Instructions"})}),e.jsx(u,{children:e.jsxs("div",{className:"space-y-3 text-sm",children:[e.jsxs("p",{children:["• ",e.jsx("strong",{children:"Smart Migration"})," is recommended as it intelligently assigns marketplace sections based on product pricing structure."]}),e.jsxs("p",{children:["• Products with wholesale pricing tiers will get ",e.jsx("code",{className:"bg-gray-100 px-1 rounded",children:"-WHOLESALE"})," suffix."]}),e.jsxs("p",{children:["• Products with retail pricing will get ",e.jsx("code",{className:"bg-gray-100 px-1 rounded",children:"-RETAIL"})," suffix."]}),e.jsx("p",{children:"• Products with both or neither pricing will be distributed evenly between wholesale and retail."}),e.jsx("p",{children:"• After migration, new products will automatically use the correct ID format based on the user's role."}),e.jsxs("p",{children:["• ",e.jsx("strong",{children:"Supplier accounts"})," will generate IDs with ",e.jsx("code",{className:"bg-gray-100 px-1 rounded",children:"-WHOLESALE"})," suffix."]}),e.jsxs("p",{children:["• ",e.jsx("strong",{children:"Merchant accounts"})," will generate IDs with ",e.jsx("code",{className:"bg-gray-100 px-1 rounded",children:"-RETAIL"})," suffix."]})]})})]})]})})}export{F as default};
