import{s as C,r as u,j as s,C as i,k as t,Q as c,l,B as a,as as D,aE as f}from"./index-BrxEt-yR.js";function S(){const{items:o}=C(),[h,x]=u.useState([]),[p,g]=u.useState(!1),j=async()=>{g(!0);try{console.log("🧪 [DEBUG] Fetching marketplace products...");const e=await f("tyres");console.log("🧪 [DEBUG] Fetched products:",e),x(e)}catch(e){console.error("🧪 [DEBUG] Error fetching products:",e)}finally{g(!1)}},m=()=>{console.log("🧪 [DEBUG] Current cart items:",o);const e=o.reduce((r,n)=>{const d=n.supplierName||"Unknown Supplier";return r[d]||(r[d]={items:[],location:n.shippingOrigin||"Location TBD"}),r[d].items.push(n),r},{});console.log("🧪 [DEBUG] Grouped by supplier:",e)},y=()=>{console.log("🧪 [DEBUG] LocalStorage data:"),console.log("Cart:",localStorage.getItem("arouz_cart")),console.log("Phone Session:",localStorage.getItem("phone_auth_session")),console.log("Consumer Profile:",localStorage.getItem("consumer_profile"))};return s.jsxs("div",{className:"container mx-auto p-6 space-y-6",children:[s.jsx("h1",{className:"text-3xl font-bold text-red-600",children:"🐛 DEBUG PAGE"}),s.jsxs(i,{children:[s.jsx(t,{children:s.jsx(c,{children:"📦 Product Fetching Test"})}),s.jsxs(l,{className:"space-y-4",children:[s.jsx(a,{onClick:j,disabled:p,children:p?"Loading...":"Fetch Test Products"}),h.length>0&&s.jsxs("div",{className:"space-y-2",children:[s.jsx("h3",{className:"font-semibold",children:"Fetched Products:"}),h.slice(0,3).map((e,r)=>s.jsxs("div",{className:"p-3 bg-gray-100 rounded text-sm",children:[s.jsxs("div",{children:[s.jsx("strong",{children:"ID:"})," ",e.id]}),s.jsxs("div",{children:[s.jsx("strong",{children:"Name:"})," ",e.name]}),s.jsxs("div",{children:[s.jsx("strong",{children:"Supplier:"})," ",e.supplierName]}),s.jsxs("div",{children:[s.jsx("strong",{children:"Supplier Account ID:"})," ",e.supplierAccountId]}),s.jsxs("div",{children:[s.jsx("strong",{children:"Shipping Origin:"})," ",e.shippingOrigin]})]},r))]})]})]}),s.jsxs(i,{children:[s.jsx(t,{children:s.jsx(c,{children:"🛒 Cart Data Test"})}),s.jsxs(l,{className:"space-y-4",children:[s.jsx(a,{onClick:m,children:"Test Cart Data in Console"}),s.jsxs("div",{className:"space-y-2",children:[s.jsxs("h3",{className:"font-semibold",children:["Current Cart Items (",o.length,"):"]}),o.map((e,r)=>s.jsxs("div",{className:"p-3 bg-gray-100 rounded text-sm",children:[s.jsxs("div",{children:[s.jsx("strong",{children:"Name:"})," ",e.name]}),s.jsxs("div",{children:[s.jsx("strong",{children:"Supplier:"})," ",e.supplierName]}),s.jsxs("div",{children:[s.jsx("strong",{children:"Supplier Account ID:"})," ",e.supplierAccountId]}),s.jsxs("div",{children:[s.jsx("strong",{children:"Shipping Origin:"})," ",e.shippingOrigin]}),s.jsxs("div",{children:[s.jsx("strong",{children:"Original Product ID:"})," ",e.originalProductId]})]},r))]})]})]}),s.jsxs(i,{children:[s.jsx(t,{children:s.jsx(c,{children:"💾 LocalStorage Test"})}),s.jsxs(l,{className:"space-y-4",children:[s.jsx(a,{onClick:y,children:"Test LocalStorage in Console"}),s.jsxs("div",{className:"space-y-2 text-sm",children:[s.jsxs("div",{children:[s.jsx("strong",{children:"Cart Items:"})," ",localStorage.getItem("arouz_cart")?.length||0," characters"]}),s.jsxs("div",{children:[s.jsx("strong",{children:"Phone Session:"})," ",localStorage.getItem("phone_auth_session")?"Present":"Missing"]}),s.jsxs("div",{children:[s.jsx("strong",{children:"Consumer Profile:"})," ",localStorage.getItem("consumer_profile")?"Present":"Missing"]})]})]})]}),s.jsxs(i,{children:[s.jsx(t,{children:s.jsx(c,{children:"🗄️ Database Connection Test"})}),s.jsx(l,{className:"space-y-4",children:s.jsx(a,{onClick:()=>{console.log("🧪 [DEBUG] Testing database connection..."),D(async()=>{const{supabase:e}=await import("./index-BrxEt-yR.js").then(r=>r.db);return{supabase:e}},[]).then(({supabase:e})=>{e.from("products").select("id, name, supplier_name, shipping_origin").limit(5).then(({data:r,error:n})=>{n?console.error("🧪 [DEBUG] Database error:",n):console.log("🧪 [DEBUG] Database products:",r)})})},children:"Test Database Connection"})})]}),s.jsxs(i,{children:[s.jsx(t,{children:s.jsx(c,{children:"📋 Order Creation Test"})}),s.jsx(l,{className:"space-y-4",children:s.jsx(a,{onClick:()=>{console.log("🧪 [DEBUG] Testing order creation data transformation...");const e=o.map(r=>({product_id:r.originalProductId||r.id.toString(),product_name:r.name,quantity:r.quantity,unit_price:r.price,total_price:r.price*r.quantity,supplier_name:r.supplierName||"Unknown Supplier",supplier_wilaya:r.shippingOrigin||"",supplier_account_id:r.supplierAccountId||"",marketplace_section:r.marketplaceSection||"retail"}));console.log("🧪 [DEBUG] Transformed order items:",e)},children:"Test Order Data Transformation"})})]}),s.jsxs(i,{className:"bg-blue-50",children:[s.jsx(t,{children:s.jsx(c,{className:"text-blue-800",children:"📋 Debug Instructions"})}),s.jsx(l,{className:"text-blue-700",children:s.jsxs("ol",{className:"list-decimal list-inside space-y-2",children:[s.jsx("li",{children:"Open browser console (F12)"}),s.jsx("li",{children:'Click "Fetch Test Products" to see if products have shipping origins'}),s.jsx("li",{children:"Add products to cart from marketplace"}),s.jsx("li",{children:'Click "Test Cart Data" to verify cart items have shipping origins'}),s.jsx("li",{children:"Go to checkout and check if shipping origins display correctly"}),s.jsx("li",{children:"Use the debug section in checkout for detailed analysis"})]})})]})]})}export{S as DebugPage};
