import{u as k,a as F,r as n,d8 as R,j as e,L as N,ah as b,I as v,aX as y,aq as P,d7 as D,B as I,i as E,U as M,d9 as T,ag as q,a1 as S}from"./index-BrxEt-yR.js";function B({onComplete:a}){const{t:s}=k(),{toast:i}=F(),[t,x]=n.useState(""),[r,p]=n.useState(""),[o,f]=n.useState(!1),[u,d]=n.useState(!1),[l,c]=n.useState(!1),[m,C]=n.useState(!1),g=R(t),L=async h=>{if(h.preventDefault(),!(!t||!r||l)){if(t!==r){i({title:s("auth.passwordMismatch"),description:s("auth.passwordMismatchDescription"),variant:"destructive"});return}if(!g.isValid){i({title:s("auth.passwordTooWeak"),description:s("auth.passwordRequirements"),variant:"destructive"});return}c(!0);try{const{success:w,error:j}=await T(t);if(j){i({title:s("auth.resetPasswordFailed"),description:j.message,variant:"destructive"}),c(!1);return}w&&(C(!0),i({title:s("auth.passwordResetSuccess"),description:s("auth.passwordResetSuccessDescription")}),setTimeout(()=>{a()},2e3))}catch(w){console.error("Error resetting password:",w),i({title:s("auth.resetPasswordFailed"),description:s("auth.unexpectedError"),variant:"destructive"})}finally{c(!1)}}};return e.jsx("div",{className:"space-y-4",children:m?e.jsxs("div",{className:"text-center space-y-4",children:[e.jsx("div",{className:"flex justify-center",children:e.jsx(M,{className:"h-12 w-12 text-green-500"})}),e.jsx("h2",{className:"text-lg font-semibold",children:s("auth.passwordResetSuccess")}),e.jsx("p",{className:"text-sm text-muted-foreground",children:s("auth.passwordResetSuccessDescription")})]}):e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"text-center mb-6",children:[e.jsx("h2",{className:"text-lg font-semibold",children:s("auth.resetPassword")}),e.jsx("p",{className:"text-sm text-muted-foreground mt-1",children:s("auth.resetPasswordDescription")})]}),e.jsxs("form",{onSubmit:L,className:"space-y-4",children:[e.jsxs("div",{className:"space-y-1.5",children:[e.jsx(N,{htmlFor:"password",className:"text-sm font-medium",children:s("auth.newPassword")}),e.jsxs("div",{className:"relative",children:[e.jsx("div",{className:"absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none text-gray-500",children:e.jsx(b,{className:"h-4 w-4"})}),e.jsx(v,{id:"password",type:o?"text":"password",value:t,onChange:h=>x(h.target.value),placeholder:s("auth.enterNewPassword"),className:"w-full pl-10 pr-10 h-10",required:!0}),e.jsx("button",{type:"button",className:"absolute inset-y-0 right-0 flex items-center pr-3 text-gray-500",onClick:()=>f(!o),children:o?e.jsx(y,{className:"h-4 w-4"}):e.jsx(P,{className:"h-4 w-4"})})]}),e.jsx(D,{password:t,className:"mt-2"})]}),e.jsxs("div",{className:"space-y-1.5",children:[e.jsx(N,{htmlFor:"confirmPassword",className:"text-sm font-medium",children:s("auth.confirmPassword")}),e.jsxs("div",{className:"relative",children:[e.jsx("div",{className:"absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none text-gray-500",children:e.jsx(b,{className:"h-4 w-4"})}),e.jsx(v,{id:"confirmPassword",type:u?"text":"password",value:r,onChange:h=>p(h.target.value),placeholder:s("auth.confirmNewPassword"),className:"w-full pl-10 pr-10 h-10",required:!0}),e.jsx("button",{type:"button",className:"absolute inset-y-0 right-0 flex items-center pr-3 text-gray-500",onClick:()=>d(!u),children:u?e.jsx(y,{className:"h-4 w-4"}):e.jsx(P,{className:"h-4 w-4"})})]}),t&&r&&t!==r&&e.jsx("p",{className:"text-xs text-red-500 mt-1",children:s("auth.passwordMismatchDescription")})]}),e.jsx(I,{type:"submit",disabled:!t||!r||t!==r||!g.isValid||l,className:"w-full bg-[#fa7b00] hover:bg-[#fa7b00]/90 text-white font-semibold py-2.5 rounded-lg",children:l?e.jsxs(e.Fragment,{children:[e.jsx(E,{className:"mr-2 h-4 w-4 animate-spin"}),s("auth.processing")]}):s("auth.resetPassword")})]})]})})}const U=()=>{const{t:a}=k(),s=q(),[i,t]=n.useState(!0),[x,r]=n.useState(!1),[p,o]=n.useState(null);n.useEffect(()=>{(async()=>{try{const{data:d,error:l}=await S.auth.getSession();if(l){console.error("Error checking session:",l),o(a("auth.resetPasswordFailed")),t(!1);return}if(d.session){r(!0),t(!1);return}if(window.location.hash)try{const{data:c,error:m}=await S.auth.exchangeCodeForSession(window.location.hash.substring(1));if(m){console.error("Error exchanging hash for session:",m),o(a("auth.linkExpired")),t(!1);return}if(c.session){r(!0),t(!1);return}}catch(c){console.error("Error processing hash:",c),o(a("auth.unexpectedError")),t(!1);return}o(a("auth.invalidResetLink")),t(!1)}catch(d){console.error("Unexpected error in reset password page:",d),o(a("auth.unexpectedError")),t(!1)}})()},[a]);const f=()=>{s("/partners")};return e.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gray-50",children:e.jsx("div",{className:"w-full max-w-md p-8 bg-white rounded-lg shadow-md",children:i?e.jsxs("div",{className:"flex flex-col items-center justify-center py-12",children:[e.jsx(E,{className:"h-12 w-12 text-[#fa7b00] animate-spin mb-4"}),e.jsx("p",{className:"text-gray-600",children:a("auth.loading")})]}):x?e.jsxs(e.Fragment,{children:[e.jsx("h1",{className:"text-2xl font-bold text-center mb-6",children:a("auth.resetPassword")}),e.jsx(B,{onComplete:f})]}):e.jsxs("div",{className:"text-center py-8",children:[e.jsx("div",{className:"h-12 w-12 rounded-full bg-red-100 flex items-center justify-center mx-auto mb-4",children:e.jsx("svg",{className:"h-8 w-8 text-red-600",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})}),e.jsx("h1",{className:"text-xl font-semibold text-gray-800 mb-2",children:a("auth.resetPasswordFailed")}),e.jsx("p",{className:"text-gray-600 mb-6",children:p||a("auth.unexpectedError")}),e.jsx("button",{onClick:()=>s("/partners"),className:"px-6 py-2 bg-[#fa7b00] text-white rounded-lg hover:bg-[#fa7b00]/90 transition-colors",children:a("auth.returnToPartners")})]})})})};export{U as default};
