import{c as ie,u as M,a as U,bb as Q,r as t,j as e,i as w,a9 as y,bz as re,bA as ne,bB as le,B as D,L as c,_ as G,I as j,bl as Z,a5 as ee,a3 as ce,au as se,bm as oe,bH as de,d6 as me,ah as q,aX as R,aq as B,d7 as xe,O as te,ab as he,d8 as ge,d9 as ue,da as pe,H as je,w as fe,M as O,c3 as $,N as ve}from"./index-BrxEt-yR.js";import{B as ae,A as Ne}from"./AdminLayout-CXacme7h.js";import{U as ye}from"./upload-C1XlCzBf.js";import{P as we}from"./phone-rG_ZX172.js";import{S as T}from"./switch-BPvkSFYb.js";import{T as be}from"./tag-Cdb2Tczt.js";import{S as H,a as W,b as X,c as J,d as C}from"./select-BkcxmIo5.js";import{R as K,a as P}from"./radio-group-B1nv8luK.js";import{C as Se}from"./calendar-apcLFglb.js";import{S as Ce}from"./settings-3dK106XO.js";import"./house-CB53rBf_.js";/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Pe=ie("Monitor",[["rect",{width:"20",height:"14",x:"2",y:"3",rx:"2",key:"48i651"}],["line",{x1:"8",x2:"16",y1:"21",y2:"21",key:"1svkeh"}],["line",{x1:"12",x2:"12",y1:"17",y2:"21",key:"vw1qmm"}]]);function De(){const{t:s}=M(),{toast:i}=U(),{isSupplier:m,isMerchant:n}=Q(),[r,g]=t.useState(!1),[o,p]=t.useState(!1),[l,x]=t.useState(null),[h,u]=t.useState(""),[N,F]=t.useState(""),[b,S]=t.useState(""),[Y,I]=t.useState(""),[L,k]=t.useState(""),[E,A]=t.useState("");t.useEffect(()=>{(async()=>{g(!0);try{const{data:f}=await oe();if(f.user){const{profile:a,error:v}=await de(f.user.id);if(v){console.error("Error loading profile:",v),i({title:s("settings.errorLoadingProfile"),description:v.message,variant:"destructive"});return}a&&(x(a),u(a.full_name||""),F(a.email||""),S(a.phone||""),I(a.company_name||""),k(a.store_name||""),A(a.store_address||""))}}catch(f){console.error("Error loading user:",f),i({title:s("settings.errorLoadingProfile"),description:s("settings.unexpectedError"),variant:"destructive"})}finally{g(!1)}})()},[s,i]);const V=async()=>{if(l){p(!0);try{const d={full_name:h,company_name:m()?Y:void 0,store_name:n()?L:void 0,store_address:n()?E:void 0},{success:f,error:a}=await me(l.id,d);if(a){console.error("Error updating profile:",a),i({title:s("settings.errorSavingProfile"),description:a.message,variant:"destructive"});return}f&&i({title:s("settings.profileSaved"),description:s("settings.profileSavedDescription")})}catch(d){console.error("Unexpected error updating profile:",d),i({title:s("settings.errorSavingProfile"),description:s("settings.unexpectedError"),variant:"destructive"})}finally{p(!1)}}};return r?e.jsx("div",{className:"flex justify-center items-center py-12",children:e.jsx(w,{className:"h-8 w-8 animate-spin text-primary"})}):e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-medium",children:s("settings.profileInformation")}),e.jsx("p",{className:"text-sm text-muted-foreground mt-1",children:s("settings.profileInformationDescription")})]}),e.jsx(y,{}),e.jsxs("div",{className:"flex flex-col md:flex-row gap-8",children:[e.jsx("div",{className:"md:w-1/3",children:e.jsxs("div",{className:"flex flex-col items-center gap-4",children:[e.jsxs(re,{className:"h-24 w-24 border-2 border-electric-orange/20 shadow-sm",children:[e.jsx(ne,{src:"",alt:"Profile"}),e.jsx(le,{className:"bg-gradient-to-br from-midnight-blue to-primary text-white text-xl font-bold",children:h?h.charAt(0).toUpperCase():"U"})]}),e.jsxs(D,{variant:"outline",size:"sm",className:"gap-2",children:[e.jsx(ye,{className:"h-4 w-4"}),s("settings.uploadPhoto")]}),e.jsx("p",{className:"text-xs text-center text-muted-foreground max-w-[200px]",children:s("settings.photoRequirements")})]})}),e.jsxs("div",{className:"md:w-2/3 space-y-4",children:[e.jsxs("div",{className:"space-y-1.5",children:[e.jsx(c,{htmlFor:"fullName",children:s("settings.fullName")}),e.jsxs("div",{className:"relative",children:[e.jsx("div",{className:"absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none text-gray-500",children:e.jsx(G,{className:"h-4 w-4"})}),e.jsx(j,{id:"fullName",value:h,onChange:d=>u(d.target.value),className:"pl-10"})]})]}),m()&&e.jsxs("div",{className:"space-y-1.5",children:[e.jsx(c,{htmlFor:"companyName",children:s("settings.companyName")}),e.jsxs("div",{className:"relative",children:[e.jsx("div",{className:"absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none text-gray-500",children:e.jsx(Z,{className:"h-4 w-4"})}),e.jsx(j,{id:"companyName",value:Y,onChange:d=>I(d.target.value),className:"pl-10"})]})]}),n()&&e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"space-y-1.5",children:[e.jsx(c,{htmlFor:"storeName",children:s("settings.storeName")}),e.jsxs("div",{className:"relative",children:[e.jsx("div",{className:"absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none text-gray-500",children:e.jsx(ee,{className:"h-4 w-4"})}),e.jsx(j,{id:"storeName",value:L,onChange:d=>k(d.target.value),className:"pl-10"})]})]}),e.jsxs("div",{className:"space-y-1.5",children:[e.jsx(c,{htmlFor:"storeAddress",children:s("settings.storeAddress")}),e.jsxs("div",{className:"relative",children:[e.jsx("div",{className:"absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none text-gray-500",children:e.jsx(ce,{className:"h-4 w-4"})}),e.jsx(j,{id:"storeAddress",value:E,onChange:d=>A(d.target.value),className:"pl-10"})]})]})]}),e.jsxs("div",{className:"space-y-1.5",children:[e.jsx(c,{htmlFor:"email",children:s("settings.email")}),e.jsxs("div",{className:"relative",children:[e.jsx("div",{className:"absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none text-gray-500",children:e.jsx(se,{className:"h-4 w-4"})}),e.jsx(j,{id:"email",value:N,readOnly:!0,className:"pl-10 bg-gray-50"})]}),e.jsx("p",{className:"text-xs text-muted-foreground",children:s("settings.emailChangeDescription")})]}),e.jsxs("div",{className:"space-y-1.5",children:[e.jsx(c,{htmlFor:"phone",children:s("settings.phone")}),e.jsxs("div",{className:"relative",children:[e.jsx("div",{className:"absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none text-gray-500",children:e.jsx(we,{className:"h-4 w-4"})}),e.jsx(j,{id:"phone",value:b,readOnly:!0,className:"pl-10 bg-gray-50"})]}),e.jsx("p",{className:"text-xs text-muted-foreground",children:s("settings.phoneChangeDescription")})]}),e.jsx("div",{className:"pt-4",children:e.jsx(D,{onClick:V,disabled:o,className:"bg-electric-orange hover:bg-electric-orange/90",children:o?e.jsxs(e.Fragment,{children:[e.jsx(w,{className:"mr-2 h-4 w-4 animate-spin"}),s("settings.saving")]}):s("settings.saveChanges")})})]})]})]})}function Fe(){const{t:s}=M(),{toast:i}=U(),[m,n]=t.useState(""),[r,g]=t.useState(""),[o,p]=t.useState(""),[l,x]=t.useState(!1),[h,u]=t.useState(!1),[N,F]=t.useState(!1),[b,S]=t.useState(!1),[Y,I]=t.useState(!1),[L,k]=t.useState(!1),[E,A]=t.useState(!1),V=async a=>{if(a.preventDefault(),r!==o){i({title:s("settings.passwordMismatch"),description:s("settings.passwordMismatchDescription"),variant:"destructive"});return}if(!ge(r).isValid){i({title:s("settings.passwordTooWeak"),description:s("settings.passwordRequirements"),variant:"destructive"});return}S(!0);try{const{success:_,error:z}=await ue(r);if(z){console.error("Error updating password:",z),i({title:s("settings.passwordChangeFailed"),description:z.message,variant:"destructive"});return}_&&(i({title:s("settings.passwordChanged"),description:s("settings.passwordChangedDescription")}),n(""),g(""),p(""))}catch(_){console.error("Unexpected error updating password:",_),i({title:s("settings.passwordChangeFailed"),description:s("settings.unexpectedError"),variant:"destructive"})}finally{S(!1)}},d=async a=>{k(!0),setTimeout(()=>{I(a),k(!1),i({title:s(a?"settings.twoFactorEnabled":"settings.twoFactorDisabled"),description:s(a?"settings.twoFactorEnabledDescription":"settings.twoFactorDisabledDescription")})},1e3)},f=async()=>{A(!0);try{const{success:a,error:v}=await pe("<EMAIL>");if(v){console.error("Error sending verification email:",v),i({title:s("settings.verificationEmailFailed"),description:v.message,variant:"destructive"});return}a&&i({title:s("settings.verificationEmailSent"),description:s("settings.verificationEmailSentDescription")})}catch(a){console.error("Unexpected error sending verification email:",a),i({title:s("settings.verificationEmailFailed"),description:s("settings.unexpectedError"),variant:"destructive"})}finally{A(!1)}};return e.jsxs("div",{className:"space-y-8",children:[e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-medium",children:s("settings.securitySettings")}),e.jsx("p",{className:"text-sm text-muted-foreground mt-1",children:s("settings.securitySettingsDescription")})]}),e.jsx(y,{}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("h4",{className:"text-md font-medium",children:s("settings.changePassword")}),e.jsx("p",{className:"text-sm text-muted-foreground mt-1",children:s("settings.changePasswordDescription")})]}),e.jsxs("form",{onSubmit:V,className:"space-y-4 max-w-md",children:[e.jsxs("div",{className:"space-y-1.5",children:[e.jsx(c,{htmlFor:"currentPassword",children:s("settings.currentPassword")}),e.jsxs("div",{className:"relative",children:[e.jsx("div",{className:"absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none text-gray-500",children:e.jsx(q,{className:"h-4 w-4"})}),e.jsx(j,{id:"currentPassword",type:l?"text":"password",value:m,onChange:a=>n(a.target.value),className:"pl-10 pr-10",required:!0}),e.jsx("button",{type:"button",className:"absolute inset-y-0 right-0 flex items-center pr-3 text-gray-500",onClick:()=>x(!l),children:l?e.jsx(R,{className:"h-4 w-4"}):e.jsx(B,{className:"h-4 w-4"})})]})]}),e.jsxs("div",{className:"space-y-1.5",children:[e.jsx(c,{htmlFor:"newPassword",children:s("settings.newPassword")}),e.jsxs("div",{className:"relative",children:[e.jsx("div",{className:"absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none text-gray-500",children:e.jsx(q,{className:"h-4 w-4"})}),e.jsx(j,{id:"newPassword",type:h?"text":"password",value:r,onChange:a=>g(a.target.value),className:"pl-10 pr-10",required:!0}),e.jsx("button",{type:"button",className:"absolute inset-y-0 right-0 flex items-center pr-3 text-gray-500",onClick:()=>u(!h),children:h?e.jsx(R,{className:"h-4 w-4"}):e.jsx(B,{className:"h-4 w-4"})})]}),r&&e.jsx(xe,{password:r,className:"mt-2"})]}),e.jsxs("div",{className:"space-y-1.5",children:[e.jsx(c,{htmlFor:"confirmPassword",children:s("settings.confirmPassword")}),e.jsxs("div",{className:"relative",children:[e.jsx("div",{className:"absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none text-gray-500",children:e.jsx(q,{className:"h-4 w-4"})}),e.jsx(j,{id:"confirmPassword",type:N?"text":"password",value:o,onChange:a=>p(a.target.value),className:"pl-10 pr-10",required:!0}),e.jsx("button",{type:"button",className:"absolute inset-y-0 right-0 flex items-center pr-3 text-gray-500",onClick:()=>F(!N),children:N?e.jsx(R,{className:"h-4 w-4"}):e.jsx(B,{className:"h-4 w-4"})})]}),r&&o&&r!==o&&e.jsx("p",{className:"text-xs text-red-500 mt-1",children:s("settings.passwordMismatchDescription")})]}),e.jsx(D,{type:"submit",disabled:b||!m||!r||!o,className:"bg-electric-orange hover:bg-electric-orange/90",children:b?e.jsxs(e.Fragment,{children:[e.jsx(w,{className:"mr-2 h-4 w-4 animate-spin"}),s("settings.changing")]}):s("settings.updatePassword")})]})]}),e.jsx(y,{}),e.jsx("div",{className:"space-y-4",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("h4",{className:"text-md font-medium",children:s("settings.twoFactorAuthentication")}),e.jsx("p",{className:"text-sm text-muted-foreground mt-1",children:s("settings.twoFactorAuthenticationDescription")})]}),e.jsx("div",{className:"flex items-center gap-2",children:L?e.jsx(w,{className:"h-4 w-4 animate-spin"}):e.jsx(T,{checked:Y,onCheckedChange:d})})]})}),e.jsx(y,{}),e.jsx("div",{className:"space-y-4",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("h4",{className:"text-md font-medium",children:s("settings.emailVerification")}),e.jsx("p",{className:"text-sm text-muted-foreground mt-1",children:s("settings.emailVerificationDescription")})]}),e.jsxs(D,{variant:"outline",size:"sm",onClick:f,disabled:E,className:"gap-2",children:[E?e.jsx(w,{className:"h-4 w-4 animate-spin"}):e.jsx(te,{className:"h-4 w-4"}),s("settings.verifyEmail")]})]})}),e.jsx(y,{}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("h4",{className:"text-md font-medium",children:s("settings.accountActivity")}),e.jsx("p",{className:"text-sm text-muted-foreground mt-1",children:s("settings.accountActivityDescription")})]}),e.jsxs("div",{className:"bg-amber-50 border border-amber-200 rounded-md p-4 flex gap-3 items-start max-w-md",children:[e.jsx(he,{className:"h-5 w-5 text-amber-600 flex-shrink-0 mt-0.5"}),e.jsxs("div",{children:[e.jsx("h5",{className:"text-sm font-medium text-amber-800",children:s("settings.securityTip")}),e.jsx("p",{className:"text-xs text-amber-700 mt-1",children:s("settings.securityTipDescription")})]})]})]})]})}function ke(){const{t:s}=M(),{toast:i}=U(),[m,n]=t.useState(!1),[r,g]=t.useState([{id:"order_updates",title:s("settings.orderUpdates"),description:s("settings.orderUpdatesDescription"),email:!0,push:!0,sms:!1,icon:e.jsx(je,{className:"h-5 w-5 text-gray-500"})},{id:"price_alerts",title:s("settings.priceAlerts"),description:s("settings.priceAlertsDescription"),email:!0,push:!1,sms:!1,icon:e.jsx(be,{className:"h-5 w-5 text-gray-500"})},{id:"inventory_alerts",title:s("settings.inventoryAlerts"),description:s("settings.inventoryAlertsDescription"),email:!0,push:!0,sms:!0,icon:e.jsx(fe,{className:"h-5 w-5 text-gray-500"})},{id:"messages",title:s("settings.messages"),description:s("settings.messagesDescription"),email:!0,push:!0,sms:!1,icon:e.jsx(O,{className:"h-5 w-5 text-gray-500"})}]),o=(l,x,h)=>{g(r.map(u=>u.id===l?{...u,[x]:h}:u))},p=async()=>{n(!0),setTimeout(()=>{n(!1),i({title:s("settings.notificationSettingsSaved"),description:s("settings.notificationSettingsSavedDescription")})},1e3)};return e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-medium",children:s("settings.notificationSettings")}),e.jsx("p",{className:"text-sm text-muted-foreground mt-1",children:s("settings.notificationSettingsDescription")})]}),e.jsx(y,{}),e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"grid grid-cols-4 gap-4 pb-2",children:[e.jsx("div",{}),e.jsx("div",{className:"text-sm font-medium text-center",children:e.jsxs("div",{className:"flex items-center justify-center gap-2",children:[e.jsx(se,{className:"h-4 w-4"}),s("settings.email")]})}),e.jsx("div",{className:"text-sm font-medium text-center",children:e.jsxs("div",{className:"flex items-center justify-center gap-2",children:[e.jsx(ae,{className:"h-4 w-4"}),s("settings.push")]})}),e.jsx("div",{className:"text-sm font-medium text-center",children:e.jsxs("div",{className:"flex items-center justify-center gap-2",children:[e.jsx(O,{className:"h-4 w-4"}),s("settings.sms")]})})]}),r.map(l=>e.jsxs("div",{className:"grid grid-cols-4 gap-4 py-4 border-t",children:[e.jsxs("div",{className:"flex items-start gap-3",children:[e.jsx("div",{className:"mt-0.5",children:l.icon}),e.jsxs("div",{children:[e.jsx("h4",{className:"text-sm font-medium",children:l.title}),e.jsx("p",{className:"text-xs text-muted-foreground mt-1",children:l.description})]})]}),e.jsx("div",{className:"flex justify-center items-center",children:e.jsx(T,{checked:l.email,onCheckedChange:x=>o(l.id,"email",x)})}),e.jsx("div",{className:"flex justify-center items-center",children:e.jsx(T,{checked:l.push,onCheckedChange:x=>o(l.id,"push",x)})}),e.jsx("div",{className:"flex justify-center items-center",children:e.jsx(T,{checked:l.sms,onCheckedChange:x=>o(l.id,"sms",x)})})]},l.id))]}),e.jsx("div",{className:"pt-4",children:e.jsx(D,{onClick:p,disabled:m,className:"bg-electric-orange hover:bg-electric-orange/90",children:m?e.jsxs(e.Fragment,{children:[e.jsx(w,{className:"mr-2 h-4 w-4 animate-spin"}),s("settings.saving")]}):s("settings.saveChanges")})})]})}function Ee(){const{t:s,i18n:i}=M(),{toast:m}=U(),[n,r]=t.useState(!1),[g,o]=t.useState(i.language||"en"),[p,l]=t.useState("Africa/Algiers"),[x,h]=t.useState("light"),[u,N]=t.useState("DD/MM/YYYY"),[F,b]=t.useState(!0),S=async()=>{r(!0),g!==i.language&&await i.changeLanguage(g),setTimeout(()=>{r(!1),m({title:s("settings.preferencesSaved"),description:s("settings.preferencesSavedDescription")})},1e3)};return e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-medium",children:s("settings.accountPreferences")}),e.jsx("p",{className:"text-sm text-muted-foreground mt-1",children:s("settings.accountPreferencesDescription")})]}),e.jsx(y,{}),e.jsxs("div",{className:"space-y-6 max-w-md",children:[e.jsxs("div",{className:"space-y-3",children:[e.jsxs("div",{className:"flex items-start gap-3",children:[e.jsx($,{className:"h-5 w-5 text-gray-500 mt-0.5"}),e.jsxs("div",{children:[e.jsx(c,{htmlFor:"language",className:"text-sm font-medium",children:s("settings.language")}),e.jsx("p",{className:"text-xs text-muted-foreground mt-1",children:s("settings.languageDescription")})]})]}),e.jsxs(H,{value:g,onValueChange:o,children:[e.jsx(W,{id:"language",className:"w-full",children:e.jsx(X,{placeholder:s("settings.selectLanguage")})}),e.jsxs(J,{children:[e.jsx(C,{value:"en",children:"English"}),e.jsx(C,{value:"fr",children:"Français"}),e.jsx(C,{value:"ar",children:"العربية"})]})]})]}),e.jsxs("div",{className:"space-y-3",children:[e.jsxs("div",{className:"flex items-start gap-3",children:[e.jsx(ve,{className:"h-5 w-5 text-gray-500 mt-0.5"}),e.jsxs("div",{children:[e.jsx(c,{htmlFor:"timezone",className:"text-sm font-medium",children:s("settings.timezone")}),e.jsx("p",{className:"text-xs text-muted-foreground mt-1",children:s("settings.timezoneDescription")})]})]}),e.jsxs(H,{value:p,onValueChange:l,children:[e.jsx(W,{id:"timezone",className:"w-full",children:e.jsx(X,{placeholder:s("settings.selectTimezone")})}),e.jsxs(J,{children:[e.jsx(C,{value:"Africa/Algiers",children:"Africa/Algiers (GMT+1)"}),e.jsx(C,{value:"Europe/Paris",children:"Europe/Paris (GMT+2)"}),e.jsx(C,{value:"Europe/London",children:"Europe/London (GMT+0)"})]})]})]}),e.jsxs("div",{className:"space-y-3",children:[e.jsxs("div",{className:"flex items-start gap-3",children:[e.jsx(Se,{className:"h-5 w-5 text-gray-500 mt-0.5"}),e.jsxs("div",{children:[e.jsx(c,{className:"text-sm font-medium",children:s("settings.dateFormat")}),e.jsx("p",{className:"text-xs text-muted-foreground mt-1",children:s("settings.dateFormatDescription")})]})]}),e.jsxs(K,{value:u,onValueChange:N,className:"flex flex-col space-y-1",children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(P,{value:"DD/MM/YYYY",id:"date-format-1"}),e.jsx(c,{htmlFor:"date-format-1",className:"text-sm",children:"DD/MM/YYYY"})]}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(P,{value:"MM/DD/YYYY",id:"date-format-2"}),e.jsx(c,{htmlFor:"date-format-2",className:"text-sm",children:"MM/DD/YYYY"})]}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(P,{value:"YYYY-MM-DD",id:"date-format-3"}),e.jsx(c,{htmlFor:"date-format-3",className:"text-sm",children:"YYYY-MM-DD"})]})]})]}),e.jsxs("div",{className:"space-y-3",children:[e.jsxs("div",{className:"flex items-start gap-3",children:[e.jsx(Pe,{className:"h-5 w-5 text-gray-500 mt-0.5"}),e.jsxs("div",{children:[e.jsx(c,{className:"text-sm font-medium",children:s("settings.theme")}),e.jsx("p",{className:"text-xs text-muted-foreground mt-1",children:s("settings.themeDescription")})]})]}),e.jsxs(K,{value:x,onValueChange:h,className:"flex flex-col space-y-1",children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(P,{value:"light",id:"theme-light"}),e.jsx(c,{htmlFor:"theme-light",className:"text-sm",children:s("settings.lightTheme")})]}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(P,{value:"dark",id:"theme-dark"}),e.jsx(c,{htmlFor:"theme-dark",className:"text-sm",children:s("settings.darkTheme")})]}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(P,{value:"system",id:"theme-system"}),e.jsx(c,{htmlFor:"theme-system",className:"text-sm",children:s("settings.systemTheme")})]})]})]}),e.jsx("div",{className:"space-y-3",children:e.jsxs("div",{className:"flex items-start justify-between",children:[e.jsxs("div",{className:"flex items-start gap-3",children:[e.jsx($,{className:"h-5 w-5 text-gray-500 mt-0.5"}),e.jsxs("div",{children:[e.jsx(c,{className:"text-sm font-medium",children:s("settings.autoTranslate")}),e.jsx("p",{className:"text-xs text-muted-foreground mt-1",children:s("settings.autoTranslateDescription")})]})]}),e.jsx(T,{checked:F,onCheckedChange:b})]})})]}),e.jsx("div",{className:"pt-4",children:e.jsx(D,{onClick:S,disabled:n,className:"bg-electric-orange hover:bg-electric-orange/90",children:n?e.jsxs(e.Fragment,{children:[e.jsx(w,{className:"mr-2 h-4 w-4 animate-spin"}),s("settings.saving")]}):s("settings.saveChanges")})})]})}function Re(){const{t:s}=M(),{isSupplier:i,isMerchant:m}=Q(),[n,r]=t.useState("profile");return e.jsx(Ne,{children:e.jsxs("div",{className:"flex flex-col gap-6",children:[e.jsxs("div",{children:[e.jsx("h1",{className:"text-3xl font-bold tracking-tight",children:s("settings.title")}),e.jsx("p",{className:"text-muted-foreground mt-1",children:s("settings.description")})]}),e.jsxs("div",{className:"flex flex-col md:flex-row gap-8",children:[e.jsx("div",{className:"md:w-64 flex-shrink-0",children:e.jsxs("div",{className:"bg-white rounded-lg shadow-sm border p-4 sticky top-24",children:[e.jsxs("div",{className:"flex items-center gap-3 mb-6 pb-4 border-b",children:[e.jsx("div",{className:"w-12 h-12 rounded-full bg-gradient-to-br from-midnight-blue to-primary text-white font-bold flex items-center justify-center text-lg",children:i()?e.jsx(Z,{className:"h-6 w-6"}):m()?e.jsx(ee,{className:"h-6 w-6"}):e.jsx(G,{className:"h-6 w-6"})}),e.jsxs("div",{children:[e.jsx("h3",{className:"font-medium",children:i()?s("roles.supplierAndManufacturer"):m()?s("roles.merchantRetailer"):s("roles.user")}),e.jsx("p",{className:"text-sm text-muted-foreground",children:s("settings.manageYourAccount")})]})]}),e.jsxs("nav",{className:"space-y-1",children:[e.jsxs("button",{className:`w-full flex items-center gap-3 px-3 py-2 text-sm rounded-md ${n==="profile"?"bg-primary/10 text-primary font-medium":"text-muted-foreground hover:bg-muted"}`,onClick:()=>r("profile"),children:[e.jsx(G,{className:"h-4 w-4"}),s("settings.profile")]}),e.jsxs("button",{className:`w-full flex items-center gap-3 px-3 py-2 text-sm rounded-md ${n==="security"?"bg-primary/10 text-primary font-medium":"text-muted-foreground hover:bg-muted"}`,onClick:()=>r("security"),children:[e.jsx(te,{className:"h-4 w-4"}),s("settings.security")]}),e.jsxs("button",{className:`w-full flex items-center gap-3 px-3 py-2 text-sm rounded-md ${n==="notifications"?"bg-primary/10 text-primary font-medium":"text-muted-foreground hover:bg-muted"}`,onClick:()=>r("notifications"),children:[e.jsx(ae,{className:"h-4 w-4"}),s("settings.notifications")]}),e.jsxs("button",{className:`w-full flex items-center gap-3 px-3 py-2 text-sm rounded-md ${n==="preferences"?"bg-primary/10 text-primary font-medium":"text-muted-foreground hover:bg-muted"}`,onClick:()=>r("preferences"),children:[e.jsx(Ce,{className:"h-4 w-4"}),s("settings.preferences")]})]})]})}),e.jsx("div",{className:"flex-1",children:e.jsxs("div",{className:"bg-white rounded-lg shadow-sm border p-6",children:[n==="profile"&&e.jsx(De,{}),n==="security"&&e.jsx(Fe,{}),n==="notifications"&&e.jsx(ke,{}),n==="preferences"&&e.jsx(Ee,{})]})})]})]})})}export{Re as default};
