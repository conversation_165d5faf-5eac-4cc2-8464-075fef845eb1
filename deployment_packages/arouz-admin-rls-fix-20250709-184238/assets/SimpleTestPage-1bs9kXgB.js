import{q as r,j as e}from"./index-DnIZvqWt.js";function a(){const{orderId:s}=r();return console.log("🔍 [SIMPLE_TEST] Component mounted with orderId:",s),e.jsx("div",{className:"min-h-screen bg-gray-50 p-8",children:e.jsxs("div",{className:"max-w-4xl mx-auto",children:[e.jsx("h1",{className:"text-3xl font-bold text-gray-900 mb-6",children:"Simple Test Page"}),e.jsxs("div",{className:"bg-white rounded-lg shadow p-6",children:[e.jsx("h2",{className:"text-xl font-semibold mb-4",children:"Debug Information"}),e.jsxs("p",{className:"text-gray-600 mb-2",children:["Order ID: ",s]}),e.jsx("p",{className:"text-gray-600 mb-2",children:"Component Status: Mounted Successfully"}),e.jsx("p",{className:"text-gray-600 mb-2",children:"Check browser console for logs"}),e.jsx("div",{className:"mt-4 p-4 bg-green-50 border border-green-200 rounded",children:e.jsx("p",{className:"text-green-800",children:"✅ If you can see this page, the routing is working correctly!"})})]})]})})}export{a as default};
