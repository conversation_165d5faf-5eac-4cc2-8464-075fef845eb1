import{q as a,r as o,j as e,v as u,az as h}from"./index-DnIZvqWt.js";import{i as x}from"./productUtils-CGeH6jSk.js";import"./centralizedProductData-7Q4sAk9k.js";import"./idGenerator-8IGaQKCc.js";import"./categories-k_ueMvEL.js";function y(){const{productId:s}=a(),[r,i]=o.useState(null),[l,c]=o.useState(!0),[d,t]=o.useState(null);return o.useEffect(()=>{(async()=>{if(console.log(`[TEST] Starting fetch for: ${s}`),!s){t("No product ID provided"),c(!1);return}if(!x(s)){t("Invalid product ID format"),c(!1);return}try{t(null);const n=await h(s);console.log("[TEST] Product fetch result:",n),n?i(n):t(`Product not found: ${s}`)}catch(n){console.error("[TEST] Error:",n),t("Failed to load product")}finally{c(!1)}})()},[s]),e.jsx(u,{children:e.jsxs("div",{className:"container py-8",children:[e.jsx("h1",{className:"text-2xl font-bold mb-4",children:"Product Page Test"}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"p-4 bg-blue-50 rounded-lg",children:[e.jsx("h3",{className:"font-semibold mb-2",children:"Debug Info"}),e.jsxs("p",{children:[e.jsx("strong",{children:"Product ID:"})," ",s]}),e.jsxs("p",{children:[e.jsx("strong",{children:"Loading:"})," ",l?"true":"false"]}),e.jsxs("p",{children:[e.jsx("strong",{children:"Error:"})," ",d||"none"]}),e.jsxs("p",{children:[e.jsx("strong",{children:"Product Found:"})," ",r?"yes":"no"]})]}),l&&e.jsx("div",{className:"p-4 bg-yellow-50 rounded-lg",children:e.jsx("p",{children:"Loading product..."})}),d&&e.jsx("div",{className:"p-4 bg-red-50 rounded-lg",children:e.jsxs("p",{className:"text-red-800",children:["Error: ",d]})}),r&&e.jsxs("div",{className:"p-4 bg-green-50 rounded-lg",children:[e.jsx("h3",{className:"font-semibold mb-2",children:"Product Found!"}),e.jsxs("div",{className:"space-y-2 text-sm",children:[e.jsxs("p",{children:[e.jsx("strong",{children:"ID:"})," ",r.id]}),e.jsxs("p",{children:[e.jsx("strong",{children:"Name:"})," ",r.name]}),e.jsxs("p",{children:[e.jsx("strong",{children:"Category:"})," ",r.category]}),e.jsxs("p",{children:[e.jsx("strong",{children:"Manufacturer:"})," ",r.manufacturer]}),e.jsxs("p",{children:[e.jsx("strong",{children:"Stock:"})," ",r.stockQuantity]}),e.jsxs("p",{children:[e.jsx("strong",{children:"Min Order Qty:"})," ",r.minimumOrderQuantity||"not set"]}),e.jsxs("p",{children:[e.jsx("strong",{children:"Marketplace Section:"})," ",r.marketplaceSection||"not set"]}),e.jsxs("p",{children:[e.jsx("strong",{children:"Wholesale Tiers:"})," ",r.wholesalePricingTiers?r.wholesalePricingTiers.length:0]}),e.jsxs("p",{children:[e.jsx("strong",{children:"Retail Price:"})," ",r.retailPrice||"not set"]})]})]})]})]})})}export{y as default};
