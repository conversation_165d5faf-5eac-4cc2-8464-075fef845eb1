import{ag as Y,a as J,r as a,j as e,K as g,B as m,a6 as X,aY as ee,P as p,N as y,U as D,C as T,k as I,Q as A,aW as se,l as E,aw as ae,I as te,$ as ie,m as re,aq as ne,a3 as le}from"./index-DnIZvqWt.js";import{S as F,a as R,b as O,c as B,d as i}from"./select-DN5XplGq.js";import{S as L}from"./skeleton-iTtVbUqk.js";import{g as ce,l as de}from"./shippingCompanyAuthService-gV-D2DlT.js";import{g as oe,a as me}from"./shippingCompanyOrderService-Ne7ibXry.js";import{S as j}from"./StatsCard-CllgX-O9.js";import{P as ue}from"./phone-CUsbluw7.js";import{B as xe}from"./building-2-YMlv9Wto.js";import"./trending-up-DOWsPmVN.js";import"./trending-down-CtxiHcR2.js";function be(){const u=Y(),{toast:S}=J(),[r,he]=a.useState(ce()),[w,z]=a.useState([]),[c,H]=a.useState(null),[_,b]=a.useState(!0),[C,P]=a.useState(!1),[x,M]=a.useState(""),[d,V]=a.useState("all"),[o,G]=a.useState("all"),[n,f]=a.useState(1),[k,U]=a.useState(0),v=10;a.useEffect(()=>{if(!r){console.log("🚚 No valid session, redirecting to login..."),u("/shipping/login");return}console.log("🚚 Authenticated as:",r.company_name)},[r,u]);const N=async(s=!1)=>{if(r)try{s?P(!0):b(!0),console.log("📊 [SHIPPING_DASHBOARD] Loading orders and stats...");const t=await oe();t.success&&H(t.stats||null);const W={status:d==="all"?void 0:d,shipment_status:o==="all"?void 0:o,search:x||void 0,limit:v,offset:(n-1)*v},h=await me(W);h.success?(z(h.orders||[]),U(h.total_count||0)):S({title:"Failed to load orders",description:h.error||"Please try again",variant:"destructive"})}catch(t){console.error("❌ [SHIPPING_DASHBOARD] Error loading data:",t),S({title:"Failed to load data",description:"Please try again",variant:"destructive"})}finally{b(!1),P(!1)}};a.useEffect(()=>{r&&N()},[r,n,d,o]),a.useEffect(()=>{const s=setTimeout(()=>{n===1?N():f(1)},500);return()=>clearTimeout(s)},[x]);const Z=async()=>{try{await de()}catch(s){console.error("Error during logout:",s),u("/shipping/login")}},$=s=>new Intl.NumberFormat("fr-DZ",{style:"currency",currency:"DZD",minimumFractionDigits:0,maximumFractionDigits:0}).format(s),q=s=>new Date(s).toLocaleDateString("fr-DZ",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"}),K=s=>{switch(s){case"unassigned":return"secondary";case"assigned":return"default";case"picked_up":return"outline";case"in_transit":return"default";case"delivered":return"default";default:return"secondary"}},Q=s=>{switch(s){case"unassigned":return y;case"assigned":return p;case"picked_up":return g;case"in_transit":return g;case"delivered":return D;default:return y}},l=Math.ceil(k/v);return r?e.jsxs("div",{className:"min-h-screen bg-gray-50",children:[e.jsx("div",{className:"bg-white border-b border-gray-200 shadow-sm",children:e.jsx("div",{className:"container mx-auto px-4 py-4",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("div",{className:"w-12 h-12 bg-[#fa7b00] rounded-lg flex items-center justify-center",children:e.jsx(g,{className:"h-6 w-6 text-white"})}),e.jsxs("div",{children:[e.jsx("h1",{className:"text-2xl font-bold text-gray-900",children:r.company_name}),e.jsx("p",{className:"text-gray-600",children:"Shipping Portal"})]})]}),e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsxs(m,{onClick:()=>N(!0),disabled:C,variant:"outline",size:"sm",className:"flex items-center gap-2",children:[e.jsx(X,{className:`h-4 w-4 ${C?"animate-spin":""}`}),"Refresh"]}),e.jsxs(m,{onClick:Z,variant:"outline",size:"sm",className:"flex items-center gap-2 text-red-600 border-red-200 hover:bg-red-50",children:[e.jsx(ee,{className:"h-4 w-4"}),"Logout"]})]})]})})}),e.jsxs("div",{className:"container mx-auto px-4 py-6 space-y-6",children:[_?e.jsx("div",{className:"grid gap-4 md:grid-cols-2 lg:grid-cols-4",children:[...Array(4)].map((s,t)=>e.jsx(L,{className:"h-32"},t))}):c?e.jsxs("div",{className:"grid gap-4 md:grid-cols-2 lg:grid-cols-4",children:[e.jsx(j,{title:"Total Orders",value:c.total_assigned_orders,icon:e.jsx(p,{className:"h-6 w-6"}),description:"Assigned to you"}),e.jsx(j,{title:"Pending Pickup",value:c.pending_pickup,icon:e.jsx(y,{className:"h-6 w-6"}),description:"Ready for pickup"}),e.jsx(j,{title:"In Transit",value:c.in_transit,icon:e.jsx(g,{className:"h-6 w-6"}),description:"On the way"}),e.jsx(j,{title:"Delivered Today",value:c.delivered_today,icon:e.jsx(D,{className:"h-6 w-6"}),description:"Completed deliveries"})]}):null,e.jsxs(T,{children:[e.jsx(I,{children:e.jsxs(A,{className:"flex items-center gap-2",children:[e.jsx(se,{className:"h-5 w-5"}),"Filters & Search"]})}),e.jsx(E,{children:e.jsxs("div",{className:"flex flex-col md:flex-row gap-4",children:[e.jsx("div",{className:"flex-1",children:e.jsxs("div",{className:"relative",children:[e.jsx(ae,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),e.jsx(te,{placeholder:"Search by order number, customer name, or phone...",value:x,onChange:s=>M(s.target.value),className:"pl-10"})]})}),e.jsx("div",{className:"w-full md:w-48",children:e.jsxs(F,{value:d,onValueChange:V,children:[e.jsx(R,{children:e.jsx(O,{placeholder:"Order status"})}),e.jsxs(B,{children:[e.jsx(i,{value:"all",children:"All Orders"}),e.jsx(i,{value:"pending",children:"Pending"}),e.jsx(i,{value:"confirmed",children:"Confirmed"}),e.jsx(i,{value:"shipped",children:"Shipped"}),e.jsx(i,{value:"delivered",children:"Delivered"})]})]})}),e.jsx("div",{className:"w-full md:w-48",children:e.jsxs(F,{value:o,onValueChange:G,children:[e.jsx(R,{children:e.jsx(O,{placeholder:"Shipment status"})}),e.jsxs(B,{children:[e.jsx(i,{value:"all",children:"All Shipments"}),e.jsx(i,{value:"assigned",children:"Assigned"}),e.jsx(i,{value:"picked_up",children:"Picked Up"}),e.jsx(i,{value:"in_transit",children:"In Transit"}),e.jsx(i,{value:"delivered",children:"Delivered"})]})]})})]})})]}),e.jsxs(T,{children:[e.jsx(I,{children:e.jsxs(A,{className:"flex items-center justify-between",children:[e.jsxs("span",{className:"flex items-center gap-2",children:[e.jsx(p,{className:"h-5 w-5"}),"Assigned Orders (",k,")"]}),l>1&&e.jsxs("span",{className:"text-sm text-gray-500",children:["Page ",n," of ",l]})]})}),e.jsxs(E,{children:[_?e.jsx("div",{className:"space-y-4",children:[...Array(5)].map((s,t)=>e.jsx(L,{className:"h-24"},t))}):w.length===0?e.jsxs("div",{className:"text-center py-12",children:[e.jsx(p,{className:"h-16 w-16 text-gray-300 mx-auto mb-4"}),e.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No orders found"}),e.jsx("p",{className:"text-gray-600",children:x||d!=="all"||o!=="all"?"Try adjusting your filters or search terms":"Assigned orders will appear here"})]}):e.jsx("div",{className:"space-y-4",children:w.map(s=>{const t=Q(s.shipment_status||"unassigned");return e.jsxs(ie.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow",children:[e.jsxs("div",{className:"flex items-start justify-between mb-4",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx(t,{className:"h-6 w-6 text-[#fa7b00]"}),e.jsxs("div",{children:[e.jsx("h3",{className:"font-semibold text-lg",children:s.order_number}),e.jsx("p",{className:"text-sm text-gray-600",children:q(s.created_at)})]})]}),e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx(re,{variant:K(s.shipment_status||"unassigned"),children:(s.shipment_status||"unassigned").replace("_"," ").toUpperCase()}),e.jsxs(m,{onClick:()=>u(`/shipping/orders/${s.id}`),size:"sm",variant:"outline",className:"flex items-center gap-2",children:[e.jsx(ne,{className:"h-4 w-4"}),"View Details"]})]})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 mb-4",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(ue,{className:"h-4 w-4 text-gray-400"}),e.jsxs("span",{className:"text-sm",children:[s.consumer_name||"Consumer"," - ",s.consumer_phone]})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(le,{className:"h-4 w-4 text-gray-400"}),e.jsx("span",{className:"text-sm",children:s.delivery_wilaya})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(xe,{className:"h-4 w-4 text-gray-400"}),e.jsxs("span",{className:"text-sm",children:[s.supplier_contacts.length," supplier",s.supplier_contacts.length!==1?"s":""]})]})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsxs("span",{className:"text-sm text-gray-600",children:["Items: ",s.order_items.length]}),s.tracking_number&&e.jsxs("span",{className:"text-sm text-gray-600",children:["Tracking: ",s.tracking_number]})]}),e.jsxs("div",{className:"text-sm text-gray-600",children:["Total: ",$(s.total_amount)]})]})]},s.id)})}),l>1&&e.jsxs("div",{className:"flex items-center justify-center gap-2 mt-6",children:[e.jsx(m,{onClick:()=>f(s=>Math.max(1,s-1)),disabled:n===1,variant:"outline",size:"sm",children:"Previous"}),e.jsxs("span",{className:"text-sm text-gray-600",children:["Page ",n," of ",l]}),e.jsx(m,{onClick:()=>f(s=>Math.min(l,s+1)),disabled:n===l,variant:"outline",size:"sm",children:"Next"})]})]})]})]})]}):null}export{be as default};
