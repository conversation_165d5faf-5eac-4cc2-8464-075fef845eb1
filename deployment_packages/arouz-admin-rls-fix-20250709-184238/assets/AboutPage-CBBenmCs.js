import{u as o,j as e,v as d,C as a,l as t,aq as m,a3 as x,B as i,at as l,O as h}from"./index-DnIZvqWt.js";import{T as u}from"./target-CAk9TU7t.js";import{Z as p}from"./zap-Bm0cyP8_.js";import{U as g}from"./users-BW5JT5DS.js";import{T as b}from"./trending-up-DOWsPmVN.js";function w(){o();const n=[{icon:h,title:"Quality Assurance",description:"We ensure all products meet or exceed OEM specifications for your peace of mind."},{icon:p,title:"Innovation",description:"Leveraging cutting-edge technology to revolutionize the auto parts industry in Algeria."},{icon:g,title:"Customer First",description:"Our customers are at the heart of everything we do, driving our commitment to excellence."},{icon:b,title:"Growth",description:"Empowering local businesses and manufacturers to grow and thrive in the digital economy."}],c=[{number:"10,000+",label:"Products Available"},{number:"500+",label:"Partner Suppliers"},{number:"48",label:"Wilayas Covered"},{number:"24/7",label:"Customer Support"}];return e.jsx(d,{children:e.jsxs("div",{className:"container py-8",children:[e.jsxs("div",{className:"text-center mb-16",children:[e.jsx("h1",{className:"text-4xl md:text-5xl font-bold text-[#071c44] mb-6",children:"About AROUZ MARKET"}),e.jsx("p",{className:"text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed",children:"Algeria's First Intelligent Auto Parts Ecosystem connecting manufacturers, suppliers, merchants, and consumers in one unified platform."})]}),e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-8 mb-16",children:[e.jsx(a,{className:"bg-gradient-to-br from-[#071c44] to-[#071c44]/80 text-white",children:e.jsxs(t,{className:"p-8",children:[e.jsxs("div",{className:"flex items-center mb-6",children:[e.jsx(u,{className:"h-8 w-8 text-[#fa7b00] mr-3"}),e.jsx("h2",{className:"text-2xl font-bold",children:"Our Mission"})]}),e.jsx("p",{className:"text-gray-200 leading-relaxed",children:"To digitize and streamline the auto parts supply chain in Algeria, empowering local manufacturers and creating a more efficient marketplace that serves all stakeholders from suppliers to end consumers."})]})}),e.jsx(a,{className:"bg-gradient-to-br from-[#fa7b00] to-[#fa7b00]/80 text-white",children:e.jsxs(t,{className:"p-8",children:[e.jsxs("div",{className:"flex items-center mb-6",children:[e.jsx(m,{className:"h-8 w-8 text-white mr-3"}),e.jsx("h2",{className:"text-2xl font-bold",children:"Our Vision"})]}),e.jsx("p",{className:"text-orange-100 leading-relaxed",children:"To become the leading digital platform that transforms Algeria's auto parts industry, reducing import dependency and fostering a thriving local ecosystem built on innovation and efficiency."})]})})]}),e.jsxs("div",{className:"bg-gray-50 rounded-2xl p-8 mb-16",children:[e.jsx("h2",{className:"text-3xl font-bold text-[#071c44] text-center mb-8",children:"AROUZ MARKET by the Numbers"}),e.jsx("div",{className:"grid grid-cols-2 lg:grid-cols-4 gap-8",children:c.map((s,r)=>e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-3xl md:text-4xl font-bold text-[#fa7b00] mb-2",children:s.number}),e.jsx("div",{className:"text-gray-600 font-medium",children:s.label})]},r))})]}),e.jsxs("div",{className:"mb-16",children:[e.jsx("h2",{className:"text-3xl font-bold text-[#071c44] text-center mb-12",children:"Our Core Values"}),e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-8",children:n.map((s,r)=>e.jsx(a,{className:"hover:shadow-lg transition-shadow",children:e.jsx(t,{className:"p-6",children:e.jsxs("div",{className:"flex items-start space-x-4",children:[e.jsx("div",{className:"bg-[#fa7b00]/10 p-3 rounded-lg",children:e.jsx(s.icon,{className:"h-6 w-6 text-[#fa7b00]"})}),e.jsxs("div",{children:[e.jsx("h3",{className:"text-xl font-semibold text-[#071c44] mb-3",children:s.title}),e.jsx("p",{className:"text-gray-600 leading-relaxed",children:s.description})]})]})})},r))})]}),e.jsx("div",{className:"mb-16",children:e.jsx(a,{className:"bg-white border-0 shadow-lg",children:e.jsx(t,{className:"p-8 md:p-12",children:e.jsxs("div",{className:"max-w-4xl mx-auto",children:[e.jsx("h2",{className:"text-3xl font-bold text-[#071c44] mb-8 text-center",children:"Our Story"}),e.jsxs("div",{className:"prose prose-lg max-w-none text-gray-600",children:[e.jsx("p",{className:"mb-6",children:"AROUZ MARKET was born from a simple observation: Algeria's auto parts industry was fragmented, inefficient, and heavily dependent on imports. We saw an opportunity to create a unified digital ecosystem that would connect all stakeholders and streamline operations."}),e.jsx("p",{className:"mb-6",children:"Starting with a vision to digitize the entire supply chain, we built a platform that serves manufacturers, suppliers, merchants, and consumers alike. Our intelligent matching system, comprehensive inventory management, and seamless marketplace experience have transformed how auto parts are bought and sold in Algeria."}),e.jsx("p",{children:"Today, AROUZ MARKET stands as Algeria's premier auto parts platform, empowering local businesses, reducing costs, and providing consumers with unprecedented access to quality parts and services."})]})]})})})}),e.jsx("div",{className:"mb-16",children:e.jsx(a,{className:"bg-gradient-to-r from-[#071c44] to-[#071c44]/90 text-white",children:e.jsxs(t,{className:"p-8 text-center",children:[e.jsx(x,{className:"h-12 w-12 text-[#fa7b00] mx-auto mb-4"}),e.jsx("h2",{className:"text-2xl font-bold mb-4",children:"Proudly Algerian"}),e.jsx("p",{className:"text-gray-200 max-w-2xl mx-auto",children:"Based in Algiers, we serve customers across all 48 wilayas of Algeria, bringing the future of auto parts commerce to every corner of our nation."})]})})}),e.jsx("div",{className:"text-center",children:e.jsx(a,{className:"bg-[#fa7b00] text-white",children:e.jsxs(t,{className:"py-12",children:[e.jsx("h3",{className:"text-3xl font-bold mb-4",children:"Join the AROUZ MARKET Family"}),e.jsx("p",{className:"text-orange-100 mb-8 max-w-2xl mx-auto",children:"Whether you're a supplier, merchant, or consumer, become part of Algeria's most innovative auto parts ecosystem."}),e.jsxs("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[e.jsx(i,{className:"bg-white text-[#fa7b00] hover:bg-gray-100",asChild:!0,children:e.jsx(l,{to:"/partners",children:"Become a Partner"})}),e.jsx(i,{variant:"outline",className:"border-white text-white hover:bg-white hover:text-[#fa7b00]",asChild:!0,children:e.jsx(l,{to:"/contact",children:"Contact Us"})})]})]})})})]})})}export{w as default};
