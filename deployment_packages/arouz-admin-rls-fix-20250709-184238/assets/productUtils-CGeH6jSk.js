import{a as h}from"./centralizedProductData-7Q4sAk9k.js";import{extractMarketplaceSectionFromId as f,isValidProductId as m}from"./idGenerator-8IGaQKCc.js";h();const P=e=>{if(!e)return{description:"",specifications:{}};const i=e.trim(),s=i.match(/Part 1[:\-\s]*Product Description[:\-\s]*(.*?)(?=Part 2|$)/is),a=i.match(/Part 2[:\-\s]*Specifications[:\-\s]*(.*?)$/is);if(s&&a){const t=s[1].trim(),n=a[1].trim(),c={};return n.split(`
`).filter(r=>r.trim()).forEach(r=>{const l=r.indexOf(":");if(l>0){const o=r.substring(0,l).trim().replace(/^-\s*/,""),g=r.substring(l+1).trim();o&&g&&(c[o]=g)}}),{description:t,specifications:c}}return{description:i,specifications:{}}},S=e=>{console.log("🔍 [MARKETPLACE_SECTION] Analyzing product:",{id:e.id,name:e.name,hasMarketplaceSectionProperty:"marketplaceSection"in e,marketplaceSectionValue:e.marketplaceSection});const i=f(e.id);if(console.log("🔍 [MARKETPLACE_SECTION] ID suffix extraction:",{productId:e.id,endsWithWholesale:e.id.endsWith("-WHOLESALE"),endsWithRetail:e.id.endsWith("-RETAIL"),extractedSection:i}),i)return console.log("✅ [MARKETPLACE_SECTION] Using ID suffix (PRIORITY 1):",i),i;const s=e.wholesalePricingTiers&&e.wholesalePricingTiers.length>0,a=!!e.retailPrice;return console.log("🔍 [MARKETPLACE_SECTION] Pricing structure analysis:",{hasWholesalePricing:s,wholesalePricingTiersCount:e.wholesalePricingTiers?.length||0,hasRetailPrice:a,retailPriceValue:e.retailPrice}),s&&!a?(console.log("✅ [MARKETPLACE_SECTION] Using pricing structure (PRIORITY 2): wholesale (has wholesale tiers, no retail price)"),"wholesale"):a&&!s?(console.log("✅ [MARKETPLACE_SECTION] Using pricing structure (PRIORITY 2): retail (has retail price, no wholesale tiers)"),"retail"):e.id.includes("WHOLESALE")?(console.log("✅ [MARKETPLACE_SECTION] ID contains WHOLESALE (PRIORITY 3), forcing wholesale section"),"wholesale"):"marketplaceSection"in e&&e.marketplaceSection?(console.log("✅ [MARKETPLACE_SECTION] Using product.marketplaceSection (PRIORITY 4):",e.marketplaceSection),e.marketplaceSection):(console.log("⚠️ [MARKETPLACE_SECTION] Using default fallback: retail"),"retail")},E=e=>({tyres:"Tyres",brakes:"Brake Parts"})[e]||e,R=(e,i)=>{if(console.log("💰 [DISPLAY_PRICE] Calculating price for:",{productId:e.id,section:i,hasWholesalePricingTiers:!!(e.wholesalePricingTiers&&e.wholesalePricingTiers.length>0),wholesalePricingTiersCount:e.wholesalePricingTiers?.length||0,hasRetailPrice:!!e.retailPrice,retailPriceValue:e.retailPrice}),i==="wholesale"&&e.wholesalePricingTiers&&e.wholesalePricingTiers.length>0){const s=e.wholesalePricingTiers[0].price;return console.log("✅ [DISPLAY_PRICE] Using wholesale first tier price:",s),s}if(i==="retail"&&e.retailPrice)return console.log("✅ [DISPLAY_PRICE] Using retail price:",e.retailPrice),e.retailPrice;if(e.retailPrice)return console.log("⚠️ [DISPLAY_PRICE] Fallback to retail price:",e.retailPrice),e.retailPrice;if(e.wholesalePricingTiers&&e.wholesalePricingTiers.length>0){const s=e.wholesalePricingTiers[0].price;return console.log("⚠️ [DISPLAY_PRICE] Fallback to wholesale first tier:",s),s}return console.log("❌ [DISPLAY_PRICE] No price found, returning 0"),0},p=e=>m(e),C=e=>{const i=P(e.descriptionAndSpecifications||""),a={...{"Part #":e.partArticleNumber||e.sku||"N/A",SKU:e.sku||"N/A","Product ID":e.id,Manufacturer:e.manufacturer||"N/A","Stock Quantity":e.stockQuantity?.toString()||"0"},...i.specifications};if(e.category==="tyres"&&"width"in e){const t=e,n={Width:t.width?`${t.width}mm`:"N/A","Aspect Ratio":t.aspectRatio?`${t.aspectRatio}%`:"N/A","Rim Diameter":t.rimDiameter?`${t.rimDiameter}"`:"N/A","Load Index":t.loadIndex?.toString()||"N/A","Speed Rating":t.speedRating||"N/A",Season:t.season||"N/A","Tread Life":t.treadLife||"N/A","Traction Rating":t.tractionRating||"N/A","Temperature Rating":t.temperatureRating||"N/A"};return{...a,...n}}return e.certifications&&e.certifications.length>0&&(a.Certifications=e.certifications.join(", ")),e.shippingOrigin&&(a["Ships From"]=e.shippingOrigin),e.estimatedLeadTime&&(a["Lead Time"]=e.estimatedLeadTime),a},y=e=>{const i=P(e.descriptionAndSpecifications||"");return i.description?i.description:`High-quality ${e.name} from ${e.manufacturer}. This product is designed to meet the highest standards of performance and reliability, ensuring optimal functionality for your vehicle.`},L=e=>"vehicleTypeCompatibility"in e&&e.vehicleTypeCompatibility?e.vehicleTypeCompatibility.map(i=>i.displayName):["Universal Compatibility - Check with your vehicle specifications"];export{E as a,C as b,L as c,y as d,R as e,S as g,p as i};
