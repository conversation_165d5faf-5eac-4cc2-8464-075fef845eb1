import{q as Q,r as o,t as p,j as e,v as P,C as b,k as N,Q as k,l as v,B as m,aE as T,m as h}from"./index-DnIZvqWt.js";function O(){const{productId:r}=Q(),[t,S]=o.useState({}),[E,w]=o.useState(!0),{products:c,isLoading:n,isError:C,error:B}=p("tyres"),{products:l,isLoading:d,isError:R,error:L}=p("brakes"),[A,F]=o.useState([]),[D,I]=o.useState([]);o.useEffect(()=>{!n&&!d&&(async()=>{console.log("🧪 [COMPREHENSIVE_TEST] Starting all tests for:",r);const a={timestamp:new Date().toISOString(),productId:r,tests:{}};a.tests.reactQuery={tyreProducts:c?.length||0,brakeProducts:l?.length||0,tyresLoading:n,brakesLoading:d,tyresError:C,brakesError:R,tyreErrorMessage:B?.message,brakeErrorMessage:L?.message};try{console.log("🧪 [COMPREHENSIVE_TEST] Testing direct service calls");const s=await T("tyres"),i=await T("brakes");F(s),I(i),a.tests.directService={tyreProducts:s.length,brakeProducts:i.length,success:!0}}catch(s){a.tests.directService={success:!1,error:s.message}}const f=c.filter(s=>{const i=s.retailPrice&&s.retailPrice>0,j=s.status==="active"||s.status==="out_of_stock";return i&&j}),y=l.filter(s=>{const i=s.retailPrice&&s.retailPrice>0,j=s.status==="active"||s.status==="out_of_stock";return i&&j}),u=[...f,...y],x=u.find(s=>s.id===r);a.tests.filtering={originalTyres:c.length,originalBrakes:l.length,filteredTyres:f.length,filteredBrakes:y.length,totalFiltered:u.length,productFound:!!x,productName:x?.name,productStatus:x?.status,productRetailPrice:x?.retailPrice},a.tests.sampleProducts={first10ProductIds:u.slice(0,10).map(s=>({id:s.id,name:s.name,status:s.status,retailPrice:s.retailPrice})),searchedProductId:r,exactMatch:u.find(s=>s.id===r)},a.tests.lifecycle={componentMounted:!0,reactQueryWorking:!n&&!d,hasData:c.length>0||l.length>0,timestamp:new Date().toISOString()},console.log("🧪 [COMPREHENSIVE_TEST] All tests completed:",a),S(a),w(!1)})()},[r,c,l,n,d]);const M=g=>{switch(g){case"PASS":return e.jsx(h,{className:"bg-green-100 text-green-800",children:"PASS"});case"FAIL":return e.jsx(h,{className:"bg-red-100 text-red-800",children:"FAIL"});case"WARN":return e.jsx(h,{className:"bg-yellow-100 text-yellow-800",children:"WARN"});default:return e.jsx(h,{variant:"outline",children:g})}};return E||n||d?e.jsx(P,{children:e.jsx("div",{className:"container py-8",children:e.jsxs("div",{className:"text-center",children:[e.jsx("h1",{className:"text-2xl font-bold mb-4",children:"Running Comprehensive Tests..."}),e.jsxs("p",{children:["Testing ProductPage data flow for: ",r]})]})})}):e.jsx(P,{children:e.jsx("div",{className:"container py-8",children:e.jsxs("div",{className:"max-w-6xl mx-auto space-y-6",children:[e.jsxs("div",{className:"text-center mb-8",children:[e.jsx("h1",{className:"text-3xl font-bold mb-4",children:"ProductPage Comprehensive Test"}),e.jsxs("p",{className:"text-gray-600",children:["Testing product ID: ",e.jsx("code",{className:"bg-gray-100 px-2 py-1 rounded",children:r})]})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[e.jsxs(b,{children:[e.jsx(N,{children:e.jsx(k,{children:"React Query Test"})}),e.jsx(v,{children:e.jsxs("div",{className:"space-y-2 text-sm",children:[e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{children:"Tyre Products:"}),e.jsx("span",{children:t.tests?.reactQuery?.tyreProducts||0})]}),e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{children:"Brake Products:"}),e.jsx("span",{children:t.tests?.reactQuery?.brakeProducts||0})]}),e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{children:"Loading State:"}),e.jsx("span",{children:t.tests?.reactQuery?.tyresLoading||t.tests?.reactQuery?.brakesLoading?"Loading":"Complete"})]}),e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{children:"Errors:"}),e.jsx("span",{children:t.tests?.reactQuery?.tyresError||t.tests?.reactQuery?.brakesError?"Yes":"None"})]})]})})]}),e.jsxs(b,{children:[e.jsx(N,{children:e.jsx(k,{children:"Product Filtering Test"})}),e.jsx(v,{children:e.jsxs("div",{className:"space-y-2 text-sm",children:[e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{children:"Original Tyres:"}),e.jsx("span",{children:t.tests?.filtering?.originalTyres||0})]}),e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{children:"Filtered Tyres:"}),e.jsx("span",{children:t.tests?.filtering?.filteredTyres||0})]}),e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{children:"Original Brakes:"}),e.jsx("span",{children:t.tests?.filtering?.originalBrakes||0})]}),e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{children:"Filtered Brakes:"}),e.jsx("span",{children:t.tests?.filtering?.filteredBrakes||0})]}),e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{children:"Total Products:"}),e.jsx("span",{children:t.tests?.filtering?.totalFiltered||0})]}),e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{children:"Product Found:"}),M(t.tests?.filtering?.productFound?"PASS":"FAIL")]}),t.tests?.filtering?.productFound&&e.jsxs("div",{className:"mt-2 p-2 bg-green-50 rounded",children:[e.jsx("p",{className:"font-medium",children:t.tests?.filtering?.productName}),e.jsxs("p",{className:"text-xs text-gray-600",children:["Status: ",t.tests?.filtering?.productStatus]}),e.jsxs("p",{className:"text-xs text-gray-600",children:["Price: ",t.tests?.filtering?.productRetailPrice," DZD"]})]})]})})]})]}),e.jsxs("div",{className:"flex gap-4 justify-center",children:[e.jsx(m,{onClick:()=>window.location.reload(),children:"Rerun Tests"}),e.jsx(m,{variant:"outline",onClick:()=>window.history.back(),children:"Go Back"}),e.jsx(m,{onClick:()=>window.open(`/${r}`,"_blank"),className:"bg-[#fa7b00] hover:bg-[#fa7b00]/90",children:"Test ProductPage"})]})]})})})}export{O as default};
