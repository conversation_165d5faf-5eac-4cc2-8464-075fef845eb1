import{u as P,ag as L,a as E,r as o,j as e,al as F,F as u,v as g,S as j,B as x,A as D,M,C as p,k as f,l as y,P as T,Q as B,m as N,ao as Y,ap as O}from"./index-DnIZvqWt.js";import{S as r}from"./skeleton-iTtVbUqk.js";import{C as U}from"./calendar-ByxQ0_Vi.js";import{T as V}from"./trash-2-CfJXQ708.js";function H(){const{t:l}=P(),i=L(),{toast:t}=E(),[d,m]=o.useState([]),[v,w]=o.useState(!0),[b,S]=o.useState(!1),[C,h]=o.useState(new Set);o.useEffect(()=>{(async()=>{try{const a=localStorage.getItem("phone_auth_session");if(!(a&&JSON.parse(a).profile?.role==="consumer")){i("/");return}S(!0);const c=await Y();c.success?m(c.reviews||[]):t.error("Failed to load reviews",{description:c.error||"Please try again"})}catch(a){console.error("Error loading reviews:",a),t.error("Failed to load reviews",{description:"Please try again"})}finally{w(!1)}})()},[i,t,l]);const k=async s=>{h(a=>new Set(a).add(s));try{const a=await O(s);a.success?(m(n=>n.filter(c=>c.id!==s)),t.success("Review deleted",{description:"Your review has been deleted successfully"})):t.error("Failed to delete review",{description:a.error||"Please try again"})}catch(a){console.error("Error deleting review:",a),t.error("Failed to delete review",{description:"Please try again"})}finally{h(a=>{const n=new Set(a);return n.delete(s),n})}},R=s=>{i(`/${s}`)},_=s=>e.jsx("div",{className:"flex items-center gap-1",children:[1,2,3,4,5].map(a=>e.jsx(j,{className:`h-4 w-4 ${a<=s?"fill-yellow-400 text-yellow-400":"fill-gray-200 text-gray-200"}`},a))}),A=s=>new Date(s).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"});return b?e.jsx(F,{action:"review",fallback:e.jsx(u,{children:e.jsx(g,{children:e.jsx("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:e.jsxs("div",{className:"text-center",children:[e.jsx(j,{className:"h-16 w-16 text-[#fa7b00] mx-auto mb-4"}),e.jsx("h2",{className:"text-2xl font-bold text-gray-900 mb-2",children:"Access Your Reviews"}),e.jsx("p",{className:"text-gray-600 mb-6",children:"View and manage your product reviews."}),e.jsx("button",{onClick:()=>{const s=document.getElementById("auth-modal-trigger");s&&s.click()},className:"bg-[#fa7b00] hover:bg-[#fa7b00]/90 text-white px-6 py-3 rounded-lg font-medium",children:l("auth.loginOrSignUp")})]})})})}),children:e.jsx(u,{children:e.jsx(g,{children:e.jsx("div",{className:"min-h-screen bg-gray-50",children:e.jsxs("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[e.jsxs("div",{className:"mb-8",children:[e.jsx("div",{className:"flex items-center gap-4 mb-4",children:e.jsxs(x,{variant:"ghost",onClick:()=>i("/"),className:"flex items-center gap-2 text-gray-600 hover:text-gray-900",children:[e.jsx(D,{className:"h-4 w-4"}),l("actions.back")]})}),e.jsx("div",{className:"flex items-center justify-between",children:e.jsxs("div",{children:[e.jsxs("h1",{className:"text-3xl font-bold text-gray-900 flex items-center gap-3",children:[e.jsx(M,{className:"h-8 w-8 text-[#fa7b00]"}),l("marketplace.myReviews")]}),e.jsxs("p",{className:"text-gray-600 mt-2",children:[d.length," review",d.length!==1?"s":""]})]})})]}),v?e.jsx("div",{className:"space-y-6",children:[...Array(5)].map((s,a)=>e.jsxs(p,{children:[e.jsx(f,{children:e.jsxs("div",{className:"flex items-start justify-between",children:[e.jsxs("div",{className:"flex-1",children:[e.jsx(r,{className:"h-6 w-3/4 mb-2"}),e.jsx(r,{className:"h-4 w-1/2 mb-2"}),e.jsx(r,{className:"h-4 w-1/4"})]}),e.jsx(r,{className:"h-8 w-20"})]})}),e.jsxs(y,{children:[e.jsx(r,{className:"h-4 w-full mb-2"}),e.jsx(r,{className:"h-4 w-3/4"})]})]},a))}):d.length===0?e.jsxs("div",{className:"text-center py-16",children:[e.jsx(T,{className:"h-16 w-16 text-gray-300 mx-auto mb-4"}),e.jsx("h3",{className:"text-xl font-semibold text-gray-900 mb-2",children:"No Reviews Yet"}),e.jsx("p",{className:"text-gray-600 mb-6",children:"You haven't written any reviews yet. Start shopping and share your experience!"}),e.jsx(x,{onClick:()=>i("/"),className:"bg-[#fa7b00] hover:bg-[#fa7b00]/90 text-white",children:l("marketplace.continueShopping")})]}):e.jsx("div",{className:"space-y-6",children:d.map(s=>e.jsxs(p,{className:"hover:shadow-lg transition-shadow",children:[e.jsx(f,{children:e.jsxs("div",{className:"flex items-start justify-between",children:[e.jsxs("div",{className:"flex-1",children:[e.jsx(B,{className:"text-lg cursor-pointer hover:text-[#fa7b00] transition-colors",onClick:()=>R(s.product_id),children:s.product_name}),e.jsxs("div",{className:"flex items-center gap-3 mt-2",children:[_(s.rating),e.jsxs("span",{className:"text-sm text-gray-500",children:[s.rating,"/5"]})]}),e.jsxs("div",{className:"flex items-center gap-2 mt-2 text-sm text-gray-500",children:[e.jsx(U,{className:"h-4 w-4"}),A(s.created_at)]})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(N,{variant:s.is_approved?"default":"secondary",children:s.is_approved?"Published":"Pending Review"}),s.is_verified_purchase&&e.jsx(N,{variant:"outline",className:"text-green-600 border-green-600",children:"Verified Purchase"}),e.jsx(x,{variant:"ghost",size:"sm",onClick:()=>k(s.id),disabled:C.has(s.id),className:"text-red-500 hover:text-red-600 hover:bg-red-50",children:e.jsx(V,{className:"h-4 w-4"})})]})]})}),e.jsxs(y,{children:[s.title&&e.jsx("h4",{className:"font-semibold text-gray-900 mb-2",children:s.title}),s.review_text&&e.jsx("p",{className:"text-gray-700 leading-relaxed",children:s.review_text}),s.helpful_count>0&&e.jsxs("div",{className:"mt-4 text-sm text-gray-500",children:[s.helpful_count," people found this helpful"]})]})]},s.id))})]})})})})}):null}export{H as default};
