import{u as W,ag as I,a as T,s as D,r as o,j as e,al as F,F as g,v as f,J as j,B as m,A as L,C as w,l as b,P,H as B,am as M,an as R}from"./index-DnIZvqWt.js";import{S as n}from"./skeleton-iTtVbUqk.js";import{T as $}from"./trash-2-CfJXQ708.js";function q(){const{t}=W(),l=I(),{toast:c}=T(),{addItem:N}=D(),[d,p]=o.useState([]),[v,k]=o.useState(!0),[y,C]=o.useState(!1),[_,x]=o.useState(new Set);o.useEffect(()=>{(async()=>{try{const r=localStorage.getItem("phone_auth_session");if(!(r&&JSON.parse(r).profile?.role==="consumer")){l("/");return}C(!0);const a=await M();a.success?p(a.items||[]):c.error(t("marketplace.wishlistError"),{description:a.error||t("marketplace.wishlistErrorDescription")})}catch(r){console.error("Error loading wishlist:",r),c.error(t("marketplace.wishlistError"),{description:t("marketplace.wishlistErrorDescription")})}finally{k(!1)}})()},[l,c,t]);const E=async s=>{x(r=>new Set(r).add(s));try{const r=await R(s);r.success?(p(i=>i.filter(a=>a.product_id!==s)),c.success(t("marketplace.removedFromWishlist"),{description:t("marketplace.removedFromWishlistDescription")}),window.dispatchEvent(new CustomEvent("wishlist:updated"))):c.error(t("marketplace.wishlistError"),{description:r.error||t("marketplace.wishlistErrorDescription")})}catch(r){console.error("Error removing from wishlist:",r),c.error(t("marketplace.wishlistError"),{description:t("marketplace.wishlistErrorDescription")})}finally{x(r=>{const i=new Set(r);return i.delete(s),i})}},S=s=>{try{N({id:(i=>{let a=0;for(let h=0;h<i.length;h++){const A=i.charCodeAt(h);a=(a<<5)-a+A,a=a&a}return Math.abs(a)})(s.product_id),name:s.product_name,price:s.product_price||0,image:s.product_image||"",manufacturer:s.product_manufacturer||"",quantity:1}),c.success(t("marketplace.addToCart"),{description:`${s.product_name} ${t("marketplace.addedToCart")}`})}catch(r){console.error("Error adding to cart:",r),c.error("Failed to add to cart",{description:"Please try again"})}},u=s=>{l(`/${s}`)};return y?e.jsx(F,{action:"wishlist",fallback:e.jsx(g,{children:e.jsx(f,{children:e.jsx("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:e.jsxs("div",{className:"text-center",children:[e.jsx(j,{className:"h-16 w-16 text-[#fa7b00] mx-auto mb-4"}),e.jsx("h2",{className:"text-2xl font-bold text-gray-900 mb-2",children:"Access Your Wishlist"}),e.jsx("p",{className:"text-gray-600 mb-6",children:"Save your favorite products and shop them later."}),e.jsx("button",{onClick:()=>{const s=document.getElementById("auth-modal-trigger");s&&s.click()},className:"bg-[#fa7b00] hover:bg-[#fa7b00]/90 text-white px-6 py-3 rounded-lg font-medium",children:t("auth.loginOrSignUp")})]})})})}),children:e.jsx(g,{children:e.jsx(f,{children:e.jsx("div",{className:"min-h-screen bg-gray-50",children:e.jsxs("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[e.jsxs("div",{className:"mb-8",children:[e.jsx("div",{className:"flex items-center gap-4 mb-4",children:e.jsxs(m,{variant:"ghost",onClick:()=>l("/"),className:"flex items-center gap-2 text-gray-600 hover:text-gray-900",children:[e.jsx(L,{className:"h-4 w-4"}),t("actions.back")]})}),e.jsx("div",{className:"flex items-center justify-between",children:e.jsxs("div",{children:[e.jsxs("h1",{className:"text-3xl font-bold text-gray-900 flex items-center gap-3",children:[e.jsx(j,{className:"h-8 w-8 text-[#fa7b00]"}),t("marketplace.myWishlist")]}),e.jsxs("p",{className:"text-gray-600 mt-2",children:[d.length," ",d.length===1?t("marketplace.item"):t("marketplace.items")]})]})})]}),v?e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6",children:[...Array(8)].map((s,r)=>e.jsxs(w,{className:"overflow-hidden",children:[e.jsx(n,{className:"h-48 w-full"}),e.jsxs(b,{className:"p-4",children:[e.jsx(n,{className:"h-4 w-3/4 mb-2"}),e.jsx(n,{className:"h-4 w-1/2 mb-4"}),e.jsxs("div",{className:"flex gap-2",children:[e.jsx(n,{className:"h-10 flex-1"}),e.jsx(n,{className:"h-10 w-10"})]})]})]},r))}):d.length===0?e.jsxs("div",{className:"text-center py-16",children:[e.jsx(P,{className:"h-16 w-16 text-gray-300 mx-auto mb-4"}),e.jsx("h3",{className:"text-xl font-semibold text-gray-900 mb-2",children:t("marketplace.wishlistEmpty")}),e.jsx("p",{className:"text-gray-600 mb-6",children:t("marketplace.wishlistEmptyDescription")}),e.jsx(m,{onClick:()=>l("/"),className:"bg-[#fa7b00] hover:bg-[#fa7b00]/90 text-white",children:t("marketplace.continueShopping")})]}):e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6",children:d.map(s=>e.jsxs(w,{className:"overflow-hidden hover:shadow-lg transition-shadow",children:[e.jsxs("div",{className:"relative",children:[e.jsx("img",{src:s.product_image||"/placeholder-product.jpg",alt:s.product_name,className:"w-full h-48 object-cover cursor-pointer",onClick:()=>u(s.product_id)}),e.jsx(m,{variant:"ghost",size:"sm",onClick:()=>E(s.product_id),disabled:_.has(s.product_id),className:"absolute top-2 right-2 bg-white/80 hover:bg-white text-red-500 hover:text-red-600 rounded-full p-2",children:e.jsx($,{className:"h-4 w-4"})})]}),e.jsxs(b,{className:"p-4",children:[e.jsx("h3",{className:"font-semibold text-gray-900 mb-2 line-clamp-2 cursor-pointer hover:text-[#fa7b00]",onClick:()=>u(s.product_id),children:s.product_name}),s.product_manufacturer&&e.jsx("p",{className:"text-sm text-gray-600 mb-2",children:s.product_manufacturer}),s.product_price&&e.jsxs("p",{className:"text-lg font-bold text-[#fa7b00] mb-4",children:[s.product_price.toLocaleString()," DA"]}),e.jsx("div",{className:"flex gap-2",children:e.jsxs(m,{onClick:()=>S(s),className:"flex-1 bg-[#fa7b00] hover:bg-[#fa7b00]/90 text-white",children:[e.jsx(B,{className:"h-4 w-4 mr-2"}),t("marketplace.addToCart")]})})]})]},s.id))})]})})})})}):null}export{q as default};
