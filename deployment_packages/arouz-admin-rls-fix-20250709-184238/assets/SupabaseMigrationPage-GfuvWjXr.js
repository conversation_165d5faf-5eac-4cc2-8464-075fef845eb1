import{c as q,aC as B,aD as _,a7 as H,r as p,ay as d,j as e,v as C,C as h,l as x,ab as X,a6 as A,k as f,Q as j,B as b,m as w,aA as k,aB as M,U as $,a0 as Y}from"./index-DnIZvqWt.js";import{D as I}from"./database-Cj1UPPdz.js";import{H as K}from"./hard-drive-CCFsZ-6Z.js";import{U as E}from"./upload-Bf1AuFkI.js";import{T as U}from"./trash-2-CfJXQ708.js";/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const V=q("Cloud",[["path",{d:"M17.5 19H9a7 7 0 1 1 6.71-9h1.79a4.5 4.5 0 1 1 0 9Z",key:"p7xjir"}]]),Z="https://irkwpzcskeqtasutqnxp.supabase.co",G="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.bUv9zxx9q_sVUwv_-G6WbHsA53-SU5tCCWbOS_5Xp94",y=B(Z,G);async function S(){try{const{data:{user:r},error:t}=await y.auth.getUser();return!t&&!!r}catch(r){return console.error("Error checking authentication:",r),!1}}async function D(){try{const{data:{user:r},error:t}=await y.auth.getUser();return t||!r?null:r.id}catch(r){return console.error("Error getting user ID:",r),null}}async function W(r,t){try{const{data:o,error:s}=await y.from("products").select("id").eq("id",r).eq("user_id",t).single();return!s&&!!o}catch{return!1}}async function F(r){const t={category:r,totalProducts:0,migratedProducts:0,failedProducts:0,errors:[],skippedProducts:0};try{if(!await S())return t.errors.push("User not authenticated"),t;const s=await D();if(!s)return t.errors.push("Could not get user ID"),t;const i=`products-${r}`,c=localStorage.getItem(i);if(!c)return t.errors.push(`No products found in localStorage for category: ${r}`),t;let m=[];try{m=JSON.parse(c),m=m.map(l=>({...l,createdAt:new Date(l.createdAt),updatedAt:new Date(l.updatedAt),inventoryUpdateDate:l.inventoryUpdateDate?new Date(l.inventoryUpdateDate):new Date}))}catch(l){return t.errors.push(`Error parsing localStorage data: ${l}`),t}t.totalProducts=m.length;for(const l of m)try{if(await W(l.id,s)){t.skippedProducts++,console.log(`Product ${l.id} already exists, skipping...`);continue}const g={...l,marketplaceSection:l.marketplaceSection||(l.wholesalePricingTiers&&l.wholesalePricingTiers.length>0?"wholesale":"retail")};await _(g),t.migratedProducts++,console.log(`Successfully migrated product: ${l.id}`)}catch(u){t.failedProducts++;const g=`Failed to migrate product ${l.id}: ${u}`;t.errors.push(g),console.error(g)}console.log(`Migration completed for category ${r}:`,{total:t.totalProducts,migrated:t.migratedProducts,failed:t.failedProducts,skipped:t.skippedProducts})}catch(o){t.errors.push(`Category migration error: ${o}`),console.error(`Error migrating category ${r}:`,o)}return t}async function Q(){const r=Date.now(),t=["tyres","brakes"],o={totalCategories:t.length,totalProducts:0,totalMigrated:0,totalFailed:0,totalSkipped:0,results:[],duration:0};if(console.log("Starting migration of all products to Supabase..."),!await S())throw new Error("User must be authenticated to perform migration");for(const i of t){console.log(`Migrating category: ${i}`);try{const c=await F(i);o.results.push(c),o.totalProducts+=c.totalProducts,o.totalMigrated+=c.migratedProducts,o.totalFailed+=c.failedProducts,o.totalSkipped+=c.skippedProducts}catch(c){console.error(`Error migrating category ${i}:`,c),o.results.push({category:i,totalProducts:0,migratedProducts:0,failedProducts:0,errors:[`Category migration failed: ${c}`],skippedProducts:0})}}return o.duration=Date.now()-r,console.log("Migration summary:",o),o}async function R(){const r={isAuthenticated:!1,localStorageData:{},supabaseData:{},needsMigration:!1};if(r.isAuthenticated=await S(),!r.isAuthenticated)return r;const t=await D();if(!t)return r.isAuthenticated=!1,r;const o=["tyres","brakes"];for(const s of o){const i=`products-${s}`,c=localStorage.getItem(i);if(c)try{const m=JSON.parse(c);r.localStorageData[s]=m.length}catch{r.localStorageData[s]=0}else r.localStorageData[s]=0}for(const s of o)try{const{count:i,error:c}=await y.from("products").select("*",{count:"exact",head:!0}).eq("user_id",t).eq("category",s);r.supabaseData[s]=i||0}catch(i){console.error(`Error checking Supabase data for ${s}:`,i),r.supabaseData[s]=0}return r.needsMigration=o.some(s=>r.localStorageData[s]>0&&r.localStorageData[s]>r.supabaseData[s]),r}async function ee(){const r=["tyres","brakes"],t=await R();if(!t.isAuthenticated)throw new Error("User not authenticated");let o=!0;for(const s of r)if(t.localStorageData[s]>t.supabaseData[s]){o=!1;break}if(!o)throw new Error("Cannot clear localStorage: migration appears incomplete");for(const s of r){const i=`products-${s}`;localStorage.removeItem(i),console.log(`Cleared localStorage for category: ${s}`)}console.log("localStorage cleared successfully after migration verification")}async function te(){if(!await S())throw new Error("User not authenticated");const t=await D();if(!t)throw new Error("Could not get user ID");console.warn("Rolling back migration - deleting all products from Supabase...");try{const{error:o}=await y.from("products").delete().eq("user_id",t);if(o)throw o;console.log("Migration rollback completed successfully")}catch(o){throw console.error("Error during rollback:",o),o}}function ce(){const{isAuthenticated:r}=H(),[t,o]=p.useState(null),[s,i]=p.useState(!1),[c,m]=p.useState(null),[l,u]=p.useState("");p.useEffect(()=>{r&&g()},[r]);const g=async()=>{try{i(!0);const a=await R();o(a)}catch(a){console.error("Error checking migration status:",a),d.error("Failed to check migration status")}finally{i(!1)}},O=async()=>{if(!t?.isAuthenticated){d.error("Please log in to perform migration");return}i(!0),u("Migrating all products to Supabase...");try{const a=await Q();m(a),a.totalMigrated>0?d.success(`Successfully migrated ${a.totalMigrated} products!`,{description:`Duration: ${(a.duration/1e3).toFixed(1)}s`}):a.totalSkipped>0?d.info("All products already exist in Supabase"):d.warning("No products were migrated"),await g()}catch(a){console.error("Migration error:",a),d.error("Migration failed. Check console for details.")}finally{i(!1),u("")}},T=async a=>{if(!t?.isAuthenticated){d.error("Please log in to perform migration");return}i(!0),u(`Migrating ${a} products...`);try{const n=await F(a);n.migratedProducts>0?d.success(`Successfully migrated ${n.migratedProducts} ${a} products!`):n.skippedProducts>0?d.info(`All ${a} products already exist in Supabase`):d.warning(`No ${a} products were migrated`),await g()}catch(n){console.error("Category migration error:",n),d.error(`Failed to migrate ${a} products`)}finally{i(!1),u("")}},z=async()=>{if(!t?.isAuthenticated){d.error("Please log in to clear localStorage");return}i(!0),u("Clearing localStorage...");try{await ee(),d.success("localStorage cleared successfully"),await g()}catch(a){console.error("Error clearing localStorage:",a),d.error("Failed to clear localStorage. Migration may be incomplete.")}finally{i(!1),u("")}},L=async()=>{if(!t?.isAuthenticated){d.error("Please log in to perform rollback");return}if(confirm("Are you sure you want to delete ALL products from Supabase? This cannot be undone.")){i(!0),u("Rolling back migration...");try{await te(),d.success("Migration rollback completed"),await g()}catch(a){console.error("Rollback error:",a),d.error("Failed to rollback migration")}finally{i(!1),u("")}}},v=()=>t?Object.values(t.localStorageData).reduce((a,n)=>a+n,0):0,P=()=>t?Object.values(t.supabaseData).reduce((a,n)=>a+n,0):0;return r?e.jsx(C,{children:e.jsxs("div",{className:"container py-8",children:[e.jsxs("div",{className:"mb-8",children:[e.jsx("h1",{className:"text-3xl font-bold mb-4",children:"Supabase Migration"}),e.jsx("p",{className:"text-gray-600 mb-6",children:"Migrate your product data from localStorage to the Supabase backend database with complete account isolation."})]}),s&&l&&e.jsx(h,{className:"mb-6 border-blue-200 bg-blue-50",children:e.jsx(x,{className:"py-4",children:e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx(A,{className:"h-5 w-5 text-blue-600 animate-spin"}),e.jsx("span",{className:"text-blue-800 font-medium",children:l})]})})}),e.jsxs(h,{className:"mb-6",children:[e.jsx(f,{children:e.jsxs(j,{className:"flex items-center gap-2",children:[e.jsx(I,{className:"h-5 w-5"}),"Migration Status",e.jsxs(b,{variant:"outline",size:"sm",onClick:g,disabled:s,className:"ml-auto",children:[e.jsx(A,{className:"h-4 w-4 mr-2"}),"Refresh"]})]})}),e.jsx(x,{children:t?e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[e.jsxs(h,{className:"border-orange-200",children:[e.jsx(f,{className:"pb-3",children:e.jsxs(j,{className:"text-lg flex items-center gap-2",children:[e.jsx(K,{className:"h-5 w-5 text-orange-600"}),"localStorage Data"]})}),e.jsx(x,{children:e.jsxs("div",{className:"space-y-3",children:[Object.entries(t.localStorageData).map(([a,n])=>e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsxs("span",{className:"capitalize",children:[a,":"]}),e.jsxs(w,{variant:"outline",children:[n," products"]})]},a)),e.jsxs("div",{className:"border-t pt-3 flex justify-between items-center font-semibold",children:[e.jsx("span",{children:"Total:"}),e.jsxs(w,{variant:"secondary",children:[v()," products"]})]})]})})]}),e.jsxs(h,{className:"border-green-200",children:[e.jsx(f,{className:"pb-3",children:e.jsxs(j,{className:"text-lg flex items-center gap-2",children:[e.jsx(V,{className:"h-5 w-5 text-green-600"}),"Supabase Data"]})}),e.jsx(x,{children:e.jsxs("div",{className:"space-y-3",children:[Object.entries(t.supabaseData).map(([a,n])=>e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsxs("span",{className:"capitalize",children:[a,":"]}),e.jsxs(w,{variant:"outline",children:[n," products"]})]},a)),e.jsxs("div",{className:"border-t pt-3 flex justify-between items-center font-semibold",children:[e.jsx("span",{children:"Total:"}),e.jsxs(w,{variant:"secondary",children:[P()," products"]})]})]})})]})]}),t.needsMigration?e.jsxs(k,{className:"border-orange-200 bg-orange-50",children:[e.jsx(E,{className:"h-4 w-4 text-orange-600"}),e.jsxs(M,{className:"text-orange-800",children:["Migration needed: You have ",v()-P()," products in localStorage that haven't been migrated to Supabase."]})]}):v()>0?e.jsxs(k,{className:"border-green-200 bg-green-50",children:[e.jsx($,{className:"h-4 w-4 text-green-600"}),e.jsx(M,{className:"text-green-800",children:"All products have been successfully migrated to Supabase!"})]}):e.jsxs(k,{className:"border-blue-200 bg-blue-50",children:[e.jsx(I,{className:"h-4 w-4 text-blue-600"}),e.jsx(M,{className:"text-blue-800",children:"No products found in localStorage. You're ready to use the Supabase backend!"})]})]}):e.jsx("div",{className:"text-center py-4",children:e.jsx("p",{className:"text-gray-500",children:"Loading migration status..."})})})]}),e.jsxs(h,{className:"mb-6",children:[e.jsx(f,{children:e.jsx(j,{children:"Migration Actions"})}),e.jsx(x,{children:e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center justify-between p-4 border rounded-lg",children:[e.jsxs("div",{children:[e.jsx("h3",{className:"font-semibold",children:"Full Migration"}),e.jsx("p",{className:"text-sm text-gray-600",children:"Migrate all products from all categories"})]}),e.jsxs(b,{onClick:O,disabled:s||!t?.needsMigration,className:"gap-2",children:[e.jsx(E,{className:"h-4 w-4"}),"Migrate All"]})]}),t&&Object.entries(t.localStorageData).map(([a,n])=>{const N=t.supabaseData[a]||0,J=n>N;return e.jsxs("div",{className:"flex items-center justify-between p-4 border rounded-lg",children:[e.jsxs("div",{children:[e.jsxs("h3",{className:"font-semibold capitalize",children:[a," Migration"]}),e.jsxs("p",{className:"text-sm text-gray-600",children:[n," in localStorage, ",N," in Supabase"]})]}),e.jsxs(b,{variant:"outline",onClick:()=>T(a),disabled:s||!J,className:"gap-2",children:[e.jsx(Y,{className:"h-4 w-4"}),"Migrate ",a]})]},a)}),e.jsxs("div",{className:"flex items-center justify-between p-4 border rounded-lg border-orange-200",children:[e.jsxs("div",{children:[e.jsx("h3",{className:"font-semibold",children:"Clear localStorage"}),e.jsx("p",{className:"text-sm text-gray-600",children:"Remove localStorage data after successful migration"})]}),e.jsxs(b,{variant:"outline",onClick:z,disabled:s||t?.needsMigration,className:"gap-2",children:[e.jsx(U,{className:"h-4 w-4"}),"Clear localStorage"]})]}),e.jsxs("div",{className:"flex items-center justify-between p-4 border rounded-lg border-red-200 bg-red-50",children:[e.jsxs("div",{children:[e.jsx("h3",{className:"font-semibold text-red-800",children:"Rollback Migration"}),e.jsx("p",{className:"text-sm text-red-600",children:"⚠️ Delete all products from Supabase (Development only)"})]}),e.jsxs(b,{variant:"destructive",onClick:L,disabled:s,className:"gap-2",children:[e.jsx(U,{className:"h-4 w-4"}),"Rollback"]})]})]})})]}),c&&e.jsxs(h,{children:[e.jsx(f,{children:e.jsxs(j,{className:"flex items-center gap-2",children:[e.jsx($,{className:"h-5 w-5 text-green-600"}),"Migration Results"]})}),e.jsx(x,{children:e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4 text-center",children:[e.jsxs("div",{children:[e.jsx("div",{className:"text-2xl font-bold text-blue-600",children:c.totalProducts}),e.jsx("div",{className:"text-sm text-gray-600",children:"Total Products"})]}),e.jsxs("div",{children:[e.jsx("div",{className:"text-2xl font-bold text-green-600",children:c.totalMigrated}),e.jsx("div",{className:"text-sm text-gray-600",children:"Migrated"})]}),e.jsxs("div",{children:[e.jsx("div",{className:"text-2xl font-bold text-orange-600",children:c.totalSkipped}),e.jsx("div",{className:"text-sm text-gray-600",children:"Skipped"})]}),e.jsxs("div",{children:[e.jsx("div",{className:"text-2xl font-bold text-red-600",children:c.totalFailed}),e.jsx("div",{className:"text-sm text-gray-600",children:"Failed"})]})]}),e.jsxs("div",{className:"text-center text-sm text-gray-600",children:["Duration: ",(c.duration/1e3).toFixed(1)," seconds"]}),c.results.map(a=>e.jsxs("div",{className:"border rounded-lg p-4",children:[e.jsxs("h4",{className:"font-semibold capitalize mb-2",children:[a.category," Category"]}),e.jsxs("div",{className:"grid grid-cols-4 gap-4 text-sm",children:[e.jsxs("div",{children:["Total: ",a.totalProducts]}),e.jsxs("div",{children:["Migrated: ",a.migratedProducts]}),e.jsxs("div",{children:["Skipped: ",a.skippedProducts]}),e.jsxs("div",{children:["Failed: ",a.failedProducts]})]}),a.errors.length>0&&e.jsxs("div",{className:"mt-2",children:[e.jsx("p",{className:"text-sm font-medium text-red-600",children:"Errors:"}),e.jsxs("ul",{className:"text-sm text-red-600 list-disc list-inside",children:[a.errors.slice(0,3).map((n,N)=>e.jsx("li",{children:n},N)),a.errors.length>3&&e.jsxs("li",{children:["... and ",a.errors.length-3," more errors"]})]})]})]},a.category))]})})]})]})}):e.jsx(C,{children:e.jsx("div",{className:"container py-8",children:e.jsx(h,{children:e.jsxs(x,{className:"text-center py-8",children:[e.jsx(X,{className:"h-12 w-12 text-orange-500 mx-auto mb-4"}),e.jsx("h2",{className:"text-xl font-semibold mb-2",children:"Authentication Required"}),e.jsx("p",{className:"text-gray-600 mb-4",children:"Please log in to access the Supabase migration tools."})]})})})})}export{ce as default};
