import{a1 as t}from"./index-DnIZvqWt.js";async function p(e){try{if(console.log("🚚 [SHIPPING_AUTH] Authenticating shipping company with code:",e.substring(0,4)+"..."),!e||e.length<5)return{success:!1,error:"Invalid login code format"};const{data:n,error:o}=await t.from("shipping_companies").select("*").eq("login_code",e).eq("is_active",!0).single();if(o||!n)return console.error("❌ [SHIPPING_AUTH] Company not found or inactive:",o),{success:!1,error:"Invalid login code or company is inactive"};console.log("✅ [SHIPPING_AUTH] Company found:",n.company_name);const{error:r}=await t.rpc("set_app_config",{setting_name:"app.shipping_company_code",setting_value:e,is_local:!1});if(r)return console.error("❌ [SHIPPING_AUTH] Error setting company context:",r),{success:!1,error:"Authentication failed - unable to set company context"};const s={id:n.id,company_name:n.company_name,login_code:n.login_code,contact_email:n.contact_email,contact_phone:n.contact_phone,coverage_areas:n.coverage_areas||[],service_types:n.service_types||[],commission_rate:n.commission_rate||0,is_active:n.is_active,authenticated_at:new Date().toISOString()};return localStorage.setItem("shipping_company_session",JSON.stringify(s)),console.log("✅ [SHIPPING_AUTH] Authentication successful for:",n.company_name),{success:!0,company:s}}catch(n){return console.error("❌ [SHIPPING_AUTH] Authentication exception:",n),{success:!1,error:"An unexpected error occurred during authentication"}}}function a(){try{const e=localStorage.getItem("shipping_company_session");if(!e)return null;const n=JSON.parse(e),o=Date.now()-new Date(n.authenticated_at).getTime(),r=24*60*60*1e3;return o>r?(console.log("🚚 [SHIPPING_AUTH] Session expired, clearing..."),i(),null):n}catch(e){return console.error("❌ [SHIPPING_AUTH] Error getting session:",e),i(),null}}function i(){try{localStorage.removeItem("shipping_company_session"),t.rpc("set_app_config",{setting_name:"app.shipping_company_code",setting_value:"",is_local:!1}).catch(e=>{console.error("❌ [SHIPPING_AUTH] Error clearing company context:",e)}),console.log("✅ [SHIPPING_AUTH] Session cleared")}catch(e){console.error("❌ [SHIPPING_AUTH] Error clearing session:",e)}}async function l(){try{const e=a();if(!e)return!1;const{error:n}=await t.rpc("set_app_config",{setting_name:"app.shipping_company_code",setting_value:e.login_code,is_local:!1});return n?(console.error("❌ [SHIPPING_AUTH] Error initializing context:",n),!1):(console.log("✅ [SHIPPING_AUTH] Context initialized for:",e.company_name),!0)}catch(e){return console.error("❌ [SHIPPING_AUTH] Exception initializing context:",e),!1}}async function _(){try{console.log("🚚 [SHIPPING_AUTH] Logging out shipping company..."),i(),window.location.href="/shipping/login"}catch(e){console.error("❌ [SHIPPING_AUTH] Error during logout:",e)}}export{p as a,a as g,l as i,_ as l};
