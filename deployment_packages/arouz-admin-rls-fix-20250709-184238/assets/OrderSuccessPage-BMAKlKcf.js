import{u as D,ag as C,aj as F,a7 as k,r as d,j as e,P as l,B as m,$ as c,U as E,C as o,k as x,Q as h,l as u,m as N,K as O,ak as R,_ as I,a3 as Z,a9 as b,a8 as B,a0 as P}from"./index-DnIZvqWt.js";import{g as U}from"./orderService-CYoibTXQ.js";import{P as z}from"./phone-CUsbluw7.js";import{E as T}from"./external-link-CXviTBgv.js";import{B as v}from"./building-2-YMlv9Wto.js";import{C as A}from"./calendar-ByxQ0_Vi.js";import{H}from"./house-9sLOXRjO.js";function G(){const{t:s}=D(),i=C(),[w]=F(),{isAuthenticated:p}=k(),[a,S]=d.useState(null),[_,y]=d.useState(!0),[g,j]=d.useState(null),n=w.get("orderId");return d.useEffect(()=>{if(!p){i("/");return}if(!n){j("Order ID not found"),y(!1);return}(async()=>{try{console.log("🔍 [ORDER_SUCCESS] Fetching order with ID:",n);const r=await U(n);console.log("🔍 [ORDER_SUCCESS] Order fetch result:",r),r.success&&r.order?(console.log("🔍 [ORDER_SUCCESS] Order data:",r.order),console.log("🔍 [ORDER_SUCCESS] Order items count:",r.order.order_items?.length||0),console.log("🔍 [ORDER_SUCCESS] Order items:",r.order.order_items),S(r.order)):(console.error("❌ [ORDER_SUCCESS] Failed to load order:",r.error),j(r.error||"Failed to load order"))}catch(r){console.error("❌ [ORDER_SUCCESS] Exception:",r),j("An unexpected error occurred")}finally{y(!1)}})()},[n,p,i]),_?e.jsx("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"w-16 h-16 border-4 border-[#fa7b00] border-t-transparent rounded-full animate-spin mx-auto mb-4"}),e.jsx("p",{className:"text-gray-600",children:s("common.loading")})]})}):g||!a?e.jsx("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4",children:e.jsx(l,{className:"h-8 w-8 text-red-600"})}),e.jsx("h2",{className:"text-xl font-semibold text-gray-900 mb-2",children:s("orderSuccess.orderNotFound")}),e.jsx("p",{className:"text-gray-600 mb-6",children:g}),e.jsx(m,{onClick:()=>i("/"),className:"bg-[#fa7b00] hover:bg-[#fa7b00]/90",children:s("common.backToHome")})]})}):e.jsxs("div",{className:"min-h-screen bg-gray-50",children:[e.jsx("div",{className:"bg-white border-b border-gray-200",children:e.jsx("div",{className:"container mx-auto px-4 py-6",children:e.jsxs("div",{className:"text-center",children:[e.jsx(c.div,{initial:{scale:0},animate:{scale:1},transition:{duration:.5},className:"w-20 h-20 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4",children:e.jsx(E,{className:"h-10 w-10 text-green-600"})}),e.jsx(c.h1,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.2},className:"text-3xl font-bold text-gray-900 mb-2",children:s("orderSuccess.title")}),e.jsx(c.p,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.3},className:"text-gray-600",children:s("orderSuccess.description")})]})})}),e.jsx("div",{className:"container mx-auto px-4 py-8",children:e.jsxs("div",{className:"max-w-4xl mx-auto space-y-6",children:[e.jsx(c.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.4},children:e.jsxs(o,{children:[e.jsx(x,{children:e.jsxs(h,{className:"flex items-center space-x-2",children:[e.jsx(l,{className:"h-6 w-6 text-[#fa7b00]"}),e.jsx("span",{children:s("orderSuccess.orderDetails")})]})}),e.jsx(u,{className:"space-y-4",children:e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-600",children:s("orderSuccess.orderNumber")}),e.jsx("p",{className:"font-semibold text-lg",children:a.order_number})]}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-600",children:s("orderSuccess.orderDate")}),e.jsx("p",{className:"font-semibold",children:new Date(a.created_at).toLocaleDateString("fr-DZ",{year:"numeric",month:"long",day:"numeric",hour:"2-digit",minute:"2-digit"})})]}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-600",children:s("orderSuccess.status")}),e.jsx(N,{variant:"outline",className:"text-green-600 border-green-600",children:s(`orderStatus.${a.status}`)})]}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-600",children:s("orderSuccess.paymentMethod")}),e.jsxs("div",{className:"flex items-center space-x-2",children:[a.payment_method==="cash_on_delivery"?e.jsx(O,{className:"h-4 w-4 text-gray-600"}):e.jsx(R,{className:"h-4 w-4 text-gray-600"}),e.jsx("span",{className:"font-semibold",children:a.payment_method==="cash_on_delivery"?s("checkout.cashOnDelivery"):s("checkout.storePickup")})]})]})]})})]})}),e.jsx(c.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.5},children:e.jsxs(o,{children:[e.jsx(x,{children:e.jsxs(h,{className:"flex items-center space-x-2",children:[e.jsx(I,{className:"h-6 w-6 text-[#fa7b00]"}),e.jsx("span",{children:s("orderSuccess.customerInfo")})]})}),e.jsx(u,{children:e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx("div",{className:"w-2 h-2 bg-[#fa7b00] rounded-full"}),e.jsxs("div",{children:[e.jsx("span",{className:"text-sm text-gray-600",children:s("orderSuccess.name")}),e.jsx("p",{className:"font-medium text-gray-900",children:a.consumer_name})]})]}),e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx(z,{className:"h-4 w-4 text-[#fa7b00]"}),e.jsxs("div",{children:[e.jsx("span",{className:"text-sm text-gray-600",children:s("orderSuccess.phone")}),e.jsx("p",{className:"font-medium text-gray-900",children:a.consumer_phone})]})]})]})})]})}),e.jsx(c.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.6},children:e.jsxs(o,{children:[e.jsx(x,{children:e.jsxs(h,{className:"flex items-center space-x-2",children:[e.jsx(Z,{className:"h-6 w-6 text-[#fa7b00]"}),e.jsx("span",{children:s("orderSuccess.deliveryInfo")})]})}),e.jsx(u,{children:e.jsxs("div",{className:"space-y-4",children:[e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx("div",{className:"w-2 h-2 bg-[#fa7b00] rounded-full"}),e.jsxs("div",{children:[e.jsx("span",{className:"text-sm text-gray-600",children:s("orderSuccess.wilaya")}),e.jsx("p",{className:"font-medium text-gray-900",children:a.delivery_wilaya})]})]})}),e.jsxs("div",{className:"flex items-start space-x-3",children:[e.jsx("div",{className:"w-2 h-2 bg-[#fa7b00] rounded-full mt-2"}),e.jsxs("div",{className:"flex-1",children:[e.jsx("span",{className:"text-sm text-gray-600",children:s("orderSuccess.deliveryAddress")}),e.jsx("p",{className:"font-medium text-gray-900 mt-1 leading-relaxed",children:a.delivery_address})]})]}),a.special_instructions&&e.jsxs("div",{className:"flex items-start space-x-3",children:[e.jsx("div",{className:"w-2 h-2 bg-[#fa7b00] rounded-full mt-2"}),e.jsxs("div",{className:"flex-1",children:[e.jsx("span",{className:"text-sm text-gray-600",children:s("orderSuccess.specialInstructions")}),e.jsx("p",{className:"font-medium text-gray-900 mt-1 leading-relaxed",children:a.special_instructions})]})]}),e.jsx("div",{className:"pt-2",children:e.jsxs(m,{variant:"outline",size:"sm",onClick:()=>window.open(a.google_maps_url,"_blank"),className:"text-[#fa7b00] border-[#fa7b00] hover:bg-[#fa7b00] hover:text-white transition-colors",children:[e.jsx(T,{className:"h-4 w-4 mr-2"}),s("orderSuccess.viewOnMaps")]})})]})})]})}),e.jsx(c.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.6},children:e.jsxs("div",{className:"bg-white border border-gray-200 rounded-lg p-4",children:[e.jsxs("h3",{className:"font-semibold text-gray-900 mb-4 flex items-center",children:[e.jsx(l,{className:"h-5 w-5 mr-2"}),s("checkout.step4.orderSummary")]}),!a.order_items||a.order_items.length===0?e.jsxs("div",{className:"text-center py-8",children:[e.jsx(l,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),e.jsx("p",{className:"text-gray-600",children:"No order items found"})]}):e.jsx(e.Fragment,{children:e.jsx("div",{className:"space-y-3 mb-4",children:a.order_items.map((t,r)=>e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx("img",{src:t.product_image||"/placeholder.svg",alt:t.product_name,className:"w-12 h-12 object-cover rounded-md"}),e.jsxs("div",{className:"flex-1 min-w-0",children:[e.jsx("p",{className:"text-sm font-medium text-gray-900 truncate",children:t.product_name}),e.jsxs("p",{className:"text-xs text-gray-500",children:[t.quantity," × ",new Intl.NumberFormat("fr-DZ",{style:"currency",currency:"DZD",minimumFractionDigits:0,maximumFractionDigits:0}).format(t.unit_price)]})]}),e.jsx("p",{className:"text-sm font-medium",children:new Intl.NumberFormat("fr-DZ",{style:"currency",currency:"DZD",minimumFractionDigits:0,maximumFractionDigits:0}).format(t.total_price)})]},r))})}),e.jsx(b,{}),e.jsxs("div",{className:"space-y-2 mt-4",children:[e.jsxs("div",{className:"flex justify-between text-sm",children:[e.jsx("span",{children:s("orderSuccess.subtotal")}),e.jsx("span",{children:new Intl.NumberFormat("fr-DZ",{style:"currency",currency:"DZD",minimumFractionDigits:0,maximumFractionDigits:0}).format(a.subtotal)})]}),e.jsxs("div",{className:"flex justify-between text-sm",children:[e.jsx("span",{children:s("orderSuccess.shipping")}),e.jsx("span",{children:(a.shipping_cost||0)>0?new Intl.NumberFormat("fr-DZ",{style:"currency",currency:"DZD",minimumFractionDigits:0,maximumFractionDigits:0}).format(a.shipping_cost||0):e.jsx("span",{className:"text-green-600",children:"Free"})})]}),e.jsxs("div",{className:"flex justify-between text-sm",children:[e.jsx("span",{children:s("orderSuccess.arouzFee")}),e.jsx("span",{children:(a.total_arouz_fees||0)>0?new Intl.NumberFormat("fr-DZ",{style:"currency",currency:"DZD",minimumFractionDigits:0,maximumFractionDigits:0}).format(a.total_arouz_fees||0):e.jsx("span",{className:"text-green-600",children:"Free"})})]})]}),e.jsx(b,{className:"my-4"}),e.jsxs("div",{className:"flex justify-between font-bold text-lg",children:[e.jsx("span",{children:s("orderSuccess.total")}),e.jsx("span",{className:"text-[#fa7b00]",children:new Intl.NumberFormat("fr-DZ",{style:"currency",currency:"DZD",minimumFractionDigits:0,maximumFractionDigits:0}).format(a.total_amount)})]})]})}),e.jsx(c.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.7},children:e.jsxs("div",{className:"bg-white border border-gray-200 rounded-lg p-6 shadow-sm",children:[e.jsxs("h3",{className:"font-semibold text-gray-900 mb-4 flex items-center text-lg",children:[e.jsx(v,{className:"h-5 w-5 mr-3 text-[#fa7b00]"}),s("checkout.step4.shippingOrigins")]}),!a.order_items||a.order_items.length===0?e.jsxs("div",{className:"text-center py-8",children:[e.jsx(v,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),e.jsx("p",{className:"text-gray-600",children:"No shipping origins found"})]}):e.jsx("div",{className:"space-y-3",children:a.order_items.map((t,r)=>{const f=B(t.supplier_wilaya)||t.supplier_wilaya||"Location TBD";return e.jsxs("div",{className:"flex items-center justify-between p-3 bg-gray-50 rounded-lg",children:[e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx("img",{src:t.product_image||"/placeholder.svg",alt:t.product_name,className:"w-10 h-10 object-cover rounded-md"}),e.jsxs("div",{children:[e.jsx("p",{className:"font-medium text-gray-900 text-sm",children:t.product_name}),e.jsxs("p",{className:"text-xs text-gray-600",children:["Ships from: ",f]})]})]}),e.jsx(N,{variant:"outline",className:"border-gray-300 text-gray-600 text-xs",children:f})]},`${t.id}-${r}`)})})]})}),e.jsx(c.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.8},children:e.jsxs(o,{className:"bg-blue-50 border-blue-200",children:[e.jsx(x,{children:e.jsxs(h,{className:"flex items-center space-x-2 text-blue-900",children:[e.jsx(A,{className:"h-6 w-6"}),e.jsx("span",{children:s("orderSuccess.nextSteps")})]})}),e.jsx(u,{className:"text-blue-800",children:e.jsxs("div",{className:"space-y-3",children:[e.jsxs("div",{className:"flex items-start space-x-3",children:[e.jsx("div",{className:"w-6 h-6 bg-blue-200 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5",children:e.jsx("span",{className:"text-xs font-bold",children:"1"})}),e.jsx("p",{className:"text-sm",children:s("orderSuccess.step1")})]}),e.jsxs("div",{className:"flex items-start space-x-3",children:[e.jsx("div",{className:"w-6 h-6 bg-blue-200 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5",children:e.jsx("span",{className:"text-xs font-bold",children:"2"})}),e.jsx("p",{className:"text-sm",children:s("orderSuccess.step2")})]}),e.jsxs("div",{className:"flex items-start space-x-3",children:[e.jsx("div",{className:"w-6 h-6 bg-blue-200 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5",children:e.jsx("span",{className:"text-xs font-bold",children:"3"})}),e.jsx("p",{className:"text-sm",children:s("orderSuccess.step3")})]})]})})]})}),e.jsxs(c.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.9},className:"flex flex-col sm:flex-row gap-4 justify-center",children:[e.jsxs(m,{onClick:()=>i("/"),variant:"outline",size:"lg",className:"flex items-center space-x-2",children:[e.jsx(H,{className:"h-5 w-5"}),e.jsx("span",{children:s("orderSuccess.backToHome")})]}),e.jsxs(m,{onClick:()=>i("/my-orders"),size:"lg",className:"bg-[#fa7b00] hover:bg-[#fa7b00]/90 text-white flex items-center space-x-2",children:[e.jsx(l,{className:"h-5 w-5"}),e.jsx("span",{children:s("orderSuccess.viewOrders")}),e.jsx(P,{className:"h-4 w-4"})]})]})]})})]})}export{G as OrderSuccessPage};
