import{a1 as u}from"./index-DnIZvqWt.js";async function S(e){try{console.log("🔍 [SUPPLIER_ORDERS] Getting supplier orders with filters:",e);const{data:{user:r}}=await u.auth.getUser();if(!r)return{success:!1,error:"Authentication required"};console.log("🔍 [SUPPLIER_ORDERS] Current user ID:",r.id);const{data:n,error:l}=await u.from("order_items").select("order_id").eq("supplier_account_id",r.id);if(l)return console.error("❌ [SUPPLIER_ORDERS] Error fetching supplier order IDs:",l),{success:!1,error:"Failed to fetch orders"};if(!n||n.length===0)return console.log("ℹ️ [SUPPLIER_ORDERS] No orders found for supplier"),{success:!0,orders:[],total_count:0};const t=n.map(s=>s.order_id);console.log("🔍 [SUPPLIER_ORDERS] Found order IDs for supplier:",t);let o=u.from("orders").select(`
        *,
        order_items (
          id,
          order_id,
          product_id,
          product_name,
          product_image,
          quantity,
          unit_price,
          total_price,
          supplier_account_id,
          supplier_name,
          supplier_phone,
          supplier_city,
          supplier_wilaya,
          marketplace_section,
          category,
          subcategory,
          created_at
        )
      `).in("id",t);e?.status&&(o=o.eq("status",e.status)),e?.date_from&&(o=o.gte("created_at",e.date_from)),e?.date_to&&(o=o.lte("created_at",e.date_to)),e?.search&&(o=o.or(`order_number.ilike.%${e.search}%,consumer_name.ilike.%${e.search}%,consumer_phone.ilike.%${e.search}%`)),e?.limit&&(o=o.limit(e.limit)),e?.offset&&(o=o.range(e.offset,e.offset+(e.limit||10)-1)),o=o.order("created_at",{ascending:!1});const{data:_,error:m,count:p}=await o;if(m)return console.error("❌ [SUPPLIER_ORDERS] Database error:",m),{success:!1,error:"Failed to fetch orders"};if(console.log("✅ [SUPPLIER_ORDERS] Raw orders fetched:",_?.length||0),!_||_.length===0)return{success:!0,orders:[],total_count:0};const i=_.map(s=>{const a=s.order_items||[],f=a.filter(c=>c.supplier_account_id===r.id),d=new Map;return a.filter(c=>c.supplier_account_id!==r.id).forEach(c=>{const E=`${c.supplier_name}-${c.supplier_wilaya}`;d.has(E)||d.set(E,{supplier_name:c.supplier_name,supplier_wilaya:c.supplier_wilaya,item_count:0,total_value:0});const h=d.get(E);h.item_count+=c.quantity,h.total_value+=c.total_price}),{...s,order_items:a,supplier_items:f,other_suppliers:Array.from(d.values())}});return console.log("✅ [SUPPLIER_ORDERS] Processed orders:",{total:i.length,sample:i[0]?{order_number:i[0].order_number,supplier_items:i[0].supplier_items.length,other_suppliers:i[0].other_suppliers.length}:null}),{success:!0,orders:i,total_count:p||i.length}}catch(r){return console.error("❌ [SUPPLIER_ORDERS] Exception:",r),{success:!1,error:"An unexpected error occurred"}}}async function D(e){try{console.log("🔍 [SUPPLIER_ORDER_DETAIL] Getting order details for:",e);const{data:{user:r}}=await u.auth.getUser();if(!r)return{success:!1,error:"Authentication required"};console.log("🔍 [SUPPLIER_ORDER_DETAIL] Current user ID:",r.id),console.log("🔍 [SUPPLIER_ORDER_DETAIL] Order ID to fetch:",e);const{data:n,error:l}=await u.from("order_items").select("order_id").eq("order_id",e).eq("supplier_account_id",r.id);if(l)return console.error("❌ [SUPPLIER_ORDER_DETAIL] Error checking supplier access:",l),{success:!1,error:"Failed to verify order access"};if(!n||n.length===0)return console.error("❌ [SUPPLIER_ORDER_DETAIL] Supplier has no items in this order"),{success:!1,error:"Order not found or access denied"};const{data:t,error:o}=await u.from("orders").select(`
        *,
        order_items (
          id,
          order_id,
          product_id,
          product_name,
          product_image,
          quantity,
          unit_price,
          total_price,
          supplier_account_id,
          supplier_name,
          supplier_phone,
          supplier_city,
          supplier_wilaya,
          marketplace_section,
          category,
          subcategory,
          created_at
        )
      `).eq("id",e).single();if(console.log("🔍 [SUPPLIER_ORDER_DETAIL] Raw query result:",{order:t,error:o}),o)return console.error("❌ [SUPPLIER_ORDER_DETAIL] Database error:",o),{success:!1,error:`Order not found: ${o.message}`};if(!t)return console.error("❌ [SUPPLIER_ORDER_DETAIL] No order returned from query"),{success:!1,error:"Order not found in database"};console.log("✅ [SUPPLIER_ORDER_DETAIL] Order fetched:",{order_number:t.order_number,total_items:t.order_items?.length||0,order_items_sample:t.order_items?.slice(0,2)});const _=t.order_items?.filter(s=>s.supplier_account_id===r.id)||[];if(console.log("🔍 [SUPPLIER_ORDER_DETAIL] Filtering supplier items:",{total_items:t.order_items?.length||0,supplier_items_found:_.length,current_user_id:r.id,all_supplier_ids:t.order_items?.map(s=>s.supplier_account_id)||[]}),_.length===0)return console.error("❌ [SUPPLIER_ORDER_DETAIL] No items found for supplier:",{user_id:r.id,order_items:t.order_items?.map(s=>({id:s.id,supplier_account_id:s.supplier_account_id,product_name:s.product_name}))||[]}),{success:!1,error:"You do not have access to this order - no items found for your account"};const m=t.order_items||[],p=new Map;m.filter(s=>s.supplier_account_id!==r.id).forEach(s=>{const a=`${s.supplier_name}-${s.supplier_wilaya}`;p.has(a)||p.set(a,{supplier_name:s.supplier_name,supplier_wilaya:s.supplier_wilaya,item_count:0,total_value:0});const f=p.get(a);f.item_count+=s.quantity,f.total_value+=s.total_price});const i={...t,order_items:m,supplier_items:_,other_suppliers:Array.from(p.values())};return console.log("✅ [SUPPLIER_ORDER_DETAIL] Order details retrieved:",{order_number:i.order_number,supplier_items:i.supplier_items.length,other_suppliers:i.other_suppliers.length}),{success:!0,order:i}}catch(r){return console.error("❌ [SUPPLIER_ORDER_DETAIL] Exception:",r),{success:!1,error:"An unexpected error occurred"}}}async function I(e){try{console.log("🔄 [UPDATE_ORDER_STATUS] Updating order status:",e);const{data:{user:r}}=await u.auth.getUser();if(!r)return{success:!1,error:"Authentication required"};const{data:n}=await u.from("order_items").select("id").eq("order_id",e.order_id).eq("supplier_account_id",r.id);if(!n||n.length===0)return{success:!1,error:"You do not have access to this order"};const l={status:e.new_status,updated_at:new Date().toISOString()};e.new_status==="confirmed"?l.confirmed_at=new Date().toISOString():e.new_status==="delivered"&&(l.delivered_at=new Date().toISOString());const{error:t}=await u.from("orders").update(l).eq("id",e.order_id);return t?(console.error("❌ [UPDATE_ORDER_STATUS] Database error:",t),{success:!1,error:"Failed to update order status"}):(console.log("✅ [UPDATE_ORDER_STATUS] Order status updated successfully"),{success:!0})}catch(r){return console.error("❌ [UPDATE_ORDER_STATUS] Exception:",r),{success:!1,error:"An unexpected error occurred"}}}async function P(){try{console.log("📊 [SUPPLIER_STATS] Getting supplier order statistics...");const{data:{user:e}}=await u.auth.getUser();if(!e)return{success:!1,error:"Authentication required"};const{data:r,error:n}=await u.from("order_items").select("order_id").eq("supplier_account_id",e.id);if(n)return console.error("❌ [SUPPLIER_STATS] Error fetching supplier order IDs:",n),{success:!1,error:"Failed to fetch statistics"};if(!r||r.length===0)return{success:!0,stats:{total_orders:0,pending_orders:0,confirmed_orders:0,shipped_orders:0,delivered_orders:0,cancelled_orders:0,total_revenue:0,this_month_revenue:0,this_month_orders:0,average_order_value:0}};const l=r.map(d=>d.order_id),{data:t,error:o}=await u.from("orders").select(`
        id,
        status,
        total_amount,
        created_at,
        order_items (
          supplier_account_id,
          total_price
        )
      `).in("id",l);if(o)return console.error("❌ [SUPPLIER_STATS] Database error:",o),{success:!1,error:"Failed to fetch statistics"};if(!t||t.length===0)return{success:!0,stats:{total_orders:0,pending_orders:0,confirmed_orders:0,shipped_orders:0,delivered_orders:0,cancelled_orders:0,total_revenue:0,this_month_revenue:0,this_month_orders:0,average_order_value:0}};const _=new Date,m=new Date(_.getFullYear(),_.getMonth(),1);let p=0,i=0,s=0;const a={pending:0,confirmed:0,shipped:0,delivered:0,cancelled:0};t.forEach(d=>{a[d.status]++;const c=d.order_items.filter(h=>h.supplier_account_id===e.id).reduce((h,R)=>h+R.total_price,0);p+=c,new Date(d.created_at)>=m&&(i+=c,s++)});const f={total_orders:t.length,pending_orders:a.pending,confirmed_orders:a.confirmed,shipped_orders:a.shipped,delivered_orders:a.delivered,cancelled_orders:a.cancelled,total_revenue:p,this_month_revenue:i,this_month_orders:s,average_order_value:t.length>0?p/t.length:0};return console.log("✅ [SUPPLIER_STATS] Statistics calculated:",f),{success:!0,stats:f}}catch(e){return console.error("❌ [SUPPLIER_STATS] Exception:",e),{success:!1,error:"An unexpected error occurred"}}}export{S as a,D as b,P as g,I as u};
