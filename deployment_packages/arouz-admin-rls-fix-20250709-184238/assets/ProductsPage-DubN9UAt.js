import{c as ve,u as _,r as h,j as e,aw as Qe,I as G,ce as _e,cf as Be,B as n,cg as Ve,L as F,ch as Ue,m as R,ar as L,D as J,d as K,e as W,f as X,g as Z,h as Y,ag as de,bb as He,C as pe,aa as B,ac as V,ad as U,ae as H,af as q,aq as te,ci as ne,bx as ue,by as me,bC as he,bG as D,S as qe,bE as Ne,w as Ge,k as Je,l as Ke,cj as We,i as Xe,P as Ze,U as Ye,N as es,aI as ss,au as ye,ab as ts,ay as w,T as rs,b$ as as,ck as cs,cl as ge,cm as is,a6 as ls}from"./index-DnIZvqWt.js";import{A as ns}from"./AdminLayout-DBeU98xA.js";import{S as os}from"./switch-BVgo8oOm.js";import{S as re,a as ae,b as ce,c as ie,d as O}from"./select-DN5XplGq.js";import{S as we,f as Q,D as be,u as je}from"./useProductsFactory-Dhr2YWoU.js";import{S as fe,$ as ds,P as us}from"./ProductEditDialog-vHbmNWF5.js";import{S as le}from"./square-pen-Dxl-IRoo.js";import{C as oe}from"./copy-CnDiqUKs.js";import{U as ms}from"./upload-Bf1AuFkI.js";import"./house-9sLOXRjO.js";import"./settings-CRvmRnrZ.js";import"./features-CbSLR5ud.js";import"./idGenerator-8IGaQKCc.js";import"./categories-k_ueMvEL.js";import"./progress-Br1Z2UhX.js";import"./enhanced-dropdown-Ce8WtuQh.js";/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const hs=ve("Archive",[["rect",{width:"20",height:"5",x:"2",y:"3",rx:"1",key:"1wp1u1"}],["path",{d:"M4 8v11a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8",key:"1s80jp"}],["path",{d:"M10 12h4",key:"a56b0p"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const xs=ve("Ellipsis",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"19",cy:"12",r:"1",key:"1wjl8i"}],["circle",{cx:"5",cy:"12",r:"1",key:"1pcz8c"}]]);function ps({filters:s,onFilterChange:g,onResetFilters:f,manufacturers:i,maxPrice:b=1e6}){const{t:r}=_();h.useState(!1);const[o,C]=h.useState(s.priceRange||[0,b]),[l,N]=h.useState(!1),[v,S]=h.useState("");h.useEffect(()=>{s.priceRange&&C(s.priceRange)},[s.priceRange]);const $=a=>{C([a[0],a[1]])},E=()=>{g({priceRange:o})},d=a=>{const p={};p[a]=void 0,a==="priceRange"&&C([0,b]),g(p)},M=()=>{if(!v.trim())return;const a=localStorage.getItem("productFilterPresets"),p=a?JSON.parse(a):[],A={id:Date.now().toString(),name:v,filters:s};p.push(A),localStorage.setItem("productFilterPresets",JSON.stringify(p)),S(""),N(!1)},y=()=>s.search&&s.search.trim()!==""||s.category&&s.category!=="all"||s.status&&s.status!=="all"||s.manufacturer&&s.manufacturer!=="all"||s.priceRange!==null||s.inStock!==void 0,u=(()=>{const a=[];return s.search&&s.search.trim()!==""&&a.push("search"),s.category&&s.category!=="all"&&a.push("category"),s.status&&s.status!=="all"&&a.push("status"),s.manufacturer&&s.manufacturer!=="all"&&a.push("manufacturer"),s.priceRange&&a.push("priceRange"),s.inStock!==void 0&&a.push("inStock"),a})();return e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex flex-col sm:flex-row gap-3",children:[e.jsxs("div",{className:"flex-1 relative",children:[e.jsx(Qe,{className:"absolute rtl:right-3 ltr:left-3 top-3 h-4 w-4 text-muted-foreground"}),e.jsx(G,{placeholder:r("products.search"),value:s.search||"",onChange:a=>g({search:a.target.value}),className:"rtl:pr-9 ltr:pl-9"})]}),e.jsxs("div",{className:"flex flex-wrap gap-2",children:[e.jsxs(re,{value:s.category||"all",onValueChange:a=>g({category:a}),children:[e.jsx(ae,{className:"w-auto min-w-[150px]",children:e.jsx(ce,{placeholder:r("products.category")})}),e.jsxs(ie,{children:[e.jsx(O,{value:"all",children:r("products.allCategories")}),e.jsx(O,{value:"tyres",children:"Tyres & Related Products"}),e.jsx(O,{value:"brakes",children:"Brake Parts & Systems"})]})]}),e.jsxs(re,{value:s.status||"all",onValueChange:a=>g({status:a}),children:[e.jsx(ae,{className:"w-auto min-w-[150px]",children:e.jsx(ce,{placeholder:r("products.status")})}),e.jsxs(ie,{children:[e.jsx(O,{value:"all",children:r("products.allStatuses")}),e.jsx(O,{value:"active",children:r("products.active")}),e.jsx(O,{value:"draft",children:r("products.draft")}),e.jsx(O,{value:"pending_approval",children:r("products.pendingApproval")}),e.jsx(O,{value:"out_of_stock",children:r("products.outOfStock")}),e.jsx(O,{value:"discontinued",children:r("products.discontinued")})]})]}),e.jsxs(_e,{children:[e.jsx(Be,{asChild:!0,children:e.jsxs(n,{variant:"outline",className:"gap-2",children:[e.jsx(we,{className:"h-4 w-4"}),r("products.moreFilters")]})}),e.jsx(Ve,{className:"w-80 p-4",align:"end",children:e.jsxs("div",{className:"space-y-4",children:[e.jsx("h4",{className:"font-medium",children:r("products.advancedFilters")}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(F,{children:r("products.manufacturer")}),e.jsxs(re,{value:s.manufacturer||"all",onValueChange:a=>g({manufacturer:a}),children:[e.jsx(ae,{children:e.jsx(ce,{placeholder:r("products.allManufacturers")})}),e.jsxs(ie,{children:[e.jsx(O,{value:"all",children:r("products.allManufacturers")}),i.map(a=>e.jsx(O,{value:a,children:a},a))]})]})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsxs("div",{className:"flex justify-between",children:[e.jsx(F,{children:r("products.priceRange")}),e.jsxs("span",{className:"text-sm text-muted-foreground",children:[Q(o[0])," - ",Q(o[1])]})]}),e.jsx(Ue,{defaultValue:[o[0],o[1]],max:b,step:1e3,value:[o[0],o[1]],onValueChange:$,onValueCommit:E,className:"my-6"})]}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(os,{id:"in-stock",checked:s.inStock,onCheckedChange:a=>g({inStock:a})}),e.jsx(F,{htmlFor:"in-stock",children:r("products.inStockOnly")})]}),e.jsxs("div",{className:"flex justify-between pt-2",children:[e.jsxs(n,{variant:"outline",size:"sm",onClick:()=>N(!0),disabled:!y(),children:[e.jsx(fe,{className:"h-4 w-4 mr-2"}),r("products.savePreset")]}),e.jsx(n,{variant:"destructive",size:"sm",onClick:f,disabled:!y(),children:r("products.resetFilters")})]})]})})]})]})]}),u.length>0&&e.jsxs("div",{className:"flex flex-wrap gap-2 items-center",children:[e.jsxs("span",{className:"text-sm text-muted-foreground",children:[r("products.activeFilters"),":"]}),s.search&&s.search.trim()!==""&&e.jsxs(R,{variant:"outline",className:"flex items-center gap-1 px-3 py-1",children:[e.jsxs("span",{children:[r("products.search"),": ",s.search]}),e.jsx(n,{variant:"ghost",size:"icon",className:"h-4 w-4 ml-1 rtl:mr-1 rtl:ml-0",onClick:()=>d("search"),children:e.jsx(L,{className:"h-3 w-3"})})]}),s.category&&s.category!=="all"&&e.jsxs(R,{variant:"outline",className:"flex items-center gap-1 px-3 py-1",children:[e.jsxs("span",{children:[r("products.category"),": ",r(`products.${s.category}`)]}),e.jsx(n,{variant:"ghost",size:"icon",className:"h-4 w-4 ml-1 rtl:mr-1 rtl:ml-0",onClick:()=>d("category"),children:e.jsx(L,{className:"h-3 w-3"})})]}),s.status&&s.status!=="all"&&e.jsxs(R,{variant:"outline",className:"flex items-center gap-1 px-3 py-1",children:[e.jsxs("span",{children:[r("products.status"),": ",r(`products.${s.status}`)]}),e.jsx(n,{variant:"ghost",size:"icon",className:"h-4 w-4 ml-1 rtl:mr-1 rtl:ml-0",onClick:()=>d("status"),children:e.jsx(L,{className:"h-3 w-3"})})]}),s.manufacturer&&s.manufacturer!=="all"&&e.jsxs(R,{variant:"outline",className:"flex items-center gap-1 px-3 py-1",children:[e.jsxs("span",{children:[r("products.manufacturer"),": ",s.manufacturer]}),e.jsx(n,{variant:"ghost",size:"icon",className:"h-4 w-4 ml-1 rtl:mr-1 rtl:ml-0",onClick:()=>d("manufacturer"),children:e.jsx(L,{className:"h-3 w-3"})})]}),s.priceRange&&e.jsxs(R,{variant:"outline",className:"flex items-center gap-1 px-3 py-1",children:[e.jsxs("span",{children:[r("products.priceRange"),": ",Q(s.priceRange[0])," - ",Q(s.priceRange[1])]}),e.jsx(n,{variant:"ghost",size:"icon",className:"h-4 w-4 ml-1 rtl:mr-1 rtl:ml-0",onClick:()=>d("priceRange"),children:e.jsx(L,{className:"h-3 w-3"})})]}),s.inStock!==void 0&&e.jsxs(R,{variant:"outline",className:"flex items-center gap-1 px-3 py-1",children:[e.jsx("span",{children:r("products.inStockOnly")}),e.jsx(n,{variant:"ghost",size:"icon",className:"h-4 w-4 ml-1 rtl:mr-1 rtl:ml-0",onClick:()=>d("inStock"),children:e.jsx(L,{className:"h-3 w-3"})})]}),e.jsx(n,{variant:"ghost",size:"sm",className:"h-8 text-muted-foreground",onClick:f,children:r("products.clearAll")})]}),e.jsx(J,{open:l,onOpenChange:N,children:e.jsxs(K,{children:[e.jsxs(W,{children:[e.jsx(X,{children:r("products.saveFilterPreset")}),e.jsx(Z,{children:r("products.saveFilterPresetDescription")})]}),e.jsx("div",{className:"py-4",children:e.jsx(G,{placeholder:r("products.presetName"),value:v,onChange:a=>S(a.target.value)})}),e.jsxs(Y,{children:[e.jsx(n,{variant:"outline",onClick:()=>N(!1),children:r("actions.cancel")}),e.jsxs(n,{onClick:M,disabled:!v.trim(),children:[e.jsx(fe,{className:"h-4 w-4 mr-2"}),r("actions.save")]})]})]})})]})}function gs({product:s,isSelected:g,onSelect:f,onEdit:i,onDelete:b,onDuplicate:r,onFeature:o,viewMode:C="grid"}){const{t:l}=_();de();const[N,v]=h.useState(!1),{isSupplier:S,isMerchant:$}=He(),E=()=>{if(S()){if(s.wholesalePricingTiers&&s.wholesalePricingTiers.length>0){const a=[...s.wholesalePricingTiers].sort((p,A)=>p.minQuantity-A.minQuantity)[0];return{price:a.price,minQuantity:a.minQuantity,isWholesale:!0}}return{price:0}}else if($())return{price:s.retailPrice||0};return{price:s.retailPrice||0}},d=()=>{window.open(`/${s.id}`,"_blank")},M={active:"bg-algerian-green text-white",draft:"bg-muted text-muted-foreground",pending_approval:"bg-yellow-500 text-white",out_of_stock:"bg-destructive text-destructive-foreground",discontinued:"bg-purple-500 text-white"},y={active:l("products.active"),draft:l("products.draft"),pending_approval:l("products.pendingApproval"),out_of_stock:l("products.outOfStock"),discontinued:l("products.discontinued")},T=s.stockQuantity>0&&s.stockQuantity<5;return C==="list"?e.jsx(pe,{className:`group relative overflow-hidden transition-all duration-300 bg-white border-2 hover:border-electric-orange/30 ${g?"ring-2 ring-electric-orange border-electric-orange shadow-lg":"border-gray-200 hover:shadow-md"}`,onMouseEnter:()=>v(!0),onMouseLeave:()=>v(!1),children:e.jsxs("div",{className:"flex items-center p-5",children:[e.jsxs("div",{className:"flex items-center gap-4 flex-1",children:[e.jsx(B,{checked:g,onCheckedChange:()=>f(s.id),className:"h-5 w-5 border-2 border-gray-300 data-[state=checked]:bg-electric-orange data-[state=checked]:border-electric-orange"}),e.jsx("div",{className:"relative h-16 w-16 overflow-hidden rounded-lg bg-gray-50 border border-gray-200",children:e.jsx("img",{src:s.primaryImage||"/placeholder.svg",alt:s.name,className:"object-cover w-full h-full transition-transform duration-300 group-hover:scale-105"})}),e.jsxs("div",{className:"flex-1 min-w-0",children:[e.jsx("h3",{className:"font-semibold text-gray-900 line-clamp-1 group-hover:text-electric-orange transition-colors",children:s.name}),e.jsxs("div",{className:"flex items-center gap-3 text-sm text-gray-500 mt-1",children:[e.jsxs("span",{className:"font-medium",children:["SKU: ",s.sku]}),e.jsx("span",{children:"•"}),e.jsx(R,{variant:"secondary",className:"text-xs bg-gray-100 text-gray-700 capitalize",children:s.category}),e.jsx("span",{children:"•"}),e.jsx("span",{className:"font-medium",children:s.manufacturer})]})]})]}),e.jsxs("div",{className:"flex items-center gap-6",children:[e.jsxs("div",{className:"text-right space-y-1",children:[e.jsxs("div",{className:"space-y-1",children:[e.jsx("div",{className:"text-xl font-bold text-electric-orange",children:(()=>{const u=E();return Q(u.price)})()}),(()=>{const u=E();return u.isWholesale&&u.minQuantity?e.jsxs("div",{className:"text-xs text-electric-orange/80 font-medium",children:["from ",u.minQuantity," units"]}):null})()]}),e.jsxs("div",{className:"text-sm text-gray-500",children:[e.jsxs("span",{className:"font-medium",children:[l("products.stock"),":"]}),e.jsx("span",{className:`ml-1 font-semibold ${s.stockQuantity>0?T?"text-yellow-600":"text-green-600":"text-red-600"}`,children:s.stockQuantity})]})]}),e.jsx(R,{className:`${M[s.status]} font-medium shadow-sm`,children:y[s.status]}),e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsx(V,{children:e.jsxs(U,{children:[e.jsx(H,{asChild:!0,children:e.jsx(n,{variant:"ghost",size:"icon",onClick:()=>i(s),className:"hover:bg-blue-50 hover:text-blue-600 transition-colors",children:e.jsx(le,{className:"h-4 w-4"})})}),e.jsx(q,{children:e.jsx("p",{children:l("products.edit")})})]})}),e.jsx(V,{children:e.jsxs(U,{children:[e.jsx(H,{asChild:!0,children:e.jsx(n,{variant:"ghost",size:"icon",onClick:d,className:"text-electric-orange hover:bg-orange-50 transition-colors",children:e.jsx(te,{className:"h-4 w-4"})})}),e.jsx(q,{children:e.jsx("p",{children:l("actions.view")})})]})}),e.jsx(V,{children:e.jsxs(U,{children:[e.jsx(H,{asChild:!0,children:e.jsx(n,{variant:"ghost",size:"icon",onClick:()=>r(s),className:"hover:bg-green-50 hover:text-green-600 transition-colors",children:e.jsx(oe,{className:"h-4 w-4"})})}),e.jsx(q,{children:e.jsx("p",{children:l("products.duplicate")})})]})}),e.jsx(V,{children:e.jsxs(U,{children:[e.jsx(H,{asChild:!0,children:e.jsx(n,{variant:"ghost",size:"icon",onClick:()=>b(s.id),className:"hover:bg-red-50 hover:text-red-600 transition-colors",children:e.jsx(ne,{className:"h-4 w-4"})})}),e.jsx(q,{children:e.jsx("p",{children:l("products.delete")})})]})})]})]})]})}):e.jsxs(pe,{className:`group relative overflow-hidden transition-all duration-300 h-[450px] flex flex-col bg-white border-2 hover:border-electric-orange/30 ${g?"ring-2 ring-electric-orange border-electric-orange shadow-lg":"border-gray-200 hover:shadow-xl"}`,onMouseEnter:()=>v(!0),onMouseLeave:()=>v(!1),children:[e.jsx("div",{className:"absolute left-3 top-3 z-20",children:e.jsx(B,{checked:g,onCheckedChange:()=>f(s.id),className:"h-5 w-5 bg-white/90 border-2 border-gray-300 data-[state=checked]:bg-electric-orange data-[state=checked]:border-electric-orange shadow-sm"})}),e.jsx("div",{className:"absolute right-3 top-3 z-20 rtl:right-auto rtl:left-3",children:e.jsxs(ue,{children:[e.jsx(me,{asChild:!0,children:e.jsx(n,{variant:"ghost",size:"icon",className:"h-8 w-8 bg-white/90 text-midnight-blue hover:bg-white hover:text-electric-orange shadow-sm border border-gray-200",children:e.jsx(xs,{className:"h-4 w-4"})})}),e.jsxs(he,{align:"end",className:"w-48",children:[e.jsxs(D,{onClick:()=>i(s),className:"cursor-pointer",children:[e.jsx(le,{className:"mr-2 h-4 w-4 rtl:ml-2 rtl:mr-0 text-blue-600"}),e.jsx("span",{className:"font-medium",children:l("products.edit")})]}),e.jsxs(D,{onClick:d,className:"cursor-pointer",children:[e.jsx(te,{className:"mr-2 h-4 w-4 rtl:ml-2 rtl:mr-0 text-electric-orange"}),e.jsx("span",{className:"font-medium",children:l("actions.view")})]}),e.jsxs(D,{onClick:()=>r(s),className:"cursor-pointer",children:[e.jsx(oe,{className:"mr-2 h-4 w-4 rtl:ml-2 rtl:mr-0 text-green-600"}),e.jsx("span",{className:"font-medium",children:l("products.duplicate")})]}),o&&e.jsxs(D,{onClick:()=>o(s),className:"cursor-pointer",children:[e.jsx(qe,{className:"mr-2 h-4 w-4 rtl:ml-2 rtl:mr-0 text-yellow-500"}),e.jsx("span",{className:"font-medium",children:l("products.feature")})]}),e.jsx(Ne,{}),e.jsxs(D,{onClick:()=>b(s.id),className:"text-destructive focus:text-destructive cursor-pointer",children:[e.jsx(ne,{className:"mr-2 h-4 w-4 rtl:ml-2 rtl:mr-0"}),e.jsx("span",{className:"font-medium",children:l("products.delete")})]})]})]})}),e.jsxs("div",{className:"relative aspect-square overflow-hidden bg-gray-50",children:[e.jsx("img",{src:s.primaryImage||"/placeholder.svg",alt:s.name,className:"object-cover w-full h-full transition-transform duration-300 group-hover:scale-105"}),e.jsx(R,{className:`absolute bottom-3 left-3 rtl:left-auto rtl:right-3 shadow-sm font-medium ${M[s.status]}`,children:y[s.status]}),T&&e.jsxs("div",{className:"absolute top-12 right-3 bg-yellow-500 text-white px-2 py-1 rounded-md text-xs font-medium flex items-center shadow-sm",children:[e.jsx(Ge,{className:"h-3 w-3 mr-1"}),l("products.lowStock")]}),s.stockQuantity===0&&e.jsx("div",{className:"absolute inset-0 bg-black/60 flex items-center justify-center",children:e.jsx("div",{className:"bg-white/90 px-4 py-2 rounded-lg",children:e.jsx("span",{className:"text-gray-900 font-bold text-sm",children:l("products.outOfStock")})})})]}),e.jsx(Je,{className:"p-5 pb-3",children:e.jsx("div",{className:"space-y-2",children:e.jsxs("div",{className:"flex justify-between items-start gap-3",children:[e.jsxs("div",{className:"flex-1 min-w-0",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900 line-clamp-1 group-hover:text-electric-orange transition-colors",children:s.name}),e.jsx("div",{className:"flex items-center gap-2 mt-1",children:e.jsx(R,{variant:"secondary",className:"text-xs font-medium bg-gray-100 text-gray-700 capitalize",children:s.category})}),e.jsxs("div",{className:"mt-2 space-y-1",children:[e.jsxs("div",{className:"text-xs text-gray-500",children:["SKU: ",s.sku]}),s.partArticleNumber&&e.jsxs("div",{className:"text-xs text-gray-500",children:["Part No: ",s.partArticleNumber]})]})]}),e.jsx("div",{className:"text-right",children:e.jsxs("div",{className:"space-y-1",children:[e.jsx("div",{className:"text-xl font-bold text-electric-orange",children:(()=>{const u=E();return Q(u.price)})()}),(()=>{const u=E();return u.isWholesale&&u.minQuantity?e.jsxs("div",{className:"text-xs text-electric-orange/80 font-medium",children:["from ",u.minQuantity," units"]}):null})()]})})]})})}),e.jsx(Ke,{className:"p-5 pt-0 flex-1 flex flex-col",children:e.jsx("div",{className:"space-y-3 flex-1",children:e.jsxs("div",{className:"grid grid-cols-2 gap-3 pt-2",children:[e.jsxs("div",{className:"space-y-1",children:[e.jsx("div",{className:"text-xs font-medium text-gray-500 uppercase tracking-wide",children:l("products.stock")}),e.jsxs("div",{className:`text-sm font-semibold ${s.stockQuantity>0?T?"text-yellow-600":"text-green-600":"text-red-600"}`,children:[s.stockQuantity," ",l("products.units")]})]}),e.jsxs("div",{className:"space-y-1",children:[e.jsx("div",{className:"text-xs font-medium text-gray-500 uppercase tracking-wide",children:l("products.manufacturer")}),e.jsx("div",{className:"text-sm font-medium text-gray-900 truncate",children:s.manufacturer})]})]})})}),e.jsx(We,{className:"p-5 pt-3 border-t border-gray-100 mt-auto",children:e.jsxs("div",{className:"flex gap-2 w-full",children:[e.jsxs(n,{variant:"outline",size:"sm",onClick:()=>i(s),className:"flex-1 border-blue-200 text-blue-600 hover:bg-blue-50 hover:border-blue-300 transition-colors",children:[e.jsx(le,{className:"mr-2 h-4 w-4 rtl:ml-2 rtl:mr-0"}),l("actions.edit")]}),e.jsxs(n,{variant:"default",size:"sm",className:"flex-1 bg-electric-orange hover:bg-electric-orange/90 text-white transition-colors",onClick:d,children:[e.jsx(te,{className:"mr-2 h-4 w-4 rtl:ml-2 rtl:mr-0"}),l("actions.view")]})]})})]})}function js({products:s,isLoading:g,viewMode:f,selectedProductIds:i,onSelectProduct:b,onEditProduct:r,onDeleteProduct:o,onDuplicateProduct:C,onFeatureProduct:l}){const{t:N}=_(),v=de();return g?e.jsxs("div",{className:"flex flex-col items-center justify-center py-12 bg-white rounded-lg",children:[e.jsx(Xe,{className:"h-8 w-8 text-electric-orange animate-spin mb-4"}),e.jsx("h3",{className:"text-lg font-medium",children:N("products.loading")}),e.jsx("p",{className:"text-muted-foreground mt-1",children:N("products.loadingDescription")})]}):s.length===0?e.jsxs("div",{className:"flex flex-col items-center justify-center py-12 bg-white rounded-lg",children:[e.jsx("div",{className:"h-16 w-16 bg-muted/30 rounded-full flex items-center justify-center mb-4",children:e.jsx(Ze,{className:"h-8 w-8 text-muted-foreground"})}),e.jsx("h3",{className:"text-lg font-medium",children:N("products.noProductsFound")}),e.jsx("p",{className:"text-muted-foreground mt-1 mb-4",children:N("products.tryAdjustingFilters")}),e.jsx(n,{className:"gap-2 bg-electric-orange hover:bg-electric-orange/90",onClick:()=>v("/app/products-table/tyres"),children:N("products.goToProductsTable")})]}):e.jsx("div",{className:f==="grid"?"grid gap-6 grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4":"flex flex-col gap-3",children:s.map(S=>e.jsx(gs,{product:S,isSelected:i.includes(S.id),onSelect:b,onEdit:r,onDelete:o,onDuplicate:C,onFeature:l,viewMode:f},S.id))})}function fs({selectedCount:s,onClearSelection:g,onDelete:f,onUpdateStatus:i,onExport:b,onShareByEmail:r}){const{t:o}=_(),[C,l]=h.useState(!1),N=()=>{l(!1),f()},v=()=>{r?r():w.info("Email sharing feature coming soon")};return e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"fixed bottom-0 left-0 right-0 bg-background border-t shadow-lg z-50 py-3 px-4 flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("span",{className:"font-medium",children:o("products.selectedItems",{count:s})}),e.jsxs(n,{variant:"ghost",size:"sm",className:"h-8 w-8 p-0",onClick:g,children:[e.jsx(L,{className:"h-4 w-4"}),e.jsx("span",{className:"sr-only",children:o("actions.clearSelection")})]})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsxs(ue,{children:[e.jsx(me,{asChild:!0,children:e.jsx(n,{variant:"outline",size:"sm",className:"h-9",children:o("products.setStatus")})}),e.jsxs(he,{align:"end",children:[e.jsxs(D,{onClick:()=>i("active"),children:[e.jsx(Ye,{className:"mr-2 h-4 w-4 text-algerian-green"}),e.jsx("span",{children:o("products.markAsActive")})]}),e.jsxs(D,{onClick:()=>i("draft"),children:[e.jsx(es,{className:"mr-2 h-4 w-4 text-muted-foreground"}),e.jsx("span",{children:o("products.markAsDraft")})]}),e.jsxs(D,{onClick:()=>i("out_of_stock"),children:[e.jsx(ss,{className:"mr-2 h-4 w-4 text-destructive"}),e.jsx("span",{children:o("products.markAsOutOfStock")})]}),e.jsxs(D,{onClick:()=>i("discontinued"),children:[e.jsx(hs,{className:"mr-2 h-4 w-4 text-purple-500"}),e.jsx("span",{children:o("products.markAsDiscontinued")})]})]})]}),e.jsxs(n,{variant:"outline",size:"sm",className:"h-9",onClick:b,children:[e.jsx(be,{className:"mr-2 h-4 w-4"}),o("products.export")]}),e.jsxs(n,{variant:"outline",size:"sm",className:"h-9",onClick:v,children:[e.jsx(ye,{className:"mr-2 h-4 w-4"}),o("products.shareByEmail")]}),e.jsxs(n,{variant:"destructive",size:"sm",className:"h-9",onClick:()=>l(!0),children:[e.jsx(ne,{className:"mr-2 h-4 w-4"}),o("products.delete")]})]})]}),e.jsx(J,{open:C,onOpenChange:l,children:e.jsxs(K,{children:[e.jsxs(W,{children:[e.jsxs(X,{className:"flex items-center gap-2",children:[e.jsx(ts,{className:"h-5 w-5 text-destructive"}),o("products.confirmDelete")]}),e.jsx(Z,{children:o("products.deleteSelectedConfirmation",{count:s})})]}),e.jsxs(Y,{children:[e.jsx(n,{variant:"outline",onClick:()=>l(!1),children:o("actions.cancel")}),e.jsx(n,{variant:"destructive",onClick:N,children:o("products.delete")})]})]})})]})}function vs({isOpen:s,onClose:g,products:f}){const{t:i}=_(),[b,r]=h.useState(""),[o,C]=h.useState(`${i("products.checkOutTheseProducts")}`),[l,N]=h.useState(""),[v,S]=h.useState(!0),[$,E]=h.useState(!0),[d,M]=h.useState(!0),[y,T]=h.useState(!1),u=()=>{let m=`${l}

${i("products.selectedProducts")}:

`;return f.forEach((I,ee)=>{m+=`${ee+1}. ${I.name}
`,d&&I.description&&(m+=`   ${I.description}
`),$&&I.retailPrice&&(m+=`   ${i("products.price")}: ${Q(I.retailPrice)}
`),v&&I.primaryImage&&(m+=`   ${i("products.image")}: ${I.primaryImage}
`),m+=`
`}),m+=`
${i("products.emailFooter")}`,m},a=()=>{const m=encodeURIComponent(u()),I=encodeURIComponent(o);return`mailto:${b}?subject=${I}&body=${m}`},p=()=>{if(!b){w.error(i("products.emailRequired"));return}window.location.href=a(),setTimeout(()=>{g(),w.success(i("products.emailClientOpened"))},500)},A=()=>{navigator.clipboard.writeText(u()).then(()=>{T(!0),w.success(i("products.copiedToClipboard")),setTimeout(()=>{T(!1)},2e3)}).catch(()=>{w.error(i("products.failedToCopy"))})};return e.jsx(J,{open:s,onOpenChange:g,children:e.jsxs(K,{className:"sm:max-w-[600px]",children:[e.jsxs(W,{children:[e.jsxs(X,{className:"flex items-center gap-2",children:[e.jsx(ye,{className:"h-5 w-5"}),i("products.shareProductsByEmail")]}),e.jsx(Z,{children:i("products.shareProductsDescription",{count:f.length})})]}),e.jsxs("div",{className:"grid gap-4 py-4",children:[e.jsxs("div",{className:"grid grid-cols-4 items-center gap-4",children:[e.jsx(F,{htmlFor:"email",className:"text-right",children:i("products.recipientEmail")}),e.jsx(G,{id:"email",type:"email",value:b,onChange:m=>r(m.target.value),placeholder:"<EMAIL>",className:"col-span-3"})]}),e.jsxs("div",{className:"grid grid-cols-4 items-center gap-4",children:[e.jsx(F,{htmlFor:"subject",className:"text-right",children:i("products.emailSubject")}),e.jsx(G,{id:"subject",value:o,onChange:m=>C(m.target.value),className:"col-span-3"})]}),e.jsxs("div",{className:"grid grid-cols-4 items-start gap-4",children:[e.jsx(F,{htmlFor:"message",className:"text-right pt-2",children:i("products.message")}),e.jsx(rs,{id:"message",value:l,onChange:m=>N(m.target.value),placeholder:i("products.messageToClient"),className:"col-span-3",rows:4})]}),e.jsxs("div",{className:"grid grid-cols-4 items-start gap-4",children:[e.jsx("div",{className:"text-right pt-2",children:e.jsx(F,{children:i("products.includeOptions")})}),e.jsxs("div",{className:"col-span-3 space-y-2",children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(B,{id:"include-images",checked:v,onCheckedChange:m=>S(!!m)}),e.jsx(F,{htmlFor:"include-images",children:i("products.includeImages")})]}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(B,{id:"include-prices",checked:$,onCheckedChange:m=>E(!!m)}),e.jsx(F,{htmlFor:"include-prices",children:i("products.includePrices")})]}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(B,{id:"include-description",checked:d,onCheckedChange:m=>M(!!m)}),e.jsx(F,{htmlFor:"include-description",children:i("products.includeDescriptions")})]})]})]}),e.jsxs("div",{className:"grid grid-cols-4 items-start gap-4",children:[e.jsx("div",{className:"text-right pt-2",children:e.jsx(F,{children:i("products.preview")})}),e.jsx("div",{className:"col-span-3",children:e.jsx("div",{className:"bg-muted/30 p-3 rounded-md text-sm font-mono h-32 overflow-y-auto whitespace-pre-wrap",children:u()})})]})]}),e.jsxs(Y,{className:"flex justify-between sm:justify-between",children:[e.jsxs(n,{variant:"outline",className:"gap-2",onClick:A,children:[y?e.jsx(as,{className:"h-4 w-4"}):e.jsx(oe,{className:"h-4 w-4"}),i(y?"products.copied":"products.copyContent")]}),e.jsxs("div",{className:"flex gap-2",children:[e.jsx(n,{variant:"outline",onClick:g,children:i("actions.cancel")}),e.jsxs(n,{className:"gap-2",onClick:p,children:[e.jsx(cs,{className:"h-4 w-4"}),i("products.sendEmail")]})]})]})]})})}function $s(){const{t:s}=_(),g=de(),[f,i]=h.useState("grid"),[b,r]=h.useState(!1),[o,C]=h.useState(null),[l,N]=h.useState(!1),[v,S]=h.useState(!1),[$,E]=h.useState(null),[d,M]=h.useState({}),[y,T]=h.useState([]),u=je("tyres"),a=je("brakes"),p=h.useMemo(()=>[...u.products,...a.products],[u.products,a.products]),A=u.isLoading||a.isLoading,m=h.useMemo(()=>{let t=p;if(d.search){const c=d.search.toLowerCase();t=t.filter(x=>x.name.toLowerCase().includes(c)||x.sku.toLowerCase().includes(c)||x.manufacturer?.toLowerCase().includes(c))}if(d.category&&d.category!=="all"&&(t=t.filter(c=>c.category===d.category)),d.status&&d.status!=="all"&&(t=t.filter(c=>c.status===d.status)),d.manufacturer&&d.manufacturer!=="all"&&(t=t.filter(c=>c.manufacturer===d.manufacturer)),d.priceRange){const[c,x]=d.priceRange;t=t.filter(z=>{const k=z.retailPrice||0;return k>=c&&k<=x})}return d.inStock!==void 0&&(t=t.filter(c=>d.inStock?c.stockQuantity>0:!0)),t},[p,d]),I=h.useMemo(()=>{const t=new Set;return p.forEach(c=>{c.manufacturer&&t.add(c.manufacturer)}),Array.from(t).sort()},[p]),ee=h.useMemo(()=>{let t=0;return p.forEach(c=>{const x=c.retailPrice||0;x>t&&(t=x)}),Math.ceil(t/1e3)*1e3},[p]),ke=()=>{u.refetch(),a.refetch()},Ce=t=>{M(c=>({...c,...t}))},Se=()=>{M({})},Pe=t=>{T(c=>c.includes(t)?c.filter(x=>x!==t):[...c,t])},De=()=>{T([])},Ee=t=>{E(t),S(!0)},Te=async t=>{try{t.category==="tyres"?await u.updateProduct(t.id,t):t.category==="brakes"&&await a.updateProduct(t.id,t),S(!1),E(null),w.success(s("products.updateSuccess"))}catch(c){console.error("Error updating product:",c),w.error(s("products.updateError"))}},ze=async t=>{try{const c=p.find(x=>x.id===t);if(!c)return;c.category==="tyres"?await u.deleteProduct(t):c.category==="brakes"&&await a.deleteProduct(t),T(x=>x.filter(z=>z!==t)),w.success(s("products.deleteSuccess"))}catch(c){console.error("Error deleting product:",c),w.error(s("products.deleteError"))}},Ie=async t=>{try{const c=t.filter(z=>p.find(P=>P.id===z)?.category==="tyres"),x=t.filter(z=>p.find(P=>P.id===z)?.category==="brakes");c.length>0&&await u.deleteProducts(c),x.length>0&&await a.deleteProducts(x),T([]),w.success(s("products.bulkDeleteSuccess",{count:t.length}))}catch(c){console.error("Error deleting products:",c),w.error(s("products.bulkDeleteError"))}},Oe=async(t,c)=>{try{const x=t.filter(k=>p.find(j=>j.id===k)?.category==="tyres"),z=t.filter(k=>p.find(j=>j.id===k)?.category==="brakes");x.length>0&&await Promise.all(x.map(k=>{const P=p.find(se=>se.id===k),j={status:c};return c==="out_of_stock"&&(j.stockQuantity=0),u.updateProduct(k,j)})),z.length>0&&await Promise.all(z.map(k=>{const P=p.find(se=>se.id===k),j={status:c};return c==="out_of_stock"&&(j.stockQuantity=0),a.updateProduct(k,j)})),w.success(s("products.statusUpdateSuccess",{count:t.length}))}catch(x){console.error("Error updating product status:",x),w.error(s("products.statusUpdateError"))}},Fe=t=>{C(t),r(!0)},Re=()=>{o&&(ze(o),C(null)),r(!1)},$e=t=>{w.info(s("products.duplicateNotImplemented"))},Me=t=>{w.info(s("products.featureNotImplemented"))},xe=()=>{const t=y.length>0?m.filter(j=>y.includes(j.id)):m,x=[["ID","Name","SKU","Category","Price","Stock","Status","Manufacturer"].join(","),...t.map(j=>[j.id,`"${j.name.replace(/"/g,'""')}"`,j.sku,j.category,j.retailPrice||0,j.stockQuantity,j.status,j.manufacturer].join(","))].join(`
`),z=new Blob([x],{type:"text/csv;charset=utf-8;"}),k=URL.createObjectURL(z),P=document.createElement("a");P.setAttribute("href",k),P.setAttribute("download",`products-export-${new Date().toISOString().split("T")[0]}.csv`),P.style.visibility="hidden",document.body.appendChild(P),P.click(),document.body.removeChild(P),w.success(s("products.exportSuccess",{count:t.length}))},Ae=()=>{N(!0)},Le=h.useMemo(()=>m.filter(t=>y.includes(t.id)),[m,y]);return e.jsx(ns,{children:e.jsx(ds,{children:e.jsxs("div",{className:"flex flex-col gap-6",children:[e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsxs("div",{children:[e.jsx("h1",{className:"text-3xl font-bold tracking-tight",children:s("products.products")}),e.jsx("p",{className:"text-muted-foreground mt-1",children:s("products.manageProductCatalog")})]}),e.jsxs("div",{className:"flex gap-3",children:[e.jsxs("div",{className:"flex items-center bg-white rounded-md border",children:[e.jsxs(n,{variant:f==="grid"?"default":"ghost",size:"icon",onClick:()=>i("grid"),className:f==="grid"?"bg-midnight-blue text-white":"",children:[e.jsx(ge,{className:"h-4 w-4"}),e.jsx("span",{className:"sr-only",children:s("products.gridView")})]}),e.jsxs(n,{variant:f==="list"?"default":"ghost",size:"icon",onClick:()=>i("list"),className:f==="list"?"bg-midnight-blue text-white":"",children:[e.jsx(is,{className:"h-4 w-4"}),e.jsx("span",{className:"sr-only",children:s("products.listView")})]})]}),e.jsxs(ue,{children:[e.jsx(me,{asChild:!0,children:e.jsxs(n,{variant:"outline",className:"gap-2",children:[e.jsx(we,{className:"h-4 w-4"}),s("products.actions")]})}),e.jsxs(he,{align:"end",children:[e.jsxs(D,{onClick:()=>g("/app/products-table/tyres"),children:[e.jsx(ge,{className:"mr-2 h-4 w-4"}),s("products.goToDataGrid")]}),e.jsxs(D,{onClick:()=>ke(),children:[e.jsx(ls,{className:"mr-2 h-4 w-4"}),s("products.refreshProducts")]}),e.jsx(Ne,{}),e.jsxs(D,{onClick:xe,children:[e.jsx(be,{className:"mr-2 h-4 w-4"}),s("products.exportProducts")]}),e.jsxs(D,{onClick:()=>w.info(s("products.importNotImplemented")),children:[e.jsx(ms,{className:"mr-2 h-4 w-4"}),s("products.importProducts")]})]})]})]})]}),e.jsx(ps,{filters:d,onFilterChange:Ce,onResetFilters:Se,manufacturers:I,maxPrice:ee}),e.jsx(js,{products:m,isLoading:A,viewMode:f,selectedProductIds:y,onSelectProduct:Pe,onEditProduct:Ee,onDeleteProduct:Fe,onDuplicateProduct:$e,onFeatureProduct:Me}),e.jsx(J,{open:b,onOpenChange:r,children:e.jsxs(K,{children:[e.jsxs(W,{children:[e.jsx(X,{children:s("products.confirmDelete")}),e.jsx(Z,{children:s("products.deleteConfirmation")})]}),e.jsxs(Y,{children:[e.jsx(n,{variant:"outline",onClick:()=>r(!1),children:s("actions.cancel")}),e.jsx(n,{variant:"destructive",onClick:Re,children:s("products.delete")})]})]})}),e.jsx(vs,{isOpen:l,onClose:()=>N(!1),products:Le}),v&&$&&e.jsx(us,{isOpen:v,onClose:()=>{S(!1),E(null)},onSave:Te,product:$,categories:[{id:"tyres",name:s("categories.tyres")},{id:"brakes",name:s("categories.brakes")}]}),y.length>0&&e.jsx(fs,{selectedCount:y.length,onClearSelection:De,onDelete:()=>Ie(y),onUpdateStatus:t=>Oe(y,t),onExport:xe,onShareByEmail:Ae})]})})})}export{$s as default};
