import{u as oe,ag as de,a as me,r as a,j as e,B as r,a6 as T,P as C,N as _,C as k,k as L,Q as M,aW as he,l as U,aw as pe,I as ge,m as ue,K as b,aq as xe,a3 as je,D as fe,d as ve,e as Ne,f as Se,g as ye,h as we,w as Ce,U as V}from"./index-DnIZvqWt.js";import{A as _e}from"./AdminLayout-DBeU98xA.js";import{S as G,a as B,b as z,c as $,d as n}from"./select-DN5XplGq.js";import{S as Z}from"./skeleton-iTtVbUqk.js";import{g as be,a as Pe}from"./supplierOrderService-DOVoj0B_.js";import{g as De,a as Ae}from"./supplierShippingService-BN0sW-sh.js";import{S as j}from"./StatsCard-CllgX-O9.js";import{D as H}from"./dollar-sign-Ds7_eYaM.js";import{T as Ee}from"./trending-up-DOWsPmVN.js";import{P as Oe}from"./phone-CUsbluw7.js";import"./house-9sLOXRjO.js";import"./settings-CRvmRnrZ.js";import"./trending-down-CtxiHcR2.js";function Ze(){oe();const Y=de(),{toast:d}=me(),[P,q]=a.useState([]),[l,K]=a.useState(null),[D,A]=a.useState(!0),[E,O]=a.useState(!1),[p,Q]=a.useState(""),[m,W]=a.useState("all"),[i,f]=a.useState(1),[R,J]=a.useState(0),[X,ee]=a.useState([]),[se,g]=a.useState(!1),[v,I]=a.useState(""),[h,N]=a.useState(""),[S,F]=a.useState(!1),y=10,ae=async()=>{try{const s=await De();s.success&&ee(s.companies||[])}catch(s){console.error("❌ [SUPPLIER_ORDERS_PAGE] Error loading shipping companies:",s)}},te=async()=>{if(!(!v||!h))try{F(!0),console.log("🚚 [SUPPLIER_ORDERS_PAGE] Assigning order to shipping company...",{orderId:v,shippingCompanyId:h});const s=await Ae(v,h);s.success?(d({title:"Order Assigned Successfully",description:"Order has been assigned to shipping company and will appear in their dashboard."}),u(!0),g(!1),I(""),N("")):d({title:"Assignment Failed",description:s.error||"Failed to assign order to shipping company",variant:"destructive"})}catch(s){console.error("❌ [SUPPLIER_ORDERS_PAGE] Error assigning shipping:",s),d({title:"Assignment Failed",description:"Failed to assign order to shipping company",variant:"destructive"})}finally{F(!1)}},ie=s=>{I(s),N(""),g(!0)},u=async(s=!1)=>{try{s?O(!0):A(!0),console.log("📊 [SUPPLIER_ORDERS_PAGE] Loading orders and stats...");const t=await be();t.success&&K(t.stats||null);const w={status:m==="all"?void 0:m,search:p||void 0,limit:y,offset:(i-1)*y},o=await Pe(w);o.success?(q(o.orders||[]),J(o.total_count||0)):d({title:"Failed to load orders",description:o.error||"Please try again",variant:"destructive"})}catch(t){console.error("❌ [SUPPLIER_ORDERS_PAGE] Error loading data:",t),d({title:"Failed to load data",description:"Please try again",variant:"destructive"})}finally{A(!1),O(!1)}};a.useEffect(()=>{u(),ae()},[i,m]),a.useEffect(()=>{const s=setTimeout(()=>{i===1?u():f(1)},500);return()=>clearTimeout(s)},[p]);const x=s=>new Intl.NumberFormat("fr-DZ",{style:"currency",currency:"DZD",minimumFractionDigits:0,maximumFractionDigits:0}).format(s),re=s=>new Date(s).toLocaleDateString("fr-DZ",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"}),ne=s=>{switch(s){case"pending":return"secondary";case"confirmed":return"default";case"shipped":return"outline";case"delivered":return"default";case"cancelled":return"destructive";default:return"secondary"}},le=s=>{switch(s){case"pending":return _;case"confirmed":return V;case"shipped":return b;case"delivered":return V;case"cancelled":return Ce;default:return _}},c=Math.ceil(R/y);return e.jsx(_e,{children:e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("h1",{className:"text-3xl font-bold text-gray-900",children:"Order Management"}),e.jsx("p",{className:"text-gray-600 mt-1",children:"Manage orders containing your products and coordinate with shipping companies"})]}),e.jsxs(r,{onClick:()=>u(!0),disabled:E,variant:"outline",className:"flex items-center gap-2",children:[e.jsx(T,{className:`h-4 w-4 ${E?"animate-spin":""}`}),"Refresh"]})]}),D?e.jsx("div",{className:"grid gap-4 md:grid-cols-2 lg:grid-cols-4",children:[...Array(4)].map((s,t)=>e.jsx(Z,{className:"h-32"},t))}):l?e.jsxs("div",{className:"grid gap-4 md:grid-cols-2 lg:grid-cols-4",children:[e.jsx(j,{title:"Total Orders",value:l.total_orders,icon:e.jsx(C,{className:"h-6 w-6"}),description:"All time orders"}),e.jsx(j,{title:"Pending Orders",value:l.pending_orders,icon:e.jsx(_,{className:"h-6 w-6"}),description:"Awaiting confirmation"}),e.jsx(j,{title:"This Month Revenue",value:x(l.this_month_revenue),icon:e.jsx(H,{className:"h-6 w-6"}),description:`${l.this_month_orders} orders`}),e.jsx(j,{title:"Average Order Value",value:x(l.average_order_value),icon:e.jsx(Ee,{className:"h-6 w-6"}),description:"Per order average"})]}):null,e.jsxs(k,{children:[e.jsx(L,{children:e.jsxs(M,{className:"flex items-center gap-2",children:[e.jsx(he,{className:"h-5 w-5"}),"Filters & Search"]})}),e.jsx(U,{children:e.jsxs("div",{className:"flex flex-col md:flex-row gap-4",children:[e.jsx("div",{className:"flex-1",children:e.jsxs("div",{className:"relative",children:[e.jsx(pe,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),e.jsx(ge,{placeholder:"Search by order number, customer name, or phone...",value:p,onChange:s=>Q(s.target.value),className:"pl-10"})]})}),e.jsx("div",{className:"w-full md:w-48",children:e.jsxs(G,{value:m,onValueChange:W,children:[e.jsx(B,{children:e.jsx(z,{placeholder:"Filter by status"})}),e.jsxs($,{children:[e.jsx(n,{value:"all",children:"All Status"}),e.jsx(n,{value:"pending",children:"Pending"}),e.jsx(n,{value:"confirmed",children:"Confirmed"}),e.jsx(n,{value:"shipped",children:"Shipped"}),e.jsx(n,{value:"delivered",children:"Delivered"}),e.jsx(n,{value:"cancelled",children:"Cancelled"})]})]})})]})})]}),e.jsxs(k,{children:[e.jsx(L,{children:e.jsxs(M,{className:"flex items-center justify-between",children:[e.jsxs("span",{className:"flex items-center gap-2",children:[e.jsx(C,{className:"h-5 w-5"}),"Orders (",R,")"]}),c>1&&e.jsxs("span",{className:"text-sm text-gray-500",children:["Page ",i," of ",c]})]})}),e.jsxs(U,{children:[D?e.jsx("div",{className:"space-y-4",children:[...Array(5)].map((s,t)=>e.jsx(Z,{className:"h-24"},t))}):P.length===0?e.jsxs("div",{className:"text-center py-12",children:[e.jsx(C,{className:"h-16 w-16 text-gray-300 mx-auto mb-4"}),e.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No orders found"}),e.jsx("p",{className:"text-gray-600",children:p||m!=="all"?"Try adjusting your filters or search terms":"Orders containing your products will appear here"})]}):e.jsx("div",{className:"space-y-4",children:P.map(s=>{const t=le(s.status),w=s.supplier_items.reduce((o,ce)=>o+ce.total_price,0);return e.jsxs("div",{className:"border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow",children:[e.jsxs("div",{className:"flex items-start justify-between mb-4",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx(t,{className:"h-6 w-6 text-[#fa7b00]"}),e.jsxs("div",{children:[e.jsx("h3",{className:"font-semibold text-lg",children:s.order_number}),e.jsx("p",{className:"text-sm text-gray-600",children:re(s.created_at)})]})]}),e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx(ue,{variant:ne(s.status),children:s.status.charAt(0).toUpperCase()+s.status.slice(1)}),s.status==="confirmed"&&e.jsxs(r,{onClick:()=>ie(s.id),size:"sm",variant:"default",className:"flex items-center gap-2 bg-[#fa7b00] hover:bg-[#e56b00]",children:[e.jsx(b,{className:"h-4 w-4"}),"Assign Shipping"]}),e.jsxs(r,{onClick:()=>Y(`/app/orders/${s.id}`),size:"sm",variant:"outline",className:"flex items-center gap-2",children:[e.jsx(xe,{className:"h-4 w-4"}),"View Details"]})]})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 mb-4",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(Oe,{className:"h-4 w-4 text-gray-400"}),e.jsxs("span",{className:"text-sm",children:[s.consumer_name||"Consumer"," - ",s.consumer_phone]})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(je,{className:"h-4 w-4 text-gray-400"}),e.jsx("span",{className:"text-sm",children:s.delivery_wilaya})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(H,{className:"h-4 w-4 text-gray-400"}),e.jsxs("span",{className:"text-sm font-medium",children:["Your items: ",x(w)]})]})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsxs("span",{className:"text-sm text-gray-600",children:["Your products: ",s.supplier_items.length]}),s.other_suppliers.length>0&&e.jsxs("span",{className:"text-sm text-gray-600",children:["Other suppliers: ",s.other_suppliers.length]})]}),e.jsxs("div",{className:"text-sm text-gray-600",children:["Total order: ",x(s.total_amount)]})]})]},s.id)})}),c>1&&e.jsxs("div",{className:"flex items-center justify-center gap-2 mt-6",children:[e.jsx(r,{onClick:()=>f(s=>Math.max(1,s-1)),disabled:i===1,variant:"outline",size:"sm",children:"Previous"}),e.jsxs("span",{className:"text-sm text-gray-600",children:["Page ",i," of ",c]}),e.jsx(r,{onClick:()=>f(s=>Math.min(c,s+1)),disabled:i===c,variant:"outline",size:"sm",children:"Next"})]})]})]}),e.jsx(fe,{open:se,onOpenChange:g,children:e.jsxs(ve,{className:"sm:max-w-[425px]",children:[e.jsxs(Ne,{children:[e.jsx(Se,{children:"Assign Order to Shipping Company"}),e.jsx(ye,{children:"Select a shipping company to handle the delivery of this order."})]}),e.jsx("div",{className:"grid gap-4 py-4",children:e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{htmlFor:"shipping-company",className:"text-sm font-medium",children:"Shipping Company"}),e.jsxs(G,{value:h,onValueChange:N,children:[e.jsx(B,{children:e.jsx(z,{placeholder:"Select a shipping company"})}),e.jsx($,{children:X.map(s=>e.jsx(n,{value:s.id,children:e.jsxs("div",{className:"flex flex-col",children:[e.jsx("span",{className:"font-medium",children:s.company_name}),e.jsxs("span",{className:"text-xs text-gray-500",children:["Coverage: ",s.coverage_areas.slice(0,3).join(", "),s.coverage_areas.length>3&&` +${s.coverage_areas.length-3} more`]})]})},s.id))})]})]})}),e.jsxs(we,{children:[e.jsx(r,{variant:"outline",onClick:()=>g(!1),disabled:S,children:"Cancel"}),e.jsx(r,{onClick:te,disabled:!h||S,className:"bg-[#fa7b00] hover:bg-[#e56b00]",children:S?e.jsxs(e.Fragment,{children:[e.jsx(T,{className:"h-4 w-4 mr-2 animate-spin"}),"Assigning..."]}):e.jsxs(e.Fragment,{children:[e.jsx(b,{className:"h-4 w-4 mr-2"}),"Assign Order"]})})]})]})})]})})}export{Ze as default};
