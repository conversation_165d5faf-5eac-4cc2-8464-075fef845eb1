import{c as Qe,j as E,aZ as A,cq as Rt,N as Pt,r,cP as kt,bM as Je,cQ as Dt,cR as Ot,cS as It,cT as At,cU as z,cV as Tt,cW as Mt,cX as _t,cY as Lt,cZ as Ft,c_ as jt,bN as Ut,aw as Bt,ce as Kt,cf as Vt,B as Wt,cg as Ht,b$ as Xt}from"./index-DRgoPWv8.js";/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Yt=Qe("Bike",[["circle",{cx:"18.5",cy:"17.5",r:"3.5",key:"15x4ox"}],["circle",{cx:"5.5",cy:"17.5",r:"3.5",key:"1noe27"}],["circle",{cx:"15",cy:"5",r:"1",key:"19l28e"}],["path",{d:"M12 17.5V14l-3-3 4-3 2 3h2",key:"1npguv"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Gt=Qe("ChevronsUpDown",[["path",{d:"m7 15 5 5 5-5",key:"1hf1tw"}],["path",{d:"m7 9 5-5 5 5",key:"sgt6xg"}]]),wr=({selectedType:e,onTypeChange:n})=>E.jsxs("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[E.jsxs("button",{type:"button",className:A("flex flex-col items-center justify-center h-auto py-6 px-8 flex-1 max-w-[200px]","rounded-2xl border-2 transition-all duration-200","hover:shadow-md focus:outline-none focus:ring-2 focus:ring-primary/20",e==="car"?"border-primary bg-primary/5 text-primary":"border-gray-200 bg-white text-gray-700 hover:border-gray-300"),onClick:()=>n("car"),children:[E.jsx("div",{className:A("flex items-center justify-center w-12 h-12 rounded-full mb-3",e==="car"?"bg-primary/10":"bg-gray-100"),children:E.jsx(Rt,{className:A("h-6 w-6",e==="car"?"text-primary":"text-gray-600")})}),E.jsx("span",{className:"font-medium",children:"Cars"})]}),E.jsxs("button",{type:"button",className:A("flex flex-col items-center justify-center h-auto py-6 px-8 flex-1 max-w-[200px]","rounded-2xl border-2 transition-all duration-200","hover:shadow-md focus:outline-none focus:ring-2 focus:ring-primary/20",e==="motorcycle"?"border-primary bg-primary/5 text-primary":"border-gray-200 bg-white text-gray-700 hover:border-gray-300"),onClick:()=>n("motorcycle"),children:[E.jsx("div",{className:A("flex items-center justify-center w-12 h-12 rounded-full mb-3",e==="motorcycle"?"bg-primary/10":"bg-gray-100"),children:E.jsx(Yt,{className:A("h-6 w-6",e==="motorcycle"?"text-primary":"text-gray-600")})}),E.jsx("span",{className:"font-medium",children:"Motorcycles"})]}),E.jsxs("button",{type:"button",className:A("flex flex-col items-center justify-center h-auto py-6 px-8 flex-1 max-w-[200px]","rounded-2xl border-2 transition-all duration-200","hover:shadow-md focus:outline-none focus:ring-2 focus:ring-primary/20",e==="truck"?"border-primary bg-primary/5 text-primary":"border-gray-200 bg-white text-gray-700 hover:border-gray-300"),onClick:()=>n("truck"),children:[E.jsx("div",{className:A("flex items-center justify-center w-12 h-12 rounded-full mb-3",e==="truck"?"bg-primary/10":"bg-gray-100"),children:E.jsx(Pt,{className:A("h-6 w-6",e==="truck"?"text-primary":"text-gray-600")})}),E.jsx("span",{className:"font-medium",children:"Trucks"})]})]});var je=1,qt=.9,zt=.8,Zt=.17,he=.1,be=.999,Qt=.9999,Jt=.99,en=/[\\\/_+.#"@\[\(\{&]/,tn=/[\\\/_+.#"@\[\(\{&]/g,nn=/[\s-]/,et=/[\s-]/g;function Ce(e,n,t,o,a,c,s){if(c===n.length)return a===e.length?je:Jt;var l=`${a},${c}`;if(s[l]!==void 0)return s[l];for(var d=o.charAt(c),u=t.indexOf(d,a),f=0,v,x,p,N;u>=0;)v=Ce(e,n,t,o,u+1,c+1,s),v>f&&(u===a?v*=je:en.test(e.charAt(u-1))?(v*=zt,p=e.slice(a,u-1).match(tn),p&&a>0&&(v*=Math.pow(be,p.length))):nn.test(e.charAt(u-1))?(v*=qt,N=e.slice(a,u-1).match(et),N&&a>0&&(v*=Math.pow(be,N.length))):(v*=Zt,a>0&&(v*=Math.pow(be,u-a))),e.charAt(u)!==n.charAt(c)&&(v*=Qt)),(v<he&&t.charAt(u-1)===o.charAt(c+1)||o.charAt(c+1)===o.charAt(c)&&t.charAt(u-1)!==o.charAt(c))&&(x=Ce(e,n,t,o,u+1,c+2,s),x*he>v&&(v=x*he)),v>f&&(f=v),u=t.indexOf(d,u+1);return s[l]=f,f}function Ue(e){return e.toLowerCase().replace(et," ")}function rn(e,n,t){return e=t&&t.length>0?`${e+" "+t.join(" ")}`:e,Ce(e,n,Ue(e),Ue(n),0,0,{})}function T(){return T=Object.assign?Object.assign.bind():function(e){for(var n=1;n<arguments.length;n++){var t=arguments[n];for(var o in t)({}).hasOwnProperty.call(t,o)&&(e[o]=t[o])}return e},T.apply(null,arguments)}function Q(e,n,{checkForDefaultPrevented:t=!0}={}){return function(a){if(e?.(a),t===!1||!a.defaultPrevented)return n?.(a)}}function on(e,n){typeof e=="function"?e(n):e!=null&&(e.current=n)}function tt(...e){return n=>e.forEach(t=>on(t,n))}function oe(...e){return r.useCallback(tt(...e),e)}function an(e,n=[]){let t=[];function o(c,s){const l=r.createContext(s),d=t.length;t=[...t,s];function u(v){const{scope:x,children:p,...N}=v,m=x?.[e][d]||l,b=r.useMemo(()=>N,Object.values(N));return r.createElement(m.Provider,{value:b},p)}function f(v,x){const p=x?.[e][d]||l,N=r.useContext(p);if(N)return N;if(s!==void 0)return s;throw new Error(`\`${v}\` must be used within \`${c}\``)}return u.displayName=c+"Provider",[u,f]}const a=()=>{const c=t.map(s=>r.createContext(s));return function(l){const d=l?.[e]||c;return r.useMemo(()=>({[`__scope${e}`]:{...l,[e]:d}}),[l,d])}};return a.scopeName=e,[o,cn(a,...n)]}function cn(...e){const n=e[0];if(e.length===1)return n;const t=()=>{const o=e.map(a=>({useScope:a(),scopeName:a.scopeName}));return function(c){const s=o.reduce((l,{useScope:d,scopeName:u})=>{const v=d(c)[`__scope${u}`];return{...l,...v}},{});return r.useMemo(()=>({[`__scope${n.scopeName}`]:s}),[s])}};return t.scopeName=n.scopeName,t}const Se=globalThis?.document?r.useLayoutEffect:()=>{},ln=kt.useId||(()=>{});let sn=0;function ge(e){const[n,t]=r.useState(ln());return Se(()=>{e||t(o=>o??String(sn++))},[e]),e||(n?`radix-${n}`:"")}function V(e){const n=r.useRef(e);return r.useEffect(()=>{n.current=e}),r.useMemo(()=>(...t)=>{var o;return(o=n.current)===null||o===void 0?void 0:o.call(n,...t)},[])}function un({prop:e,defaultProp:n,onChange:t=()=>{}}){const[o,a]=dn({defaultProp:n,onChange:t}),c=e!==void 0,s=c?e:o,l=V(t),d=r.useCallback(u=>{if(c){const v=typeof u=="function"?u(e):u;v!==e&&l(v)}else a(u)},[c,e,a,l]);return[s,d]}function dn({defaultProp:e,onChange:n}){const t=r.useState(e),[o]=t,a=r.useRef(o),c=V(n);return r.useEffect(()=>{a.current!==o&&(c(o),a.current=o)},[o,a,c]),t}const Oe=r.forwardRef((e,n)=>{const{children:t,...o}=e,a=r.Children.toArray(t),c=a.find(mn);if(c){const s=c.props.children,l=a.map(d=>d===c?r.Children.count(s)>1?r.Children.only(null):r.isValidElement(s)?s.props.children:null:d);return r.createElement(Ne,T({},o,{ref:n}),r.isValidElement(s)?r.cloneElement(s,void 0,l):null)}return r.createElement(Ne,T({},o,{ref:n}),t)});Oe.displayName="Slot";const Ne=r.forwardRef((e,n)=>{const{children:t,...o}=e;return r.isValidElement(t)?r.cloneElement(t,{...vn(o,t.props),ref:n?tt(n,t.ref):t.ref}):r.Children.count(t)>1?r.Children.only(null):null});Ne.displayName="SlotClone";const fn=({children:e})=>r.createElement(r.Fragment,null,e);function mn(e){return r.isValidElement(e)&&e.type===fn}function vn(e,n){const t={...n};for(const o in n){const a=e[o],c=n[o];/^on[A-Z]/.test(o)?a&&c?t[o]=(...l)=>{c(...l),a(...l)}:a&&(t[o]=a):o==="style"?t[o]={...a,...c}:o==="className"&&(t[o]=[a,c].filter(Boolean).join(" "))}return{...e,...t}}const pn=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"],L=pn.reduce((e,n)=>{const t=r.forwardRef((o,a)=>{const{asChild:c,...s}=o,l=c?Oe:n;return r.useEffect(()=>{window[Symbol.for("radix-ui")]=!0},[]),r.createElement(l,T({},s,{ref:a}))});return t.displayName=`Primitive.${n}`,{...e,[n]:t}},{});function hn(e,n){e&&Je.flushSync(()=>e.dispatchEvent(n))}function bn(e,n=globalThis?.document){const t=V(e);r.useEffect(()=>{const o=a=>{a.key==="Escape"&&t(a)};return n.addEventListener("keydown",o),()=>n.removeEventListener("keydown",o)},[t,n])}const Re="dismissableLayer.update",gn="dismissableLayer.pointerDownOutside",$n="dismissableLayer.focusOutside";let Be;const yn=r.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),En=r.forwardRef((e,n)=>{var t;const{disableOutsidePointerEvents:o=!1,onEscapeKeyDown:a,onPointerDownOutside:c,onFocusOutside:s,onInteractOutside:l,onDismiss:d,...u}=e,f=r.useContext(yn),[v,x]=r.useState(null),p=(t=v?.ownerDocument)!==null&&t!==void 0?t:globalThis?.document,[,N]=r.useState({}),m=oe(n,P=>x(P)),b=Array.from(f.layers),[w]=[...f.layersWithOutsidePointerEventsDisabled].slice(-1),$=b.indexOf(w),S=v?b.indexOf(v):-1,y=f.layersWithOutsidePointerEventsDisabled.size>0,R=S>=$,D=xn(P=>{const F=P.target,Y=[...f.branches].some(U=>U.contains(F));!R||Y||(c?.(P),l?.(P),P.defaultPrevented||d?.())},p),O=wn(P=>{const F=P.target;[...f.branches].some(U=>U.contains(F))||(s?.(P),l?.(P),P.defaultPrevented||d?.())},p);return bn(P=>{S===f.layers.size-1&&(a?.(P),!P.defaultPrevented&&d&&(P.preventDefault(),d()))},p),r.useEffect(()=>{if(v)return o&&(f.layersWithOutsidePointerEventsDisabled.size===0&&(Be=p.body.style.pointerEvents,p.body.style.pointerEvents="none"),f.layersWithOutsidePointerEventsDisabled.add(v)),f.layers.add(v),Ke(),()=>{o&&f.layersWithOutsidePointerEventsDisabled.size===1&&(p.body.style.pointerEvents=Be)}},[v,p,o,f]),r.useEffect(()=>()=>{v&&(f.layers.delete(v),f.layersWithOutsidePointerEventsDisabled.delete(v),Ke())},[v,f]),r.useEffect(()=>{const P=()=>N({});return document.addEventListener(Re,P),()=>document.removeEventListener(Re,P)},[]),r.createElement(L.div,T({},u,{ref:m,style:{pointerEvents:y?R?"auto":"none":void 0,...e.style},onFocusCapture:Q(e.onFocusCapture,O.onFocusCapture),onBlurCapture:Q(e.onBlurCapture,O.onBlurCapture),onPointerDownCapture:Q(e.onPointerDownCapture,D.onPointerDownCapture)}))});function xn(e,n=globalThis?.document){const t=V(e),o=r.useRef(!1),a=r.useRef(()=>{});return r.useEffect(()=>{const c=l=>{if(l.target&&!o.current){let d=function(){nt(gn,t,u,{discrete:!0})};const u={originalEvent:l};l.pointerType==="touch"?(n.removeEventListener("click",a.current),a.current=d,n.addEventListener("click",a.current,{once:!0})):d()}else n.removeEventListener("click",a.current);o.current=!1},s=window.setTimeout(()=>{n.addEventListener("pointerdown",c)},0);return()=>{window.clearTimeout(s),n.removeEventListener("pointerdown",c),n.removeEventListener("click",a.current)}},[n,t]),{onPointerDownCapture:()=>o.current=!0}}function wn(e,n=globalThis?.document){const t=V(e),o=r.useRef(!1);return r.useEffect(()=>{const a=c=>{c.target&&!o.current&&nt($n,t,{originalEvent:c},{discrete:!1})};return n.addEventListener("focusin",a),()=>n.removeEventListener("focusin",a)},[n,t]),{onFocusCapture:()=>o.current=!0,onBlurCapture:()=>o.current=!1}}function Ke(){const e=new CustomEvent(Re);document.dispatchEvent(e)}function nt(e,n,t,{discrete:o}){const a=t.originalEvent.target,c=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:t});n&&a.addEventListener(e,n,{once:!0}),o?hn(a,c):a.dispatchEvent(c)}const $e="focusScope.autoFocusOnMount",ye="focusScope.autoFocusOnUnmount",Ve={bubbles:!1,cancelable:!0},Cn=r.forwardRef((e,n)=>{const{loop:t=!1,trapped:o=!1,onMountAutoFocus:a,onUnmountAutoFocus:c,...s}=e,[l,d]=r.useState(null),u=V(a),f=V(c),v=r.useRef(null),x=oe(n,m=>d(m)),p=r.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;r.useEffect(()=>{if(o){let m=function(S){if(p.paused||!l)return;const y=S.target;l.contains(y)?v.current=y:j(v.current,{select:!0})},b=function(S){if(p.paused||!l)return;const y=S.relatedTarget;y!==null&&(l.contains(y)||j(v.current,{select:!0}))},w=function(S){if(document.activeElement===document.body)for(const R of S)R.removedNodes.length>0&&j(l)};document.addEventListener("focusin",m),document.addEventListener("focusout",b);const $=new MutationObserver(w);return l&&$.observe(l,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",m),document.removeEventListener("focusout",b),$.disconnect()}}},[o,l,p.paused]),r.useEffect(()=>{if(l){He.add(p);const m=document.activeElement;if(!l.contains(m)){const w=new CustomEvent($e,Ve);l.addEventListener($e,u),l.dispatchEvent(w),w.defaultPrevented||(Sn(Dn(rt(l)),{select:!0}),document.activeElement===m&&j(l))}return()=>{l.removeEventListener($e,u),setTimeout(()=>{const w=new CustomEvent(ye,Ve);l.addEventListener(ye,f),l.dispatchEvent(w),w.defaultPrevented||j(m??document.body,{select:!0}),l.removeEventListener(ye,f),He.remove(p)},0)}}},[l,u,f,p]);const N=r.useCallback(m=>{if(!t&&!o||p.paused)return;const b=m.key==="Tab"&&!m.altKey&&!m.ctrlKey&&!m.metaKey,w=document.activeElement;if(b&&w){const $=m.currentTarget,[S,y]=Nn($);S&&y?!m.shiftKey&&w===y?(m.preventDefault(),t&&j(S,{select:!0})):m.shiftKey&&w===S&&(m.preventDefault(),t&&j(y,{select:!0})):w===$&&m.preventDefault()}},[t,o,p.paused]);return r.createElement(L.div,T({tabIndex:-1},s,{ref:x,onKeyDown:N}))});function Sn(e,{select:n=!1}={}){const t=document.activeElement;for(const o of e)if(j(o,{select:n}),document.activeElement!==t)return}function Nn(e){const n=rt(e),t=We(n,e),o=We(n.reverse(),e);return[t,o]}function rt(e){const n=[],t=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:o=>{const a=o.tagName==="INPUT"&&o.type==="hidden";return o.disabled||o.hidden||a?NodeFilter.FILTER_SKIP:o.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;t.nextNode();)n.push(t.currentNode);return n}function We(e,n){for(const t of e)if(!Rn(t,{upTo:n}))return t}function Rn(e,{upTo:n}){if(getComputedStyle(e).visibility==="hidden")return!0;for(;e;){if(n!==void 0&&e===n)return!1;if(getComputedStyle(e).display==="none")return!0;e=e.parentElement}return!1}function Pn(e){return e instanceof HTMLInputElement&&"select"in e}function j(e,{select:n=!1}={}){if(e&&e.focus){const t=document.activeElement;e.focus({preventScroll:!0}),e!==t&&Pn(e)&&n&&e.select()}}const He=kn();function kn(){let e=[];return{add(n){const t=e[0];n!==t&&t?.pause(),e=Xe(e,n),e.unshift(n)},remove(n){var t;e=Xe(e,n),(t=e[0])===null||t===void 0||t.resume()}}}function Xe(e,n){const t=[...e],o=t.indexOf(n);return o!==-1&&t.splice(o,1),t}function Dn(e){return e.filter(n=>n.tagName!=="A")}const On=r.forwardRef((e,n)=>{var t;const{container:o=globalThis==null||(t=globalThis.document)===null||t===void 0?void 0:t.body,...a}=e;return o?Dt.createPortal(r.createElement(L.div,T({},a,{ref:n})),o):null});function In(e,n){return r.useReducer((t,o)=>{const a=n[t][o];return a??t},e)}const ue=e=>{const{present:n,children:t}=e,o=An(n),a=typeof t=="function"?t({present:o.isPresent}):r.Children.only(t),c=oe(o.ref,a.ref);return typeof t=="function"||o.isPresent?r.cloneElement(a,{ref:c}):null};ue.displayName="Presence";function An(e){const[n,t]=r.useState(),o=r.useRef({}),a=r.useRef(e),c=r.useRef("none"),s=e?"mounted":"unmounted",[l,d]=In(s,{mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}});return r.useEffect(()=>{const u=le(o.current);c.current=l==="mounted"?u:"none"},[l]),Se(()=>{const u=o.current,f=a.current;if(f!==e){const x=c.current,p=le(u);e?d("MOUNT"):p==="none"||u?.display==="none"?d("UNMOUNT"):d(f&&x!==p?"ANIMATION_OUT":"UNMOUNT"),a.current=e}},[e,d]),Se(()=>{if(n){const u=v=>{const p=le(o.current).includes(v.animationName);v.target===n&&p&&Je.flushSync(()=>d("ANIMATION_END"))},f=v=>{v.target===n&&(c.current=le(o.current))};return n.addEventListener("animationstart",f),n.addEventListener("animationcancel",u),n.addEventListener("animationend",u),()=>{n.removeEventListener("animationstart",f),n.removeEventListener("animationcancel",u),n.removeEventListener("animationend",u)}}else d("ANIMATION_END")},[n,d]),{isPresent:["mounted","unmountSuspended"].includes(l),ref:r.useCallback(u=>{u&&(o.current=getComputedStyle(u)),t(u)},[])}}function le(e){return e?.animationName||"none"}let Ee=0;function Tn(){r.useEffect(()=>{var e,n;const t=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",(e=t[0])!==null&&e!==void 0?e:Ye()),document.body.insertAdjacentElement("beforeend",(n=t[1])!==null&&n!==void 0?n:Ye()),Ee++,()=>{Ee===1&&document.querySelectorAll("[data-radix-focus-guard]").forEach(o=>o.remove()),Ee--}},[])}function Ye(){const e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.cssText="outline: none; opacity: 0; position: fixed; pointer-events: none",e}var ot=Ot(),xe=function(){},de=r.forwardRef(function(e,n){var t=r.useRef(null),o=r.useState({onScrollCapture:xe,onWheelCapture:xe,onTouchMoveCapture:xe}),a=o[0],c=o[1],s=e.forwardProps,l=e.children,d=e.className,u=e.removeScrollBar,f=e.enabled,v=e.shards,x=e.sideCar,p=e.noIsolation,N=e.inert,m=e.allowPinchZoom,b=e.as,w=b===void 0?"div":b,$=It(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noIsolation","inert","allowPinchZoom","as"]),S=x,y=At([t,n]),R=z(z({},$),a);return r.createElement(r.Fragment,null,f&&r.createElement(S,{sideCar:ot,removeScrollBar:u,shards:v,noIsolation:p,inert:N,setCallbacks:c,allowPinchZoom:!!m,lockRef:t}),s?r.cloneElement(r.Children.only(l),z(z({},R),{ref:y})):r.createElement(w,z({},R,{className:d,ref:y}),l))});de.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1};de.classNames={fullWidth:Tt,zeroRight:Mt};var Pe=!1;if(typeof window<"u")try{var se=Object.defineProperty({},"passive",{get:function(){return Pe=!0,!0}});window.addEventListener("test",se,se),window.removeEventListener("test",se,se)}catch{Pe=!1}var G=Pe?{passive:!1}:!1,Mn=function(e){return e.tagName==="TEXTAREA"},at=function(e,n){var t=window.getComputedStyle(e);return t[n]!=="hidden"&&!(t.overflowY===t.overflowX&&!Mn(e)&&t[n]==="visible")},_n=function(e){return at(e,"overflowY")},Ln=function(e){return at(e,"overflowX")},Ge=function(e,n){var t=n;do{typeof ShadowRoot<"u"&&t instanceof ShadowRoot&&(t=t.host);var o=ct(e,t);if(o){var a=lt(e,t),c=a[1],s=a[2];if(c>s)return!0}t=t.parentNode}while(t&&t!==document.body);return!1},Fn=function(e){var n=e.scrollTop,t=e.scrollHeight,o=e.clientHeight;return[n,t,o]},jn=function(e){var n=e.scrollLeft,t=e.scrollWidth,o=e.clientWidth;return[n,t,o]},ct=function(e,n){return e==="v"?_n(n):Ln(n)},lt=function(e,n){return e==="v"?Fn(n):jn(n)},Un=function(e,n){return e==="h"&&n==="rtl"?-1:1},Bn=function(e,n,t,o,a){var c=Un(e,window.getComputedStyle(n).direction),s=c*o,l=t.target,d=n.contains(l),u=!1,f=s>0,v=0,x=0;do{var p=lt(e,l),N=p[0],m=p[1],b=p[2],w=m-b-c*N;(N||w)&&ct(e,l)&&(v+=w,x+=N),l=l.parentNode}while(!d&&l!==document.body||d&&(n.contains(l)||n===l));return(f&&(v===0||!a)||!f&&(x===0||!a))&&(u=!0),u},ie=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},qe=function(e){return[e.deltaX,e.deltaY]},ze=function(e){return e&&"current"in e?e.current:e},Kn=function(e,n){return e[0]===n[0]&&e[1]===n[1]},Vn=function(e){return`
  .block-interactivity-`.concat(e,` {pointer-events: none;}
  .allow-interactivity-`).concat(e,` {pointer-events: all;}
`)},Wn=0,q=[];function Hn(e){var n=r.useRef([]),t=r.useRef([0,0]),o=r.useRef(),a=r.useState(Wn++)[0],c=r.useState(function(){return _t()})[0],s=r.useRef(e);r.useEffect(function(){s.current=e},[e]),r.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(a));var m=Lt([e.lockRef.current],(e.shards||[]).map(ze),!0).filter(Boolean);return m.forEach(function(b){return b.classList.add("allow-interactivity-".concat(a))}),function(){document.body.classList.remove("block-interactivity-".concat(a)),m.forEach(function(b){return b.classList.remove("allow-interactivity-".concat(a))})}}},[e.inert,e.lockRef.current,e.shards]);var l=r.useCallback(function(m,b){if("touches"in m&&m.touches.length===2)return!s.current.allowPinchZoom;var w=ie(m),$=t.current,S="deltaX"in m?m.deltaX:$[0]-w[0],y="deltaY"in m?m.deltaY:$[1]-w[1],R,D=m.target,O=Math.abs(S)>Math.abs(y)?"h":"v";if("touches"in m&&O==="h"&&D.type==="range")return!1;var P=Ge(O,D);if(!P)return!0;if(P?R=O:(R=O==="v"?"h":"v",P=Ge(O,D)),!P)return!1;if(!o.current&&"changedTouches"in m&&(S||y)&&(o.current=R),!R)return!0;var F=o.current||R;return Bn(F,b,m,F==="h"?S:y,!0)},[]),d=r.useCallback(function(m){var b=m;if(!(!q.length||q[q.length-1]!==c)){var w="deltaY"in b?qe(b):ie(b),$=n.current.filter(function(R){return R.name===b.type&&R.target===b.target&&Kn(R.delta,w)})[0];if($&&$.should){b.cancelable&&b.preventDefault();return}if(!$){var S=(s.current.shards||[]).map(ze).filter(Boolean).filter(function(R){return R.contains(b.target)}),y=S.length>0?l(b,S[0]):!s.current.noIsolation;y&&b.cancelable&&b.preventDefault()}}},[]),u=r.useCallback(function(m,b,w,$){var S={name:m,delta:b,target:w,should:$};n.current.push(S),setTimeout(function(){n.current=n.current.filter(function(y){return y!==S})},1)},[]),f=r.useCallback(function(m){t.current=ie(m),o.current=void 0},[]),v=r.useCallback(function(m){u(m.type,qe(m),m.target,l(m,e.lockRef.current))},[]),x=r.useCallback(function(m){u(m.type,ie(m),m.target,l(m,e.lockRef.current))},[]);r.useEffect(function(){return q.push(c),e.setCallbacks({onScrollCapture:v,onWheelCapture:v,onTouchMoveCapture:x}),document.addEventListener("wheel",d,G),document.addEventListener("touchmove",d,G),document.addEventListener("touchstart",f,G),function(){q=q.filter(function(m){return m!==c}),document.removeEventListener("wheel",d,G),document.removeEventListener("touchmove",d,G),document.removeEventListener("touchstart",f,G)}},[]);var p=e.removeScrollBar,N=e.inert;return r.createElement(r.Fragment,null,N?r.createElement(c,{styles:Vn(a)}):null,p?r.createElement(Ft,{gapMode:"margin"}):null)}const Xn=jt(ot,Hn);var st=r.forwardRef(function(e,n){return r.createElement(de,z({},e,{ref:n,sideCar:Xn}))});st.classNames=de.classNames;const it="Dialog",[ut,Cr]=an(it),[Yn,X]=ut(it),Gn=e=>{const{__scopeDialog:n,children:t,open:o,defaultOpen:a,onOpenChange:c,modal:s=!0}=e,l=r.useRef(null),d=r.useRef(null),[u=!1,f]=un({prop:o,defaultProp:a,onChange:c});return r.createElement(Yn,{scope:n,triggerRef:l,contentRef:d,contentId:ge(),titleId:ge(),descriptionId:ge(),open:u,onOpenChange:f,onOpenToggle:r.useCallback(()=>f(v=>!v),[f]),modal:s},t)},dt="DialogPortal",[qn,ft]=ut(dt,{forceMount:void 0}),zn=e=>{const{__scopeDialog:n,forceMount:t,children:o,container:a}=e,c=X(dt,n);return r.createElement(qn,{scope:n,forceMount:t},r.Children.map(o,s=>r.createElement(ue,{present:t||c.open},r.createElement(On,{asChild:!0,container:a},s))))},ke="DialogOverlay",Zn=r.forwardRef((e,n)=>{const t=ft(ke,e.__scopeDialog),{forceMount:o=t.forceMount,...a}=e,c=X(ke,e.__scopeDialog);return c.modal?r.createElement(ue,{present:o||c.open},r.createElement(Qn,T({},a,{ref:n}))):null}),Qn=r.forwardRef((e,n)=>{const{__scopeDialog:t,...o}=e,a=X(ke,t);return r.createElement(st,{as:Oe,allowPinchZoom:!0,shards:[a.contentRef]},r.createElement(L.div,T({"data-state":vt(a.open)},o,{ref:n,style:{pointerEvents:"auto",...o.style}})))}),ne="DialogContent",Jn=r.forwardRef((e,n)=>{const t=ft(ne,e.__scopeDialog),{forceMount:o=t.forceMount,...a}=e,c=X(ne,e.__scopeDialog);return r.createElement(ue,{present:o||c.open},c.modal?r.createElement(er,T({},a,{ref:n})):r.createElement(tr,T({},a,{ref:n})))}),er=r.forwardRef((e,n)=>{const t=X(ne,e.__scopeDialog),o=r.useRef(null),a=oe(n,t.contentRef,o);return r.useEffect(()=>{const c=o.current;if(c)return Ut(c)},[]),r.createElement(mt,T({},e,{ref:a,trapFocus:t.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:Q(e.onCloseAutoFocus,c=>{var s;c.preventDefault(),(s=t.triggerRef.current)===null||s===void 0||s.focus()}),onPointerDownOutside:Q(e.onPointerDownOutside,c=>{const s=c.detail.originalEvent,l=s.button===0&&s.ctrlKey===!0;(s.button===2||l)&&c.preventDefault()}),onFocusOutside:Q(e.onFocusOutside,c=>c.preventDefault())}))}),tr=r.forwardRef((e,n)=>{const t=X(ne,e.__scopeDialog),o=r.useRef(!1),a=r.useRef(!1);return r.createElement(mt,T({},e,{ref:n,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:c=>{var s;if((s=e.onCloseAutoFocus)===null||s===void 0||s.call(e,c),!c.defaultPrevented){var l;o.current||(l=t.triggerRef.current)===null||l===void 0||l.focus(),c.preventDefault()}o.current=!1,a.current=!1},onInteractOutside:c=>{var s,l;(s=e.onInteractOutside)===null||s===void 0||s.call(e,c),c.defaultPrevented||(o.current=!0,c.detail.originalEvent.type==="pointerdown"&&(a.current=!0));const d=c.target;((l=t.triggerRef.current)===null||l===void 0?void 0:l.contains(d))&&c.preventDefault(),c.detail.originalEvent.type==="focusin"&&a.current&&c.preventDefault()}}))}),mt=r.forwardRef((e,n)=>{const{__scopeDialog:t,trapFocus:o,onOpenAutoFocus:a,onCloseAutoFocus:c,...s}=e,l=X(ne,t),d=r.useRef(null),u=oe(n,d);return Tn(),r.createElement(r.Fragment,null,r.createElement(Cn,{asChild:!0,loop:!0,trapped:o,onMountAutoFocus:a,onUnmountAutoFocus:c},r.createElement(En,T({role:"dialog",id:l.contentId,"aria-describedby":l.descriptionId,"aria-labelledby":l.titleId,"data-state":vt(l.open)},s,{ref:u,onDismiss:()=>l.onOpenChange(!1)}))),!1)});function vt(e){return e?"open":"closed"}const nr=Gn,rr=zn,or=Zn,ar=Jn;var te='[cmdk-group=""]',we='[cmdk-group-items=""]',cr='[cmdk-group-heading=""]',Ie='[cmdk-item=""]',Ze=`${Ie}:not([aria-disabled="true"])`,De="cmdk-item-select",K="data-value",lr=(e,n,t)=>rn(e,n,t),pt=r.createContext(void 0),ae=()=>r.useContext(pt),ht=r.createContext(void 0),Ae=()=>r.useContext(ht),bt=r.createContext(void 0),gt=r.forwardRef((e,n)=>{let t=Z(()=>{var i,g;return{search:"",value:(g=(i=e.value)!=null?i:e.defaultValue)!=null?g:"",filtered:{count:0,items:new Map,groups:new Set}}}),o=Z(()=>new Set),a=Z(()=>new Map),c=Z(()=>new Map),s=Z(()=>new Set),l=$t(e),{label:d,children:u,value:f,onValueChange:v,filter:x,shouldFilter:p,loop:N,disablePointerSelection:m=!1,vimBindings:b=!0,...w}=e,$=r.useId(),S=r.useId(),y=r.useId(),R=r.useRef(null),D=gr();W(()=>{if(f!==void 0){let i=f.trim();t.current.value=i,O.emit()}},[f]),W(()=>{D(6,Te)},[]);let O=r.useMemo(()=>({subscribe:i=>(s.current.add(i),()=>s.current.delete(i)),snapshot:()=>t.current,setState:(i,g,C)=>{var h,k,I;if(!Object.is(t.current[i],g)){if(t.current[i]=g,i==="search")me(),Y(),D(1,U);else if(i==="value"&&(C||D(5,Te),((h=l.current)==null?void 0:h.value)!==void 0)){let _=g??"";(I=(k=l.current).onValueChange)==null||I.call(k,_);return}O.emit()}},emit:()=>{s.current.forEach(i=>i())}}),[]),P=r.useMemo(()=>({value:(i,g,C)=>{var h;g!==((h=c.current.get(i))==null?void 0:h.value)&&(c.current.set(i,{value:g,keywords:C}),t.current.filtered.items.set(i,F(g,C)),D(2,()=>{Y(),O.emit()}))},item:(i,g)=>(o.current.add(i),g&&(a.current.has(g)?a.current.get(g).add(i):a.current.set(g,new Set([i]))),D(3,()=>{me(),Y(),t.current.value||U(),O.emit()}),()=>{c.current.delete(i),o.current.delete(i),t.current.filtered.items.delete(i);let C=J();D(4,()=>{me(),C?.getAttribute("id")===i&&U(),O.emit()})}),group:i=>(a.current.has(i)||a.current.set(i,new Set),()=>{c.current.delete(i),a.current.delete(i)}),filter:()=>l.current.shouldFilter,label:d||e["aria-label"],disablePointerSelection:m,listId:$,inputId:y,labelId:S,listInnerRef:R}),[]);function F(i,g){var C,h;let k=(h=(C=l.current)==null?void 0:C.filter)!=null?h:lr;return i?k(i,t.current.search,g):0}function Y(){if(!t.current.search||l.current.shouldFilter===!1)return;let i=t.current.filtered.items,g=[];t.current.filtered.groups.forEach(h=>{let k=a.current.get(h),I=0;k.forEach(_=>{let B=i.get(_);I=Math.max(B,I)}),g.push([h,I])});let C=R.current;ee().sort((h,k)=>{var I,_;let B=h.getAttribute("id"),ce=k.getAttribute("id");return((I=i.get(ce))!=null?I:0)-((_=i.get(B))!=null?_:0)}).forEach(h=>{let k=h.closest(we);k?k.appendChild(h.parentElement===k?h:h.closest(`${we} > *`)):C.appendChild(h.parentElement===C?h:h.closest(`${we} > *`))}),g.sort((h,k)=>k[1]-h[1]).forEach(h=>{let k=R.current.querySelector(`${te}[${K}="${encodeURIComponent(h[0])}"]`);k?.parentElement.appendChild(k)})}function U(){let i=ee().find(C=>C.getAttribute("aria-disabled")!=="true"),g=i?.getAttribute(K);O.setState("value",g||void 0)}function me(){var i,g,C,h;if(!t.current.search||l.current.shouldFilter===!1){t.current.filtered.count=o.current.size;return}t.current.filtered.groups=new Set;let k=0;for(let I of o.current){let _=(g=(i=c.current.get(I))==null?void 0:i.value)!=null?g:"",B=(h=(C=c.current.get(I))==null?void 0:C.keywords)!=null?h:[],ce=F(_,B);t.current.filtered.items.set(I,ce),ce>0&&k++}for(let[I,_]of a.current)for(let B of _)if(t.current.filtered.items.get(B)>0){t.current.filtered.groups.add(I);break}t.current.filtered.count=k}function Te(){var i,g,C;let h=J();h&&(((i=h.parentElement)==null?void 0:i.firstChild)===h&&((C=(g=h.closest(te))==null?void 0:g.querySelector(cr))==null||C.scrollIntoView({block:"nearest"})),h.scrollIntoView({block:"nearest"}))}function J(){var i;return(i=R.current)==null?void 0:i.querySelector(`${Ie}[aria-selected="true"]`)}function ee(){var i;return Array.from((i=R.current)==null?void 0:i.querySelectorAll(Ze))}function ve(i){let g=ee()[i];g&&O.setState("value",g.getAttribute(K))}function pe(i){var g;let C=J(),h=ee(),k=h.findIndex(_=>_===C),I=h[k+i];(g=l.current)!=null&&g.loop&&(I=k+i<0?h[h.length-1]:k+i===h.length?h[0]:h[k+i]),I&&O.setState("value",I.getAttribute(K))}function Me(i){let g=J(),C=g?.closest(te),h;for(;C&&!h;)C=i>0?hr(C,te):br(C,te),h=C?.querySelector(Ze);h?O.setState("value",h.getAttribute(K)):pe(i)}let _e=()=>ve(ee().length-1),Le=i=>{i.preventDefault(),i.metaKey?_e():i.altKey?Me(1):pe(1)},Fe=i=>{i.preventDefault(),i.metaKey?ve(0):i.altKey?Me(-1):pe(-1)};return r.createElement(L.div,{ref:n,tabIndex:-1,...w,"cmdk-root":"",onKeyDown:i=>{var g;if((g=w.onKeyDown)==null||g.call(w,i),!i.defaultPrevented)switch(i.key){case"n":case"j":{b&&i.ctrlKey&&Le(i);break}case"ArrowDown":{Le(i);break}case"p":case"k":{b&&i.ctrlKey&&Fe(i);break}case"ArrowUp":{Fe(i);break}case"Home":{i.preventDefault(),ve(0);break}case"End":{i.preventDefault(),_e();break}case"Enter":if(!i.nativeEvent.isComposing&&i.keyCode!==229){i.preventDefault();let C=J();if(C){let h=new Event(De);C.dispatchEvent(h)}}}}},r.createElement("label",{"cmdk-label":"",htmlFor:P.inputId,id:P.labelId,style:yr},d),fe(e,i=>r.createElement(ht.Provider,{value:O},r.createElement(pt.Provider,{value:P},i))))}),sr=r.forwardRef((e,n)=>{var t,o;let a=r.useId(),c=r.useRef(null),s=r.useContext(bt),l=ae(),d=$t(e),u=(o=(t=d.current)==null?void 0:t.forceMount)!=null?o:s?.forceMount;W(()=>{if(!u)return l.item(a,s?.id)},[u]);let f=yt(a,c,[e.value,e.children,c],e.keywords),v=Ae(),x=H(D=>D.value&&D.value===f.current),p=H(D=>u||l.filter()===!1?!0:D.search?D.filtered.items.get(a)>0:!0);r.useEffect(()=>{let D=c.current;if(!(!D||e.disabled))return D.addEventListener(De,N),()=>D.removeEventListener(De,N)},[p,e.onSelect,e.disabled]);function N(){var D,O;m(),(O=(D=d.current).onSelect)==null||O.call(D,f.current)}function m(){v.setState("value",f.current,!0)}if(!p)return null;let{disabled:b,value:w,onSelect:$,forceMount:S,keywords:y,...R}=e;return r.createElement(L.div,{ref:re([c,n]),...R,id:a,"cmdk-item":"",role:"option","aria-disabled":!!b,"aria-selected":!!x,"data-disabled":!!b,"data-selected":!!x,onPointerMove:b||l.disablePointerSelection?void 0:m,onClick:b?void 0:N},e.children)}),ir=r.forwardRef((e,n)=>{let{heading:t,children:o,forceMount:a,...c}=e,s=r.useId(),l=r.useRef(null),d=r.useRef(null),u=r.useId(),f=ae(),v=H(p=>a||f.filter()===!1?!0:p.search?p.filtered.groups.has(s):!0);W(()=>f.group(s),[]),yt(s,l,[e.value,e.heading,d]);let x=r.useMemo(()=>({id:s,forceMount:a}),[a]);return r.createElement(L.div,{ref:re([l,n]),...c,"cmdk-group":"",role:"presentation",hidden:v?void 0:!0},t&&r.createElement("div",{ref:d,"cmdk-group-heading":"","aria-hidden":!0,id:u},t),fe(e,p=>r.createElement("div",{"cmdk-group-items":"",role:"group","aria-labelledby":t?u:void 0},r.createElement(bt.Provider,{value:x},p))))}),ur=r.forwardRef((e,n)=>{let{alwaysRender:t,...o}=e,a=r.useRef(null),c=H(s=>!s.search);return!t&&!c?null:r.createElement(L.div,{ref:re([a,n]),...o,"cmdk-separator":"",role:"separator"})}),dr=r.forwardRef((e,n)=>{let{onValueChange:t,...o}=e,a=e.value!=null,c=Ae(),s=H(f=>f.search),l=H(f=>f.value),d=ae(),u=r.useMemo(()=>{var f;let v=(f=d.listInnerRef.current)==null?void 0:f.querySelector(`${Ie}[${K}="${encodeURIComponent(l)}"]`);return v?.getAttribute("id")},[]);return r.useEffect(()=>{e.value!=null&&c.setState("search",e.value)},[e.value]),r.createElement(L.input,{ref:n,...o,"cmdk-input":"",autoComplete:"off",autoCorrect:"off",spellCheck:!1,"aria-autocomplete":"list",role:"combobox","aria-expanded":!0,"aria-controls":d.listId,"aria-labelledby":d.labelId,"aria-activedescendant":u,id:d.inputId,type:"text",value:a?e.value:s,onChange:f=>{a||c.setState("search",f.target.value),t?.(f.target.value)}})}),fr=r.forwardRef((e,n)=>{let{children:t,label:o="Suggestions",...a}=e,c=r.useRef(null),s=r.useRef(null),l=ae();return r.useEffect(()=>{if(s.current&&c.current){let d=s.current,u=c.current,f,v=new ResizeObserver(()=>{f=requestAnimationFrame(()=>{let x=d.offsetHeight;u.style.setProperty("--cmdk-list-height",x.toFixed(1)+"px")})});return v.observe(d),()=>{cancelAnimationFrame(f),v.unobserve(d)}}},[]),r.createElement(L.div,{ref:re([c,n]),...a,"cmdk-list":"",role:"listbox","aria-label":o,id:l.listId},fe(e,d=>r.createElement("div",{ref:re([s,l.listInnerRef]),"cmdk-list-sizer":""},d)))}),mr=r.forwardRef((e,n)=>{let{open:t,onOpenChange:o,overlayClassName:a,contentClassName:c,container:s,...l}=e;return r.createElement(nr,{open:t,onOpenChange:o},r.createElement(rr,{container:s},r.createElement(or,{"cmdk-overlay":"",className:a}),r.createElement(ar,{"aria-label":e.label,"cmdk-dialog":"",className:c},r.createElement(gt,{ref:n,...l}))))}),vr=r.forwardRef((e,n)=>H(t=>t.filtered.count===0)?r.createElement(L.div,{ref:n,...e,"cmdk-empty":"",role:"presentation"}):null),pr=r.forwardRef((e,n)=>{let{progress:t,children:o,label:a="Loading...",...c}=e;return r.createElement(L.div,{ref:n,...c,"cmdk-loading":"",role:"progressbar","aria-valuenow":t,"aria-valuemin":0,"aria-valuemax":100,"aria-label":a},fe(e,s=>r.createElement("div",{"aria-hidden":!0},s)))}),M=Object.assign(gt,{List:fr,Item:sr,Input:dr,Group:ir,Separator:ur,Dialog:mr,Empty:vr,Loading:pr});function hr(e,n){let t=e.nextElementSibling;for(;t;){if(t.matches(n))return t;t=t.nextElementSibling}}function br(e,n){let t=e.previousElementSibling;for(;t;){if(t.matches(n))return t;t=t.previousElementSibling}}function $t(e){let n=r.useRef(e);return W(()=>{n.current=e}),n}var W=typeof window>"u"?r.useEffect:r.useLayoutEffect;function Z(e){let n=r.useRef();return n.current===void 0&&(n.current=e()),n}function re(e){return n=>{e.forEach(t=>{typeof t=="function"?t(n):t!=null&&(t.current=n)})}}function H(e){let n=Ae(),t=()=>e(n.snapshot());return r.useSyncExternalStore(n.subscribe,t,t)}function yt(e,n,t,o=[]){let a=r.useRef(),c=ae();return W(()=>{var s;let l=(()=>{var u;for(let f of t){if(typeof f=="string")return f.trim();if(typeof f=="object"&&"current"in f)return f.current?(u=f.current.textContent)==null?void 0:u.trim():a.current}})(),d=o.map(u=>u.trim());c.value(e,l,d),(s=n.current)==null||s.setAttribute(K,l),a.current=l}),a}var gr=()=>{let[e,n]=r.useState(),t=Z(()=>new Map);return W(()=>{t.current.forEach(o=>o()),t.current=new Map},[e]),(o,a)=>{t.current.set(o,a),n({})}};function $r(e){let n=e.type;return typeof n=="function"?n(e.props):"render"in n?n.render(e.props):e}function fe({asChild:e,children:n},t){return e&&r.isValidElement(n)?r.cloneElement($r(n),{ref:n.ref},t(n.props.children)):t(n)}var yr={position:"absolute",width:"1px",height:"1px",padding:"0",margin:"-1px",overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",borderWidth:"0"};const Et=r.forwardRef(({className:e,...n},t)=>E.jsx(M,{ref:t,className:A("flex h-full w-full flex-col overflow-hidden rounded-md bg-popover text-popover-foreground",e),...n}));Et.displayName=M.displayName;const xt=r.forwardRef(({className:e,...n},t)=>E.jsxs("div",{className:"flex items-center border-b px-3","cmdk-input-wrapper":"",children:[E.jsx(Bt,{className:"mr-2 h-4 w-4 shrink-0 opacity-50"}),E.jsx(M.Input,{ref:t,className:A("flex h-11 w-full rounded-md bg-transparent py-3 text-sm outline-none placeholder:text-muted-foreground disabled:cursor-not-allowed disabled:opacity-50",e),...n})]}));xt.displayName=M.Input.displayName;const wt=r.forwardRef(({className:e,...n},t)=>E.jsx(M.List,{ref:t,className:A("overflow-x-hidden",e),...n}));wt.displayName=M.List.displayName;const Ct=r.forwardRef((e,n)=>E.jsx(M.Empty,{ref:n,className:"py-6 text-center text-sm",...e}));Ct.displayName=M.Empty.displayName;const St=r.forwardRef(({className:e,...n},t)=>E.jsx(M.Group,{ref:t,className:A("overflow-hidden p-1 text-foreground [&_[cmdk-group-heading]]:px-2 [&_[cmdk-group-heading]]:py-1.5 [&_[cmdk-group-heading]]:text-xs [&_[cmdk-group-heading]]:font-medium [&_[cmdk-group-heading]]:text-muted-foreground",e),...n}));St.displayName=M.Group.displayName;const Er=r.forwardRef(({className:e,...n},t)=>E.jsx(M.Separator,{ref:t,className:A("-mx-1 h-px bg-border",e),...n}));Er.displayName=M.Separator.displayName;const Nt=r.forwardRef(({className:e,...n},t)=>E.jsx(M.Item,{ref:t,className:A("relative flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none data-[disabled=true]:pointer-events-none data-[selected='true']:bg-accent data-[selected=true]:text-accent-foreground data-[disabled=true]:opacity-50",e),...n}));Nt.displayName=M.Item.displayName;const Sr=({options:e,value:n,onChange:t,placeholder:o,disabled:a,emptyMessage:c="No results found.",className:s,renderOption:l,groupBy:d,searchPlaceholder:u="Search...",maxHeight:f="300px"})=>{const[v,x]=r.useState(!1),[p,N]=r.useState(""),m=e.filter($=>p?$.label.toLowerCase().includes(p.toLowerCase()):!0);console.log(`EnhancedDropdown: ${m.length}/${e.length} options after filtering with query: "${p}"`),e.length>0&&e[0].model&&console.log("EnhancedDropdown (Model): First 5 options:",m.slice(0,5).map($=>`${$.model} (${$.generation})`));const b=d?m.reduce(($,S)=>{const y=d(S);return $[y]||($[y]=[]),$[y].push(S),$},{}):{all:m};e.length>0&&e[0].model&&(console.log(`EnhancedDropdown: Grouped ${m.length} options into ${Object.keys(b).length} groups`),console.log("EnhancedDropdown: Group keys:",Object.keys(b).join(", ")));const w=e.find($=>$.value===n);return e.length===0&&console.warn("EnhancedDropdown: No options provided to dropdown"),E.jsxs(Kt,{open:v,onOpenChange:x,children:[E.jsx(Vt,{asChild:!0,children:E.jsxs(Wt,{variant:"outline",role:"combobox","aria-expanded":v,disabled:a,className:A("w-full justify-between border-2 h-14 px-4 py-6","bg-white hover:bg-gray-50","rounded-full shadow-sm","text-left font-normal",a&&"opacity-50 cursor-not-allowed",s),children:[w?l?l(w):w.label:E.jsx("span",{className:"text-muted-foreground",children:o}),E.jsx(Gt,{className:"ml-2 h-4 w-4 shrink-0 opacity-50"})]})}),E.jsx(Ht,{className:"p-0 rounded-3xl shadow-xl border-none w-full min-w-[240px]",align:"start",side:"bottom",avoidCollisions:!0,collisionPadding:20,sticky:"always",children:E.jsxs(Et,{className:"rounded-3xl",children:[E.jsx(xt,{placeholder:u,className:"h-12 rounded-t-3xl",value:p,onValueChange:N}),E.jsxs(wt,{className:"overflow-y-auto",style:{maxHeight:f},children:[E.jsx(Ct,{children:c}),Object.entries(b).map(([$,S])=>E.jsx(St,{heading:$!=="all"?$:void 0,children:S.map(y=>E.jsx(Nt,{value:y.value,onSelect:()=>{t(y.value),x(!1),N("")},className:"py-2 px-3 cursor-pointer",children:E.jsxs("div",{className:"flex items-center justify-between w-full",children:[E.jsx("div",{className:"flex items-center",children:l?l(y):y.label}),n===y.value&&E.jsx(Xt,{className:"h-4 w-4 text-primary"})]})},y.value))},$))]})]})})]})};export{Et as C,Sr as E,wr as V,xt as a,Ct as b,St as c,Nt as d};
