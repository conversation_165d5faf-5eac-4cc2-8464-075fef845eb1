const o={useSupabaseBackend:!0,useLocalStorage:!1,enableProductFeatures:!0,enableProductCreation:!0,enableProductEditing:!0,enableProductDeletion:!0,enableBulkOperations:!0,enableRealTimeUpdates:!0,enableLiveSync:!0,enableImageUpload:!0,enableFileStorage:!0,enableMigrationTools:!0,showMigrationStatus:!0,enableDebugMode:!1,showPerformanceMetrics:!1,enableTestingTools:!1};let a={...o};function u(){return{...a}}function E(e){return a[e]}function n(e){a={...a,...e},console.log("Features updated:",e)}const l={development:{useSupabaseBackend:!1,useLocalStorage:!0,enableProductFeatures:!0,enableRealTimeUpdates:!1,enableImageUpload:!1,enableMigrationTools:!0,enableDebugMode:!0,showPerformanceMetrics:!0,enableTestingTools:!0},testing:{useSupabaseBackend:!0,useLocalStorage:!1,enableProductFeatures:!0,enableRealTimeUpdates:!0,enableImageUpload:!0,enableMigrationTools:!0,enableDebugMode:!0,showPerformanceMetrics:!1,enableTestingTools:!0},production:{useSupabaseBackend:!0,useLocalStorage:!1,enableProductFeatures:!0,enableRealTimeUpdates:!0,enableImageUpload:!0,enableMigrationTools:!1,enableDebugMode:!1,showPerformanceMetrics:!1,enableTestingTools:!1}};function r(e){const t=l[e];n(t),console.log(`Applied ${e} feature preset`)}function s(){r("production")}s();export{u as g,E as i};
