import{c as Ee,u as E,ag as Te,a as ee,bb as S,R as ge,j as s,bc as qe,bd as We,be as Fe,bf as Re,bg as Ae,bh as Ye,bi as Ie,bj as Le,B,aY as X,bk as Be,bl as te,a5 as re,aC as He,bm as pe,bn as ye,bo as we,a7 as Qe,r as v,bp as ne,bq as ae,br as Ve,aZ as w,ar as ze,bs as Ue,P as Xe,ak as Ge,N as Je,n as I,bt as Ke,bu as xe,ac as H,ad as Q,ae as V,bv as L,af as z,aw as $e,I as Ze,bw as et,bx as tt,by as rt,bz as nt,bA as at,bB as st,bC as ot,bD as it,O as ct,bE as K,bF as ut,bG as W,at as ie,_ as lt,bH as dt}from"./index-DRgoPWv8.js";import{H as mt}from"./house-SNzrPgE_.js";import{S as be}from"./settings-yQBrDMIA.js";/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ht=Ee("Bell",[["path",{d:"M6 8a6 6 0 0 1 12 0c0 7 3 9 3 9H3s3-2 3-9",key:"1qo2s2"}],["path",{d:"M10.3 21a1.94 1.94 0 0 0 3.4 0",key:"qgo35s"}]]);function ve({isOpen:t,onClose:e,role:r}){const{t:n}=E(),a=Te(),{toast:o}=ee(),{supplierRole:c,merchantRole:i,setSupplierRole:m,setMerchantRole:d}=S(),[u,l]=ge.useState(!1),h=c&&i,p=!!r,y=r==="supplier"&&!i||r==="merchant"&&!c,x=async()=>{try{l(!0);const{success:M,error:q}=await Be(r);M?(r==="supplier"?m(!1):(r==="merchant"||m(!1),d(!1)),o({title:n("auth.logoutSuccess"),description:n("auth.logoutSuccessDescription")}),e(),(!r||y)&&setTimeout(()=>{a("/")},500)):(o({title:n("auth.logoutFailed"),description:q?.message||n("auth.logoutFailedDescription"),variant:"destructive"}),e())}catch(M){console.error("Error during logout:",M),o({title:n("auth.logoutFailed"),description:n("auth.unexpectedError"),variant:"destructive"}),e()}finally{l(!1)}},T=()=>{if(p){if(r==="supplier")return s.jsxs("div",{className:"flex items-center gap-2 text-xl",children:[s.jsx(te,{className:"h-5 w-5 text-[#fa7b00]"}),n("auth.logoutFromSupplierAccount")]});if(r==="merchant")return s.jsxs("div",{className:"flex items-center gap-2 text-xl",children:[s.jsx(re,{className:"h-5 w-5 text-[#071c44]"}),n("auth.logoutFromMerchantAccount")]})}return s.jsxs("div",{className:"flex items-center gap-2 text-xl",children:[s.jsx(X,{className:"h-5 w-5 text-primary"}),n("auth.confirmLogout")]})},k=()=>n(p&&h?"auth.logoutRoleSpecificMessage":"auth.logoutConfirmationMessage");return s.jsx(qe,{open:t,onOpenChange:e,children:s.jsxs(We,{className:"max-w-md",children:[s.jsxs(Fe,{children:[s.jsx(Re,{children:T()}),s.jsx(Ae,{className:"pt-2",children:k()})]}),s.jsxs(Ye,{className:"mt-4",children:[s.jsx(Ie,{disabled:u,children:n("actions.cancel")}),s.jsx(Le,{onClick:M=>{M.preventDefault(),x()},disabled:u,className:"bg-primary hover:bg-primary/90",children:n(u?"auth.loggingOut":"auth.confirmLogoutAction")})]})]})})}function ft({role:t}){const{t:e}=E(),[r,n]=ge.useState(!1);return S(),s.jsxs(s.Fragment,{children:[s.jsxs(B,{variant:"ghost",className:"w-full justify-start text-destructive hover:text-destructive hover:bg-destructive/10",onClick:()=>n(!0),children:[s.jsx(X,{className:"mr-2 h-4 w-4"}),e(t?t==="supplier"?"auth.logoutFromSupplier":"auth.logoutFromMerchant":"navigation.logout")]}),s.jsx(ve,{isOpen:r,onClose:()=>n(!1),role:t})]})}const gt="https://irkwpzcskeqtasutqnxp.supabase.co",pt="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imlya3dwemNza2VxdGFzdXRxbnhwIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDc0OTg2ODMsImV4cCI6MjA2MzA3NDY4M30.bUv9zxx9q_sVUwv_-G6WbHsA53-SU5tCCWbOS_5Xp94",_=He(gt,pt);let Y=null,$=0;const yt=5*60*1e3;async function se(){try{if(Y&&Date.now()-$<yt)return Y;const{data:t}=await pe();if(!t.user?.id)throw new Error("User not authenticated");return Y=t.user.id,$=Date.now(),t.user.id}catch(t){throw console.error("Error getting current user ID:",t),Y=null,$=0,new Error("Authentication required")}}async function _e(){try{const t=await se(),e=new Date,r=new Date(e.getFullYear(),e.getMonth(),1),n=new Date(e.setDate(e.getDate()-e.getDay())),a=new Date(e.getFullYear(),e.getMonth(),e.getDate()),o=new Date(e.getFullYear(),e.getMonth()-1,1),c=new Date(e.getFullYear(),e.getMonth(),0),{data:i,error:m}=await _.from("order_items").select(`
        total_price,
        created_at,
        order_id,
        orders(
          status,
          created_at,
          confirmed_at,
          delivered_at
        )
      `).eq("supplier_account_id",t);if(m)throw console.error("Error fetching orders:",m),m;const{data:d,error:u}=await _.from("products").select("id, status, stock_quantity, created_at").eq("user_id",t);if(u)throw console.error("Error fetching products:",u),u;const l=i?.reduce((f,b)=>f+Number(b.total_price),0)||0,h=i?.filter(f=>new Date(f.created_at)>=r).reduce((f,b)=>f+Number(b.total_price),0)||0,p=i?.filter(f=>new Date(f.created_at)>=n).reduce((f,b)=>f+Number(b.total_price),0)||0,y=i?.filter(f=>new Date(f.created_at)>=a).reduce((f,b)=>f+Number(b.total_price),0)||0,T=[...new Set(i?.map(f=>f.order_id)||[])].length,k=i?.reduce((f,b)=>{const oe=b.orders?.status||"pending";return f[oe]=(f[oe]||0)+1,f},{})||{},M=d?.length||0,q=d?.filter(f=>f.status==="active").length||0,Se=d?.filter(f=>f.status==="out_of_stock"||f.stock_quantity===0).length||0,Ce=d?.filter(f=>f.stock_quantity>0&&f.stock_quantity<=10).length||0,J=i?.filter(f=>{const b=new Date(f.created_at);return b>=o&&b<=c}).reduce((f,b)=>f+Number(b.total_price),0)||0,Pe=J>0?(h-J)/J*100:0;return{totalRevenue:l,monthlyRevenue:h,weeklyRevenue:p,dailyRevenue:y,totalOrders:T,pendingOrders:k.pending||0,confirmedOrders:k.confirmed||0,shippedOrders:k.shipped||0,deliveredOrders:k.delivered||0,totalProducts:M,activeProducts:q,outOfStockProducts:Se,lowStockProducts:Ce,revenueGrowth:Pe,orderGrowth:0,productGrowth:0}}catch(t){throw console.error("Error fetching dashboard metrics:",t),t}}async function wt(){try{const t=await se(),e=await _e(),[r,n,a,o,c,i]=await Promise.all([bt(t),Me(t),vt(t),De(t,10),_t(t),Mt(t)]),{data:m,error:d}=await _.from("orders").select("id").eq("status","confirmed").is("shipping_company_id",null);d&&console.error("Error fetching orders awaiting shipment:",d);const u=m?.length||0,l=await Dt(t);return{...e,ordersAwaitingShipment:u,unassignedOrders:u,topMerchantCustomers:r,topSellingProducts:n,inventoryAlerts:a,recentOrders:o,shippingOverview:c,financialSummary:i,customerInsights:l}}catch(t){throw console.error("Error fetching supplier dashboard data:",t),t}}async function xt(){try{const t=await se(),e=await _e(),[r,n,a,o,c]=await Promise.all([Me(t),De(t,10),jt(t),kt(t),Ot(t)]),i=e.totalOrders>0?e.totalRevenue/e.totalOrders:0;return{...e,newCustomers:a.newCustomers,returningCustomers:a.returningCustomers,averageOrderValue:i,conversionRate:2.5,topSellingProducts:r,customerAnalytics:a,storePerformance:o,recentOrders:n,inventoryStatus:c}}catch(t){throw console.error("Error fetching merchant dashboard data:",t),t}}async function bt(t){try{const{data:e,error:r}=await _.from("order_items").select(`
        orders(
          consumer_name,
          consumer_phone,
          delivery_wilaya,
          created_at,
          total_amount
        )
      `).eq("supplier_account_id",t).order("created_at",{ascending:!1});if(r)return console.error("Error fetching top customers:",r),[];const n=new Map;return e?.forEach(a=>{const o=a.orders;if(!o)return;const c=o.consumer_phone,i=n.get(c);i?(i.totalOrders+=1,i.totalSpent+=Number(o.total_amount),new Date(o.created_at)>new Date(i.lastOrderDate)&&(i.lastOrderDate=o.created_at)):n.set(c,{name:o.consumer_name||"Unknown Customer",phone:o.consumer_phone,totalOrders:1,totalSpent:Number(o.total_amount),lastOrderDate:o.created_at,wilaya:o.delivery_wilaya})}),Array.from(n.entries()).map(([a,o],c)=>({id:a,...o})).sort((a,o)=>o.totalSpent-a.totalSpent).slice(0,10)}catch(e){return console.error("Error in getTopMerchantCustomers:",e),[]}}async function Me(t){try{const{data:e,error:r}=await _.from("order_items").select(`
        product_id,
        quantity,
        total_price,
        products!inner(
          name,
          category,
          stock_quantity,
          images
        )
      `).eq("supplier_account_id",t);if(r)return console.error("Error fetching top products:",r),[];const n=new Map;return e?.forEach(a=>{const o=a.products;if(!o)return;const c=n.get(a.product_id);c?(c.totalSold+=a.quantity,c.revenue+=Number(a.total_price)):n.set(a.product_id,{name:o.name,category:o.category,totalSold:a.quantity,revenue:Number(a.total_price),stockQuantity:o.stock_quantity||0,image:o.images?.[0]})}),Array.from(n.entries()).map(([a,o])=>({id:a,...o,growth:0})).sort((a,o)=>o.revenue-a.revenue).slice(0,10)}catch(e){return console.error("Error in getTopSellingProducts:",e),[]}}async function vt(t){try{const{data:e,error:r}=await _.from("products").select("id, name, category, stock_quantity, status, updated_at").eq("user_id",t).or("status.eq.out_of_stock,stock_quantity.lte.10");return r?(console.error("Error fetching inventory alerts:",r),[]):e?.map(n=>{let a,o;return n.status==="out_of_stock"||n.stock_quantity===0?(a="out_of_stock",o=Math.floor((Date.now()-new Date(n.updated_at).getTime())/(1e3*60*60*24))):n.stock_quantity<=5?a="reorder_needed":a="low_stock",{id:`alert-${n.id}`,productId:n.id,productName:n.name,currentStock:n.stock_quantity||0,minimumStock:10,category:n.category,alertType:a,daysOutOfStock:o}})||[]}catch(e){return console.error("Error in getInventoryAlerts:",e),[]}}async function De(t,e=10){try{const{data:r,error:n}=await _.from("orders").select(`
        id,
        order_number,
        consumer_name,
        consumer_phone,
        status,
        total_amount,
        created_at,
        delivery_wilaya,
        payment_method,
        order_items!inner(
          supplier_account_id
        )
      `).eq("order_items.supplier_account_id",t).order("created_at",{ascending:!1}).limit(e);return n?(console.error("Error fetching recent orders:",n),[]):r?.map(a=>({id:a.id,orderNumber:a.order_number,customerName:a.consumer_name||"Unknown Customer",customerPhone:a.consumer_phone,status:a.status,totalAmount:Number(a.total_amount),itemCount:a.order_items?.length||0,createdAt:a.created_at,deliveryWilaya:a.delivery_wilaya,paymentMethod:a.payment_method}))||[]}catch(r){return console.error("Error in getRecentOrders:",r),[]}}async function _t(t){try{const{data:e,error:r}=await _.from("orders").select(`
        status,
        created_at,
        shipped_at,
        delivered_at,
        shipping_cost,
        order_items!inner(
          supplier_account_id
        )
      `).eq("order_items.supplier_account_id",t);if(r)return console.error("Error fetching shipping overview:",r),{readyForShipping:0,inTransit:0,delivered:0,averageDeliveryTime:0,shippingCosts:0};const n=e?.filter(d=>d.status==="confirmed").length||0,a=e?.filter(d=>d.status==="shipped").length||0,o=e?.filter(d=>d.status==="delivered").length||0,c=e?.reduce((d,u)=>d+(Number(u.shipping_cost)||0),0)||0,i=e?.filter(d=>d.status==="delivered"&&d.shipped_at&&d.delivered_at)||[],m=i.length>0?i.reduce((d,u)=>{const l=new Date(u.shipped_at),h=new Date(u.delivered_at);return d+(h.getTime()-l.getTime())/(1e3*60*60*24)},0)/i.length:0;return{readyForShipping:n,inTransit:a,delivered:o,averageDeliveryTime:Math.round(m*10)/10,shippingCosts:c}}catch(e){return console.error("Error in getShippingOverview:",e),{readyForShipping:0,inTransit:0,delivered:0,averageDeliveryTime:0,shippingCosts:0}}}async function Mt(t){try{const{data:e,error:r}=await _.from("orders").select(`
        status,
        total_amount,
        payment_method,
        arouz_fee,
        order_items!inner(
          supplier_account_id
        )
      `).eq("order_items.supplier_account_id",t);if(r)return console.error("Error fetching financial summary:",r),{totalRevenue:0,pendingPayments:0,paidOrders:0,arouzFees:0,netRevenue:0,paymentMethods:{cashOnDelivery:0,storePickup:0}};const n=e?.reduce((u,l)=>u+Number(l.total_amount),0)||0,a=e?.filter(u=>u.status==="pending"||u.status==="confirmed").reduce((u,l)=>u+Number(l.total_amount),0)||0,o=e?.filter(u=>u.status==="delivered").length||0,c=e?.reduce((u,l)=>u+(Number(l.arouz_fee)||0),0)||0,i=n-c,m=e?.filter(u=>u.payment_method==="cash_on_delivery").length||0,d=e?.filter(u=>u.payment_method==="store_pickup").length||0;return{totalRevenue:n,pendingPayments:a,paidOrders:o,arouzFees:c,netRevenue:i,paymentMethods:{cashOnDelivery:m,storePickup:d}}}catch(e){return console.error("Error in getFinancialSummary:",e),{totalRevenue:0,pendingPayments:0,paidOrders:0,arouzFees:0,netRevenue:0,paymentMethods:{cashOnDelivery:0,storePickup:0}}}}async function Dt(t){try{const{data:e,error:r}=await _.from("orders").select(`
        consumer_phone,
        delivery_wilaya,
        total_amount,
        created_at,
        order_items!inner(
          supplier_account_id
        )
      `).eq("order_items.supplier_account_id",t);if(r)return console.error("Error fetching customer insights:",r),{totalCustomers:0,newCustomersThisMonth:0,topWilayas:[],orderFrequency:{daily:0,weekly:0,monthly:0}};const a=new Set(e?.map(l=>l.consumer_phone)||[]).size,o=new Date(new Date().getFullYear(),new Date().getMonth(),1),c=e?.filter(l=>new Date(l.created_at)>=o).map(l=>l.consumer_phone).filter((l,h,p)=>p.indexOf(l)===h).length||0,i=new Map;e?.forEach(l=>{const h=i.get(l.delivery_wilaya);h?(h.orderCount+=1,h.revenue+=Number(l.total_amount)):i.set(l.delivery_wilaya,{orderCount:1,revenue:Number(l.total_amount)})});const m=Array.from(i.entries()).map(([l,h])=>({wilaya:l,...h})).sort((l,h)=>h.revenue-l.revenue).slice(0,5),d=e?.length||0,u={daily:Math.round(d/30*10)/10,weekly:Math.round(d/4*10)/10,monthly:d};return{totalCustomers:a,newCustomersThisMonth:c,topWilayas:m,orderFrequency:u}}catch(e){return console.error("Error in getCustomerInsights:",e),{totalCustomers:0,newCustomersThisMonth:0,topWilayas:[],orderFrequency:{daily:0,weekly:0,monthly:0}}}}async function jt(t){try{const{data:e,error:r}=await _.from("orders").select(`
        consumer_phone,
        created_at,
        total_amount,
        order_items!inner(
          merchant_account_id
        )
      `).eq("order_items.merchant_account_id",t);if(r)return console.error("Error fetching customer analytics:",r),{totalCustomers:0,newCustomers:0,returningCustomers:0,customerLifetimeValue:0,repeatCustomerRate:0,averageOrdersPerCustomer:0};const n=new Map;e?.forEach(p=>{const y=p.consumer_phone,x=n.get(y);x?(x.orderCount+=1,x.totalSpent+=Number(p.total_amount),new Date(p.created_at)<new Date(x.firstOrder)&&(x.firstOrder=p.created_at)):n.set(y,{orderCount:1,totalSpent:Number(p.total_amount),firstOrder:p.created_at})});const a=n.size,o=new Date(new Date().getFullYear(),new Date().getMonth(),1);let c=0,i=0,m=0,d=0;n.forEach(p=>{m+=p.totalSpent,d+=p.orderCount,new Date(p.firstOrder)>=o&&(c+=1),p.orderCount>1&&(i+=1)});const u=a>0?m/a:0,l=a>0?i/a*100:0,h=a>0?d/a:0;return{totalCustomers:a,newCustomers:c,returningCustomers:i,customerLifetimeValue:u,repeatCustomerRate:l,averageOrdersPerCustomer:h}}catch(e){return console.error("Error in getCustomerAnalytics:",e),{totalCustomers:0,newCustomers:0,returningCustomers:0,customerLifetimeValue:0,repeatCustomerRate:0,averageOrdersPerCustomer:0}}}async function kt(t){try{const{data:e,error:r}=await _.from("products").select("category").eq("user_id",t);r&&console.error("Error fetching store performance:",r);const n=new Map;e?.forEach(c=>{const i=n.get(c.category);i?(i.views+=Math.floor(Math.random()*100),i.sales+=Math.floor(Math.random()*10)):n.set(c.category,{views:Math.floor(Math.random()*100),sales:Math.floor(Math.random()*10)})});const a=Array.from(n.entries()).map(([c,i])=>({category:c,...i})).sort((c,i)=>i.views-c.views).slice(0,5),o=[{term:"tyres",count:45},{term:"brake pads",count:32},{term:"oil filter",count:28},{term:"spark plugs",count:21},{term:"battery",count:18}];return{productViews:Math.floor(Math.random()*1e3)+500,conversionRate:2.5,popularCategories:a,searchTerms:o}}catch(e){return console.error("Error in getStorePerformance:",e),{productViews:0,conversionRate:0,popularCategories:[],searchTerms:[]}}}async function Ot(t){try{const{data:e,error:r}=await _.from("products").select("category, status, stock_quantity, price").eq("user_id",t);if(r)return console.error("Error fetching inventory status:",r),{totalProducts:0,inStock:0,lowStock:0,outOfStock:0,totalValue:0,categories:[]};const n=e?.length||0,a=e?.filter(u=>u.status==="active"&&u.stock_quantity>10).length||0,o=e?.filter(u=>u.stock_quantity>0&&u.stock_quantity<=10).length||0,c=e?.filter(u=>u.status==="out_of_stock"||u.stock_quantity===0).length||0,i=e?.reduce((u,l)=>u+Number(l.price)*(l.stock_quantity||0),0)||0,m=new Map;e?.forEach(u=>{const l=m.get(u.category),h=Number(u.price)*(u.stock_quantity||0);l?(l.productCount+=1,l.stockValue+=h):m.set(u.category,{productCount:1,stockValue:h})});const d=Array.from(m.entries()).map(([u,l])=>({category:u,...l})).sort((u,l)=>l.stockValue-u.stockValue);return{totalProducts:n,inStock:a,lowStock:o,outOfStock:c,totalValue:i,categories:d}}catch(e){return console.error("Error in getInventoryStatus:",e),{totalProducts:0,inStock:0,lowStock:0,outOfStock:0,totalValue:0,categories:[]}}}function Nt(){const{userRole:t}=S(),{toast:e}=ee(),r=ye();return{...we({queryKey:["supplier-dashboard"],queryFn:wt,enabled:t==="supplier",staleTime:2*60*1e3,refetchInterval:5*60*1e3,retry:3,retryDelay:o=>Math.min(1e3*2**o,3e4),onError:o=>{console.error("Error fetching supplier dashboard:",o),e({title:"Error Loading Supplier Dashboard",description:"Failed to load supplier dashboard data. Please try again.",variant:"destructive"})}}),refresh:()=>{r.invalidateQueries({queryKey:["supplier-dashboard"]}),r.invalidateQueries({queryKey:["dashboard-metrics"]})}}}function St(){const{userRole:t}=S(),{toast:e}=ee(),r=ye();return{...we({queryKey:["merchant-dashboard"],queryFn:xt,enabled:t==="merchant",staleTime:2*60*1e3,refetchInterval:5*60*1e3,retry:3,retryDelay:o=>Math.min(1e3*2**o,3e4),onError:o=>{console.error("Error fetching merchant dashboard:",o),e({title:"Error Loading Merchant Dashboard",description:"Failed to load merchant dashboard data. Please try again.",variant:"destructive"})}}),refresh:()=>{r.invalidateQueries({queryKey:["merchant-dashboard"]}),r.invalidateQueries({queryKey:["dashboard-metrics"]})}}}function Ct(){const{userRole:t}=S(),{isAuthenticated:e}=Qe(),r=Nt(),n=St();return e?t==="supplier"?r:t==="merchant"?n:{data:null,isLoading:!1,isError:!0,error:new Error("Invalid user role"),refresh:()=>{}}:{data:null,isLoading:!1,isError:!0,error:new Error("User not authenticated"),refresh:()=>{}}}const C=({icon:t,label:e,href:r,badge:n,badgeVariant:a="default",children:o,collapsed:c=!1,onClick:i})=>{const m=ae(),[d,u]=v.useState(!1),l=m.pathname===r||r!=="/"&&m.pathname.startsWith(r),h=!!o;v.useEffect(()=>{h&&!d&&m.pathname.startsWith(r)&&u(!0)},[m.pathname,h,r,d]);const p=x=>{h&&(x.preventDefault(),u(!d))},y=s.jsxs("div",{className:w("flex items-center justify-between w-full px-3 py-2.5 rounded-md cursor-pointer","transition-colors duration-200 ease-in-out group",l?"bg-primary/10 text-primary font-medium":"text-foreground/70 hover:bg-primary/5 hover:text-primary"),onClick:p,children:[s.jsxs("div",{className:"flex items-center gap-3 min-w-0",children:[s.jsx("div",{className:w("flex items-center justify-center w-5 h-5",l?"text-primary":"text-foreground/60 group-hover:text-primary"),children:s.jsx(t,{size:18})}),!c&&s.jsx("span",{className:"truncate",children:e})]}),!c&&s.jsxs("div",{className:"flex items-center gap-2",children:[n&&s.jsx(I,{variant:a,className:"h-5 px-1.5 text-xs font-medium",children:n}),h&&s.jsx("div",{className:"text-foreground/50",children:d?s.jsx(Ke,{size:14}):s.jsx(xe,{size:14})})]})]});return s.jsx("div",{className:"w-full",children:h?s.jsxs("div",{className:"w-full",children:[c?s.jsx(H,{delayDuration:0,children:s.jsxs(Q,{children:[s.jsx(V,{asChild:!0,children:s.jsx(L,{to:r,className:"block",onClick:i,children:y})}),s.jsxs(z,{side:"right",className:"flex items-center gap-2",children:[e,n&&s.jsx(I,{variant:a,className:"h-5 px-1.5 text-xs font-medium",children:n})]})]})}):y,!c&&d&&s.jsx("div",{className:"mt-1 ml-4 pl-3 border-l border-border/40 space-y-1",children:o})]}):c?s.jsx(H,{delayDuration:0,children:s.jsxs(Q,{children:[s.jsx(V,{asChild:!0,children:s.jsx(L,{to:r,className:"block",onClick:i,children:y})}),s.jsxs(z,{side:"right",className:"flex items-center gap-2",children:[e,n&&s.jsx(I,{variant:a,className:"h-5 px-1.5 text-xs font-medium",children:n})]})]})}):s.jsx(L,{to:r,className:"block",onClick:i,children:y})})},ce=({label:t,href:e,badge:r,badgeVariant:n="default"})=>{const o=ae().pathname===e;return s.jsxs(L,{to:e,className:w("flex items-center justify-between px-2 py-1.5 rounded text-sm","transition-colors duration-200 ease-in-out",o?"text-primary font-medium bg-primary/5":"text-foreground/60 hover:text-primary hover:bg-primary/5"),children:[s.jsx("span",{className:"truncate",children:t}),r&&s.jsx(I,{variant:n,className:"h-5 px-1.5 text-xs font-medium",children:r})]})};function Pt({onToggle:t}){const{t:e,i18n:r}=E(),[n,a]=v.useState(!1),o=v.useRef(null),c=r.language==="ar",i=ne(),[m,d]=v.useState(!1),{data:u}=Ct(),l=()=>{const y=!n;a(y),t?.(!y)},h=()=>{d(!m)};v.useEffect(()=>{const y=x=>{i&&m&&o.current&&!o.current.contains(x.target)&&d(!1)};return document.addEventListener("mousedown",y),()=>{document.removeEventListener("mousedown",y)}},[i,m]);const p=ae();return v.useEffect(()=>{i&&m&&d(!1)},[p,i,m]),s.jsxs(s.Fragment,{children:[i&&s.jsx("button",{onClick:h,className:"fixed top-4 left-4 z-50 p-2 rounded-md bg-background border border-border shadow-sm","aria-label":"Toggle menu",children:s.jsx(Ve,{size:20})}),i&&s.jsx("div",{className:w("fixed inset-0 bg-black/50 backdrop-blur-sm z-40 transition-opacity duration-300",m?"opacity-100":"opacity-0 pointer-events-none"),onClick:()=>d(!1)}),s.jsxs("aside",{ref:o,className:w("fixed inset-y-0 z-50 flex flex-col bg-background border-r border-border","transition-all duration-300 ease-in-out",c?"right-0":"left-0",i?w("w-[280px]",m?"translate-x-0":c?"translate-x-full":"-translate-x-full"):w(n?"w-[70px]":"w-[280px]")),children:[s.jsxs("div",{className:"flex h-20 items-center justify-between px-4 border-b border-border bg-gradient-to-r from-primary/5 to-transparent",children:[s.jsxs("div",{className:w("flex items-center gap-3",n&&!i?"justify-center w-full":"overflow-hidden"),children:[s.jsx("div",{className:"flex-shrink-0 bg-gradient-to-br from-electric-orange/10 to-primary/5 p-1.5 rounded-md shadow-sm hover:shadow-md transition-all duration-300 hover:scale-105",children:s.jsx("img",{src:"/favicon-new.png",alt:"AROUZ MARKET",className:w("transition-all duration-300",n&&!i?"w-8 h-8":"w-9 h-9")})}),(!n||i)&&s.jsxs("div",{className:"flex flex-col",children:[s.jsx("span",{className:"text-xl font-bold tracking-tight text-primary",children:"AROUZ MARKET"}),s.jsx("span",{className:"text-xs text-muted-foreground",children:"Auto Parts Ecosystem"})]})]}),i?s.jsx("button",{onClick:()=>d(!1),className:"p-1 rounded-md hover:bg-muted","aria-label":"Close menu",children:s.jsx(ze,{size:18})}):s.jsx("button",{onClick:l,className:"p-1 rounded-md hover:bg-muted hidden md:flex","aria-label":n?"Expand sidebar":"Collapse sidebar",children:s.jsx(Ue,{size:18,className:w("transition-transform duration-300",n?c?"rotate-180":"rotate-0":c?"rotate-0":"rotate-180")})})]}),s.jsx("div",{className:"flex-1 overflow-y-auto py-4 px-3",children:s.jsxs("nav",{className:"space-y-1",children:[s.jsx(C,{icon:mt,label:e("navigation.dashboard"),href:"/app/dashboard",collapsed:n&&!i}),s.jsxs(C,{icon:Xe,label:e("navigation.products"),href:"/app/products",collapsed:n&&!i,children:[s.jsx(ce,{label:e("navigation.allProducts"),href:"/app/products"}),s.jsx(ce,{label:e("navigation.productsTable"),href:"/app/products-table"})]}),s.jsx(C,{icon:Ge,label:e("navigation.orders"),href:"/app/orders",badge:u?.totalOrders?.toString()||"0",badgeVariant:"secondary",collapsed:n&&!i}),s.jsx(C,{icon:Je,label:e("navigation.shipments"),href:"/app/shipments",collapsed:n&&!i}),s.jsx(C,{icon:be,label:e("navigation.settings"),href:"/app/settings",collapsed:n&&!i})]})}),s.jsx("div",{className:"border-t border-border p-3",children:n&&!i?s.jsx(C,{icon:X,label:e("navigation.logout"),href:"#",onClick:y=>{y.preventDefault(),document.getElementById("sidebar-logout-button")?.click()},collapsed:!0}):s.jsx("div",{id:"sidebar-logout-button",children:s.jsx(ft,{})})})]})]})}function Et(){const{userRole:t,isSupplier:e,isMerchant:r}=S(),{t:n}=E();return!e()&&!r()?null:e()?s.jsx(H,{children:s.jsxs(Q,{children:[s.jsx(V,{asChild:!0,children:s.jsxs(B,{variant:"outline",className:w("flex items-center gap-2","border-[#fa7b00]/20 bg-[#ffd9a3]/20","hover:bg-[#ffd9a3]/40 hover:text-[#fa7b00]","text-[#fa7b00]"),style:{unicodeBidi:"isolate"},children:[s.jsx(te,{className:"h-4 w-4 text-[#fa7b00] flex-shrink-0"}),s.jsx("span",{className:"hidden sm:inline-block font-medium",style:{unicodeBidi:"isolate"},children:n("roles.manufacturer")})]})}),s.jsx(z,{side:"bottom",children:s.jsx("p",{children:n("roles.manufacturerDescription")})})]})}):r()?s.jsx(H,{children:s.jsxs(Q,{children:[s.jsx(V,{asChild:!0,children:s.jsxs(B,{variant:"outline",className:w("flex items-center gap-2","border-[#071c44]/20 bg-[#071c44]/10","hover:bg-[#071c44]/20 hover:text-[#071c44]","text-[#071c44]"),style:{unicodeBidi:"isolate"},children:[s.jsx(re,{className:"h-4 w-4 text-[#071c44] flex-shrink-0"}),s.jsx("span",{className:"hidden sm:inline-block font-medium",style:{unicodeBidi:"isolate"},children:n("roles.merchant")})]})}),s.jsx(z,{side:"bottom",children:s.jsx("p",{children:n("roles.merchantDescription")})})]})}):null}function D(t){const e=Object.prototype.toString.call(t);return t instanceof Date||typeof t=="object"&&e==="[object Date]"?new t.constructor(+t):typeof t=="number"||e==="[object Number]"||typeof t=="string"||e==="[object String]"?new Date(t):new Date(NaN)}function N(t,e){return t instanceof Date?new t.constructor(e):new Date(e)}const je=6048e5,Tt=864e5;let qt={};function G(){return qt}function A(t,e){const r=G(),n=e?.weekStartsOn??e?.locale?.options?.weekStartsOn??r.weekStartsOn??r.locale?.options?.weekStartsOn??0,a=D(t),o=a.getDay(),c=(o<n?7:0)+o-n;return a.setDate(a.getDate()-c),a.setHours(0,0,0,0),a}function U(t){return A(t,{weekStartsOn:1})}function ke(t){const e=D(t),r=e.getFullYear(),n=N(t,0);n.setFullYear(r+1,0,4),n.setHours(0,0,0,0);const a=U(n),o=N(t,0);o.setFullYear(r,0,4),o.setHours(0,0,0,0);const c=U(o);return e.getTime()>=a.getTime()?r+1:e.getTime()>=c.getTime()?r:r-1}function ue(t){const e=D(t);return e.setHours(0,0,0,0),e}function le(t){const e=D(t),r=new Date(Date.UTC(e.getFullYear(),e.getMonth(),e.getDate(),e.getHours(),e.getMinutes(),e.getSeconds(),e.getMilliseconds()));return r.setUTCFullYear(e.getFullYear()),+t-+r}function Wt(t,e){const r=ue(t),n=ue(e),a=+r-le(r),o=+n-le(n);return Math.round((a-o)/Tt)}function Ft(t){const e=ke(t),r=N(t,0);return r.setFullYear(e,0,4),r.setHours(0,0,0,0),U(r)}function Rt(t){return t instanceof Date||typeof t=="object"&&Object.prototype.toString.call(t)==="[object Date]"}function At(t){if(!Rt(t)&&typeof t!="number")return!1;const e=D(t);return!isNaN(Number(e))}function Yt(t){const e=D(t),r=N(t,0);return r.setFullYear(e.getFullYear(),0,1),r.setHours(0,0,0,0),r}const It={lessThanXSeconds:{one:"less than a second",other:"less than {{count}} seconds"},xSeconds:{one:"1 second",other:"{{count}} seconds"},halfAMinute:"half a minute",lessThanXMinutes:{one:"less than a minute",other:"less than {{count}} minutes"},xMinutes:{one:"1 minute",other:"{{count}} minutes"},aboutXHours:{one:"about 1 hour",other:"about {{count}} hours"},xHours:{one:"1 hour",other:"{{count}} hours"},xDays:{one:"1 day",other:"{{count}} days"},aboutXWeeks:{one:"about 1 week",other:"about {{count}} weeks"},xWeeks:{one:"1 week",other:"{{count}} weeks"},aboutXMonths:{one:"about 1 month",other:"about {{count}} months"},xMonths:{one:"1 month",other:"{{count}} months"},aboutXYears:{one:"about 1 year",other:"about {{count}} years"},xYears:{one:"1 year",other:"{{count}} years"},overXYears:{one:"over 1 year",other:"over {{count}} years"},almostXYears:{one:"almost 1 year",other:"almost {{count}} years"}},Lt=(t,e,r)=>{let n;const a=It[t];return typeof a=="string"?n=a:e===1?n=a.one:n=a.other.replace("{{count}}",e.toString()),r?.addSuffix?r.comparison&&r.comparison>0?"in "+n:n+" ago":n};function Z(t){return(e={})=>{const r=e.width?String(e.width):t.defaultWidth;return t.formats[r]||t.formats[t.defaultWidth]}}const Bt={full:"EEEE, MMMM do, y",long:"MMMM do, y",medium:"MMM d, y",short:"MM/dd/yyyy"},Ht={full:"h:mm:ss a zzzz",long:"h:mm:ss a z",medium:"h:mm:ss a",short:"h:mm a"},Qt={full:"{{date}} 'at' {{time}}",long:"{{date}} 'at' {{time}}",medium:"{{date}}, {{time}}",short:"{{date}}, {{time}}"},Vt={date:Z({formats:Bt,defaultWidth:"full"}),time:Z({formats:Ht,defaultWidth:"full"}),dateTime:Z({formats:Qt,defaultWidth:"full"})},zt={lastWeek:"'last' eeee 'at' p",yesterday:"'yesterday at' p",today:"'today at' p",tomorrow:"'tomorrow at' p",nextWeek:"eeee 'at' p",other:"P"},Ut=(t,e,r,n)=>zt[t];function F(t){return(e,r)=>{const n=r?.context?String(r.context):"standalone";let a;if(n==="formatting"&&t.formattingValues){const c=t.defaultFormattingWidth||t.defaultWidth,i=r?.width?String(r.width):c;a=t.formattingValues[i]||t.formattingValues[c]}else{const c=t.defaultWidth,i=r?.width?String(r.width):t.defaultWidth;a=t.values[i]||t.values[c]}const o=t.argumentCallback?t.argumentCallback(e):e;return a[o]}}const Xt={narrow:["B","A"],abbreviated:["BC","AD"],wide:["Before Christ","Anno Domini"]},Gt={narrow:["1","2","3","4"],abbreviated:["Q1","Q2","Q3","Q4"],wide:["1st quarter","2nd quarter","3rd quarter","4th quarter"]},Jt={narrow:["J","F","M","A","M","J","J","A","S","O","N","D"],abbreviated:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],wide:["January","February","March","April","May","June","July","August","September","October","November","December"]},Kt={narrow:["S","M","T","W","T","F","S"],short:["Su","Mo","Tu","We","Th","Fr","Sa"],abbreviated:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],wide:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"]},$t={narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"}},Zt={narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"}},er=(t,e)=>{const r=Number(t),n=r%100;if(n>20||n<10)switch(n%10){case 1:return r+"st";case 2:return r+"nd";case 3:return r+"rd"}return r+"th"},tr={ordinalNumber:er,era:F({values:Xt,defaultWidth:"wide"}),quarter:F({values:Gt,defaultWidth:"wide",argumentCallback:t=>t-1}),month:F({values:Jt,defaultWidth:"wide"}),day:F({values:Kt,defaultWidth:"wide"}),dayPeriod:F({values:$t,defaultWidth:"wide",formattingValues:Zt,defaultFormattingWidth:"wide"})};function R(t){return(e,r={})=>{const n=r.width,a=n&&t.matchPatterns[n]||t.matchPatterns[t.defaultMatchWidth],o=e.match(a);if(!o)return null;const c=o[0],i=n&&t.parsePatterns[n]||t.parsePatterns[t.defaultParseWidth],m=Array.isArray(i)?nr(i,l=>l.test(c)):rr(i,l=>l.test(c));let d;d=t.valueCallback?t.valueCallback(m):m,d=r.valueCallback?r.valueCallback(d):d;const u=e.slice(c.length);return{value:d,rest:u}}}function rr(t,e){for(const r in t)if(Object.prototype.hasOwnProperty.call(t,r)&&e(t[r]))return r}function nr(t,e){for(let r=0;r<t.length;r++)if(e(t[r]))return r}function ar(t){return(e,r={})=>{const n=e.match(t.matchPattern);if(!n)return null;const a=n[0],o=e.match(t.parsePattern);if(!o)return null;let c=t.valueCallback?t.valueCallback(o[0]):o[0];c=r.valueCallback?r.valueCallback(c):c;const i=e.slice(a.length);return{value:c,rest:i}}}const sr=/^(\d+)(th|st|nd|rd)?/i,or=/\d+/i,ir={narrow:/^(b|a)/i,abbreviated:/^(b\.?\s?c\.?|b\.?\s?c\.?\s?e\.?|a\.?\s?d\.?|c\.?\s?e\.?)/i,wide:/^(before christ|before common era|anno domini|common era)/i},cr={any:[/^b/i,/^(a|c)/i]},ur={narrow:/^[1234]/i,abbreviated:/^q[1234]/i,wide:/^[1234](th|st|nd|rd)? quarter/i},lr={any:[/1/i,/2/i,/3/i,/4/i]},dr={narrow:/^[jfmasond]/i,abbreviated:/^(jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec)/i,wide:/^(january|february|march|april|may|june|july|august|september|october|november|december)/i},mr={narrow:[/^j/i,/^f/i,/^m/i,/^a/i,/^m/i,/^j/i,/^j/i,/^a/i,/^s/i,/^o/i,/^n/i,/^d/i],any:[/^ja/i,/^f/i,/^mar/i,/^ap/i,/^may/i,/^jun/i,/^jul/i,/^au/i,/^s/i,/^o/i,/^n/i,/^d/i]},hr={narrow:/^[smtwf]/i,short:/^(su|mo|tu|we|th|fr|sa)/i,abbreviated:/^(sun|mon|tue|wed|thu|fri|sat)/i,wide:/^(sunday|monday|tuesday|wednesday|thursday|friday|saturday)/i},fr={narrow:[/^s/i,/^m/i,/^t/i,/^w/i,/^t/i,/^f/i,/^s/i],any:[/^su/i,/^m/i,/^tu/i,/^w/i,/^th/i,/^f/i,/^sa/i]},gr={narrow:/^(a|p|mi|n|(in the|at) (morning|afternoon|evening|night))/i,any:/^([ap]\.?\s?m\.?|midnight|noon|(in the|at) (morning|afternoon|evening|night))/i},pr={any:{am:/^a/i,pm:/^p/i,midnight:/^mi/i,noon:/^no/i,morning:/morning/i,afternoon:/afternoon/i,evening:/evening/i,night:/night/i}},yr={ordinalNumber:ar({matchPattern:sr,parsePattern:or,valueCallback:t=>parseInt(t,10)}),era:R({matchPatterns:ir,defaultMatchWidth:"wide",parsePatterns:cr,defaultParseWidth:"any"}),quarter:R({matchPatterns:ur,defaultMatchWidth:"wide",parsePatterns:lr,defaultParseWidth:"any",valueCallback:t=>t+1}),month:R({matchPatterns:dr,defaultMatchWidth:"wide",parsePatterns:mr,defaultParseWidth:"any"}),day:R({matchPatterns:hr,defaultMatchWidth:"wide",parsePatterns:fr,defaultParseWidth:"any"}),dayPeriod:R({matchPatterns:gr,defaultMatchWidth:"any",parsePatterns:pr,defaultParseWidth:"any"})},wr={code:"en-US",formatDistance:Lt,formatLong:Vt,formatRelative:Ut,localize:tr,match:yr,options:{weekStartsOn:0,firstWeekContainsDate:1}};function xr(t){const e=D(t);return Wt(e,Yt(e))+1}function br(t){const e=D(t),r=+U(e)-+Ft(e);return Math.round(r/je)+1}function Oe(t,e){const r=D(t),n=r.getFullYear(),a=G(),o=e?.firstWeekContainsDate??e?.locale?.options?.firstWeekContainsDate??a.firstWeekContainsDate??a.locale?.options?.firstWeekContainsDate??1,c=N(t,0);c.setFullYear(n+1,0,o),c.setHours(0,0,0,0);const i=A(c,e),m=N(t,0);m.setFullYear(n,0,o),m.setHours(0,0,0,0);const d=A(m,e);return r.getTime()>=i.getTime()?n+1:r.getTime()>=d.getTime()?n:n-1}function vr(t,e){const r=G(),n=e?.firstWeekContainsDate??e?.locale?.options?.firstWeekContainsDate??r.firstWeekContainsDate??r.locale?.options?.firstWeekContainsDate??1,a=Oe(t,e),o=N(t,0);return o.setFullYear(a,0,n),o.setHours(0,0,0,0),A(o,e)}function _r(t,e){const r=D(t),n=+A(r,e)-+vr(r,e);return Math.round(n/je)+1}function g(t,e){const r=t<0?"-":"",n=Math.abs(t).toString().padStart(e,"0");return r+n}const j={y(t,e){const r=t.getFullYear(),n=r>0?r:1-r;return g(e==="yy"?n%100:n,e.length)},M(t,e){const r=t.getMonth();return e==="M"?String(r+1):g(r+1,2)},d(t,e){return g(t.getDate(),e.length)},a(t,e){const r=t.getHours()/12>=1?"pm":"am";switch(e){case"a":case"aa":return r.toUpperCase();case"aaa":return r;case"aaaaa":return r[0];case"aaaa":default:return r==="am"?"a.m.":"p.m."}},h(t,e){return g(t.getHours()%12||12,e.length)},H(t,e){return g(t.getHours(),e.length)},m(t,e){return g(t.getMinutes(),e.length)},s(t,e){return g(t.getSeconds(),e.length)},S(t,e){const r=e.length,n=t.getMilliseconds(),a=Math.trunc(n*Math.pow(10,r-3));return g(a,e.length)}},P={am:"am",pm:"pm",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},de={G:function(t,e,r){const n=t.getFullYear()>0?1:0;switch(e){case"G":case"GG":case"GGG":return r.era(n,{width:"abbreviated"});case"GGGGG":return r.era(n,{width:"narrow"});case"GGGG":default:return r.era(n,{width:"wide"})}},y:function(t,e,r){if(e==="yo"){const n=t.getFullYear(),a=n>0?n:1-n;return r.ordinalNumber(a,{unit:"year"})}return j.y(t,e)},Y:function(t,e,r,n){const a=Oe(t,n),o=a>0?a:1-a;if(e==="YY"){const c=o%100;return g(c,2)}return e==="Yo"?r.ordinalNumber(o,{unit:"year"}):g(o,e.length)},R:function(t,e){const r=ke(t);return g(r,e.length)},u:function(t,e){const r=t.getFullYear();return g(r,e.length)},Q:function(t,e,r){const n=Math.ceil((t.getMonth()+1)/3);switch(e){case"Q":return String(n);case"QQ":return g(n,2);case"Qo":return r.ordinalNumber(n,{unit:"quarter"});case"QQQ":return r.quarter(n,{width:"abbreviated",context:"formatting"});case"QQQQQ":return r.quarter(n,{width:"narrow",context:"formatting"});case"QQQQ":default:return r.quarter(n,{width:"wide",context:"formatting"})}},q:function(t,e,r){const n=Math.ceil((t.getMonth()+1)/3);switch(e){case"q":return String(n);case"qq":return g(n,2);case"qo":return r.ordinalNumber(n,{unit:"quarter"});case"qqq":return r.quarter(n,{width:"abbreviated",context:"standalone"});case"qqqqq":return r.quarter(n,{width:"narrow",context:"standalone"});case"qqqq":default:return r.quarter(n,{width:"wide",context:"standalone"})}},M:function(t,e,r){const n=t.getMonth();switch(e){case"M":case"MM":return j.M(t,e);case"Mo":return r.ordinalNumber(n+1,{unit:"month"});case"MMM":return r.month(n,{width:"abbreviated",context:"formatting"});case"MMMMM":return r.month(n,{width:"narrow",context:"formatting"});case"MMMM":default:return r.month(n,{width:"wide",context:"formatting"})}},L:function(t,e,r){const n=t.getMonth();switch(e){case"L":return String(n+1);case"LL":return g(n+1,2);case"Lo":return r.ordinalNumber(n+1,{unit:"month"});case"LLL":return r.month(n,{width:"abbreviated",context:"standalone"});case"LLLLL":return r.month(n,{width:"narrow",context:"standalone"});case"LLLL":default:return r.month(n,{width:"wide",context:"standalone"})}},w:function(t,e,r,n){const a=_r(t,n);return e==="wo"?r.ordinalNumber(a,{unit:"week"}):g(a,e.length)},I:function(t,e,r){const n=br(t);return e==="Io"?r.ordinalNumber(n,{unit:"week"}):g(n,e.length)},d:function(t,e,r){return e==="do"?r.ordinalNumber(t.getDate(),{unit:"date"}):j.d(t,e)},D:function(t,e,r){const n=xr(t);return e==="Do"?r.ordinalNumber(n,{unit:"dayOfYear"}):g(n,e.length)},E:function(t,e,r){const n=t.getDay();switch(e){case"E":case"EE":case"EEE":return r.day(n,{width:"abbreviated",context:"formatting"});case"EEEEE":return r.day(n,{width:"narrow",context:"formatting"});case"EEEEEE":return r.day(n,{width:"short",context:"formatting"});case"EEEE":default:return r.day(n,{width:"wide",context:"formatting"})}},e:function(t,e,r,n){const a=t.getDay(),o=(a-n.weekStartsOn+8)%7||7;switch(e){case"e":return String(o);case"ee":return g(o,2);case"eo":return r.ordinalNumber(o,{unit:"day"});case"eee":return r.day(a,{width:"abbreviated",context:"formatting"});case"eeeee":return r.day(a,{width:"narrow",context:"formatting"});case"eeeeee":return r.day(a,{width:"short",context:"formatting"});case"eeee":default:return r.day(a,{width:"wide",context:"formatting"})}},c:function(t,e,r,n){const a=t.getDay(),o=(a-n.weekStartsOn+8)%7||7;switch(e){case"c":return String(o);case"cc":return g(o,e.length);case"co":return r.ordinalNumber(o,{unit:"day"});case"ccc":return r.day(a,{width:"abbreviated",context:"standalone"});case"ccccc":return r.day(a,{width:"narrow",context:"standalone"});case"cccccc":return r.day(a,{width:"short",context:"standalone"});case"cccc":default:return r.day(a,{width:"wide",context:"standalone"})}},i:function(t,e,r){const n=t.getDay(),a=n===0?7:n;switch(e){case"i":return String(a);case"ii":return g(a,e.length);case"io":return r.ordinalNumber(a,{unit:"day"});case"iii":return r.day(n,{width:"abbreviated",context:"formatting"});case"iiiii":return r.day(n,{width:"narrow",context:"formatting"});case"iiiiii":return r.day(n,{width:"short",context:"formatting"});case"iiii":default:return r.day(n,{width:"wide",context:"formatting"})}},a:function(t,e,r){const a=t.getHours()/12>=1?"pm":"am";switch(e){case"a":case"aa":return r.dayPeriod(a,{width:"abbreviated",context:"formatting"});case"aaa":return r.dayPeriod(a,{width:"abbreviated",context:"formatting"}).toLowerCase();case"aaaaa":return r.dayPeriod(a,{width:"narrow",context:"formatting"});case"aaaa":default:return r.dayPeriod(a,{width:"wide",context:"formatting"})}},b:function(t,e,r){const n=t.getHours();let a;switch(n===12?a=P.noon:n===0?a=P.midnight:a=n/12>=1?"pm":"am",e){case"b":case"bb":return r.dayPeriod(a,{width:"abbreviated",context:"formatting"});case"bbb":return r.dayPeriod(a,{width:"abbreviated",context:"formatting"}).toLowerCase();case"bbbbb":return r.dayPeriod(a,{width:"narrow",context:"formatting"});case"bbbb":default:return r.dayPeriod(a,{width:"wide",context:"formatting"})}},B:function(t,e,r){const n=t.getHours();let a;switch(n>=17?a=P.evening:n>=12?a=P.afternoon:n>=4?a=P.morning:a=P.night,e){case"B":case"BB":case"BBB":return r.dayPeriod(a,{width:"abbreviated",context:"formatting"});case"BBBBB":return r.dayPeriod(a,{width:"narrow",context:"formatting"});case"BBBB":default:return r.dayPeriod(a,{width:"wide",context:"formatting"})}},h:function(t,e,r){if(e==="ho"){let n=t.getHours()%12;return n===0&&(n=12),r.ordinalNumber(n,{unit:"hour"})}return j.h(t,e)},H:function(t,e,r){return e==="Ho"?r.ordinalNumber(t.getHours(),{unit:"hour"}):j.H(t,e)},K:function(t,e,r){const n=t.getHours()%12;return e==="Ko"?r.ordinalNumber(n,{unit:"hour"}):g(n,e.length)},k:function(t,e,r){let n=t.getHours();return n===0&&(n=24),e==="ko"?r.ordinalNumber(n,{unit:"hour"}):g(n,e.length)},m:function(t,e,r){return e==="mo"?r.ordinalNumber(t.getMinutes(),{unit:"minute"}):j.m(t,e)},s:function(t,e,r){return e==="so"?r.ordinalNumber(t.getSeconds(),{unit:"second"}):j.s(t,e)},S:function(t,e){return j.S(t,e)},X:function(t,e,r){const n=t.getTimezoneOffset();if(n===0)return"Z";switch(e){case"X":return he(n);case"XXXX":case"XX":return O(n);case"XXXXX":case"XXX":default:return O(n,":")}},x:function(t,e,r){const n=t.getTimezoneOffset();switch(e){case"x":return he(n);case"xxxx":case"xx":return O(n);case"xxxxx":case"xxx":default:return O(n,":")}},O:function(t,e,r){const n=t.getTimezoneOffset();switch(e){case"O":case"OO":case"OOO":return"GMT"+me(n,":");case"OOOO":default:return"GMT"+O(n,":")}},z:function(t,e,r){const n=t.getTimezoneOffset();switch(e){case"z":case"zz":case"zzz":return"GMT"+me(n,":");case"zzzz":default:return"GMT"+O(n,":")}},t:function(t,e,r){const n=Math.trunc(t.getTime()/1e3);return g(n,e.length)},T:function(t,e,r){const n=t.getTime();return g(n,e.length)}};function me(t,e=""){const r=t>0?"-":"+",n=Math.abs(t),a=Math.trunc(n/60),o=n%60;return o===0?r+String(a):r+String(a)+e+g(o,2)}function he(t,e){return t%60===0?(t>0?"-":"+")+g(Math.abs(t)/60,2):O(t,e)}function O(t,e=""){const r=t>0?"-":"+",n=Math.abs(t),a=g(Math.trunc(n/60),2),o=g(n%60,2);return r+a+e+o}const fe=(t,e)=>{switch(t){case"P":return e.date({width:"short"});case"PP":return e.date({width:"medium"});case"PPP":return e.date({width:"long"});case"PPPP":default:return e.date({width:"full"})}},Ne=(t,e)=>{switch(t){case"p":return e.time({width:"short"});case"pp":return e.time({width:"medium"});case"ppp":return e.time({width:"long"});case"pppp":default:return e.time({width:"full"})}},Mr=(t,e)=>{const r=t.match(/(P+)(p+)?/)||[],n=r[1],a=r[2];if(!a)return fe(t,e);let o;switch(n){case"P":o=e.dateTime({width:"short"});break;case"PP":o=e.dateTime({width:"medium"});break;case"PPP":o=e.dateTime({width:"long"});break;case"PPPP":default:o=e.dateTime({width:"full"});break}return o.replace("{{date}}",fe(n,e)).replace("{{time}}",Ne(a,e))},Dr={p:Ne,P:Mr},jr=/^D+$/,kr=/^Y+$/,Or=["D","DD","YY","YYYY"];function Nr(t){return jr.test(t)}function Sr(t){return kr.test(t)}function Cr(t,e,r){const n=Pr(t,e,r);if(console.warn(n),Or.includes(t))throw new RangeError(n)}function Pr(t,e,r){const n=t[0]==="Y"?"years":"days of the month";return`Use \`${t.toLowerCase()}\` instead of \`${t}\` (in \`${e}\`) for formatting ${n} to the input \`${r}\`; see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md`}const Er=/[yYQqMLwIdDecihHKkms]o|(\w)\1*|''|'(''|[^'])+('|$)|./g,Tr=/P+p+|P+|p+|''|'(''|[^'])+('|$)|./g,qr=/^'([^]*?)'?$/,Wr=/''/g,Fr=/[a-zA-Z]/;function Rr(t,e,r){const n=G(),a=n.locale??wr,o=n.firstWeekContainsDate??n.locale?.options?.firstWeekContainsDate??1,c=n.weekStartsOn??n.locale?.options?.weekStartsOn??0,i=D(t);if(!At(i))throw new RangeError("Invalid time value");let m=e.match(Tr).map(u=>{const l=u[0];if(l==="p"||l==="P"){const h=Dr[l];return h(u,a.formatLong)}return u}).join("").match(Er).map(u=>{if(u==="''")return{isToken:!1,value:"'"};const l=u[0];if(l==="'")return{isToken:!1,value:Ar(u)};if(de[l])return{isToken:!0,value:u};if(l.match(Fr))throw new RangeError("Format string contains an unescaped latin alphabet character `"+l+"`");return{isToken:!1,value:u}});a.localize.preprocessor&&(m=a.localize.preprocessor(i,m));const d={firstWeekContainsDate:o,weekStartsOn:c,locale:a};return m.map(u=>{if(!u.isToken)return u.value;const l=u.value;(Sr(l)||Nr(l))&&Cr(l,e,String(t));const h=de[l[0]];return h(i,l,a.localize,d)}).join("")}function Ar(t){const e=t.match(qr);return e?e[1].replace(Wr,"'"):t}function Yr(){const{t,i18n:e}=E(),{userRole:r,isSupplier:n,isMerchant:a,supplierRole:o,merchantRole:c}=S(),i=e.language==="ar";ne();const[m,d]=v.useState(!1),[u,l]=v.useState(void 0),[h,p]=v.useState(null),[y,x]=v.useState(!0),T=o&&c;return v.useEffect(()=>{(async()=>{try{x(!0);const{data:M}=await pe();if(M.user){const{profile:q}=await dt(M.user.id);p(q)}else p(null)}catch(M){console.error("Error loading user profile:",M),p(null)}finally{x(!1)}})()},[]),s.jsxs("header",{className:w("sticky top-0 z-30 flex h-20 items-center gap-4 border-b bg-background/95 backdrop-blur-sm","px-4 md:px-6 lg:px-8","shadow-sm"),children:[s.jsxs("div",{className:"hidden md:flex w-full max-w-sm relative",children:[s.jsx($e,{className:w("absolute top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground",i?"right-3":"left-3")}),s.jsx(Ze,{type:"search",placeholder:t("products.search"),className:w("w-full bg-muted/50 border-none rounded-full","focus-visible:ring-1 focus-visible:ring-primary/50",i?"pr-9 pl-3":"pl-9 pr-3"),style:{unicodeBidi:"isolate"}})]}),s.jsxs("div",{className:"flex flex-1 items-center justify-end gap-3",children:[s.jsx(Et,{}),s.jsx(et,{}),s.jsxs(B,{variant:"ghost",size:"icon",className:"relative hidden sm:flex",children:[s.jsx(ht,{className:"h-5 w-5"}),s.jsx("span",{className:"sr-only",style:{unicodeBidi:"isolate"},children:t("notifications")}),s.jsx("span",{className:"absolute top-1 right-1 h-2 w-2 rounded-full bg-racing-red animate-pulse"})]}),s.jsxs("div",{className:w("flex items-center gap-3","sm:border-l sm:border-r-0 sm:pl-4 sm:pr-0",i&&"sm:border-r sm:border-l-0 sm:pr-4 sm:pl-0"),children:[s.jsxs(tt,{children:[s.jsx(rt,{asChild:!0,children:s.jsxs("div",{className:"flex items-center gap-2 cursor-pointer",children:[s.jsxs(nt,{className:"h-10 w-10 border-2 border-electric-orange/20 shadow-sm",children:[s.jsx(at,{src:h?.avatar_url||"",alt:"Avatar"}),s.jsx(st,{className:"bg-gradient-to-br from-midnight-blue to-primary text-white font-bold",children:h?.full_name?h.full_name.charAt(0).toUpperCase():n()?"S":a()?"M":"U"})]}),s.jsxs("div",{className:"hidden md:flex flex-col",children:[s.jsx("span",{className:"text-sm font-semibold text-primary",style:{unicodeBidi:"isolate"},children:n()?h?.company_name||t("roles.supplierAndManufacturer"):a()?h?.store_name||t("roles.merchantRetailer"):h?.full_name||t("roles.user")}),s.jsx("span",{className:"text-xs text-muted-foreground",style:{unicodeBidi:"isolate"},children:h?.email||"No email"})]}),s.jsx(xe,{className:"h-4 w-4 text-muted-foreground hidden md:block"})]})}),s.jsxs(ot,{align:"end",className:"w-64",children:[s.jsx(it,{className:"font-normal",children:s.jsxs("div",{className:"flex flex-col space-y-1",children:[s.jsx("p",{className:"text-sm font-medium leading-none",children:h?.full_name||t("settings.yourAccount")}),s.jsx("p",{className:"text-xs leading-none text-muted-foreground",children:h?.email||"No email"}),h?.last_login&&s.jsxs("p",{className:"text-xs text-muted-foreground flex items-center mt-1",children:[s.jsx(ct,{className:"h-3 w-3 mr-1"}),t("settings.lastLogin"),": ",Rr(new Date(h.last_login),"PPp")]})]})}),s.jsx(K,{}),s.jsxs(ut,{children:[s.jsx(W,{asChild:!0,children:s.jsxs(ie,{to:"/app/settings",className:"flex items-center gap-2 cursor-pointer",children:[s.jsx(lt,{className:"h-4 w-4"}),t("settings.profile")]})}),s.jsx(W,{asChild:!0,children:s.jsxs(ie,{to:"/app/settings",className:"flex items-center gap-2 cursor-pointer",children:[s.jsx(be,{className:"h-4 w-4"}),t("settings.accountSettings")]})})]}),s.jsx(K,{}),T&&s.jsxs(s.Fragment,{children:[s.jsxs(W,{className:"flex items-center gap-2 cursor-pointer text-destructive hover:text-destructive hover:bg-destructive/10",onClick:()=>{l("supplier"),d(!0)},children:[s.jsx(te,{className:"h-4 w-4"}),t("auth.logoutFromSupplier")]}),s.jsxs(W,{className:"flex items-center gap-2 cursor-pointer text-destructive hover:text-destructive hover:bg-destructive/10",onClick:()=>{l("merchant"),d(!0)},children:[s.jsx(re,{className:"h-4 w-4"}),t("auth.logoutFromMerchant")]}),s.jsx(K,{})]}),s.jsxs(W,{className:"flex items-center gap-2 cursor-pointer text-destructive hover:text-destructive hover:bg-destructive/10",onClick:()=>{l(void 0),d(!0)},children:[s.jsx(X,{className:"h-4 w-4"}),t("navigation.logout")]})]})]}),s.jsx(ve,{isOpen:m,onClose:()=>d(!1),role:u})]})]})]})}function Hr({children:t}){const{i18n:e}=E(),r=e.language==="ar",n=ne(),[a,o]=v.useState(!n);v.useEffect(()=>{o(!n)},[n]);const c=i=>{o(i)};return s.jsxs("div",{className:w("flex min-h-screen bg-background",r?"rtl":"ltr"),children:[s.jsx(Pt,{onToggle:c}),s.jsxs("div",{className:w("flex-1 flex flex-col transition-all duration-300 ease-in-out",n?"ml-0 mr-0":r?a?"mr-[280px]":"mr-[70px]":a?"ml-[280px]":"ml-[70px]","w-full p-0"),children:[s.jsx(Yr,{}),s.jsx("main",{className:"flex-1 p-4 md:p-6 lg:p-8 overflow-x-hidden bg-muted/10",children:s.jsx("div",{className:"max-w-7xl mx-auto w-full",children:t})})]})]})}export{Hr as A,ht as B,St as a,Rr as f,Nt as u};
