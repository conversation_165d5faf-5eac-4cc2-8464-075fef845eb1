import{r as l,a as p,j as e,k as g,l as v,U as j,m as T,I as d,B as o}from"./index-DRgoPWv8.js";import{s as y,a as P}from"./dexatelService-BqJYMLGI.js";function N(){const[a,u]=l.useState("+213770882600"),[r,h]=l.useState(""),[i,m]=l.useState(""),[n,c]=l.useState(!1),{toast:t}=p(),f=async()=>{c(!0);try{const s=await y(a);s.success?(m(s.data.id),t({title:"OTP Sent",description:`Verification code sent to ${a}`})):t({title:"Failed to Send",description:s.error,variant:"destructive"})}catch{t({title:"Error",description:"Failed to send OTP",variant:"destructive"})}finally{c(!1)}},x=async()=>{if(!i||!r){t({title:"Missing Information",description:"Please send <PERSON><PERSON> first and enter the code",variant:"destructive"});return}c(!0);try{const s=await P(i,r);s.success?t({title:"Verification Successful",description:"OTP verified successfully!"}):t({title:"Verification Failed",description:s.error,variant:"destructive"})}catch{t({title:"Error",description:"Failed to verify OTP",variant:"destructive"})}finally{c(!1)}};return e.jsx("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center p-4",children:e.jsxs(g,{className:"w-full max-w-md",children:[e.jsx(v,{children:e.jsx(j,{className:"text-center",children:"OTP Test Page"})}),e.jsxs(T,{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium mb-2",children:"Phone Number"}),e.jsx(d,{type:"tel",value:a,onChange:s=>u(s.target.value),placeholder:"+213770882600"})]}),e.jsx(o,{onClick:f,disabled:n||!a,className:"w-full",children:n?"Sending...":"Send OTP"}),i&&e.jsx("div",{className:"p-3 bg-green-50 border border-green-200 rounded",children:e.jsxs("p",{className:"text-sm text-green-800",children:["OTP sent! Message ID: ",e.jsx("code",{className:"font-mono",children:i})]})}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium mb-2",children:"Verification Code"}),e.jsx(d,{type:"text",value:r,onChange:s=>h(s.target.value),placeholder:"Enter 6-digit code",maxLength:6})]}),e.jsx(o,{onClick:x,disabled:n||!i||!r,className:"w-full",variant:"outline",children:n?"Verifying...":"Verify OTP"}),e.jsxs("div",{className:"text-xs text-gray-500 space-y-1",children:[e.jsx("p",{children:"• This is a test page for the new OTP implementation"}),e.jsx("p",{children:"• Check your phone for the SMS with the verification code"}),e.jsx("p",{children:"• The code expires in 5 minutes"})]})]})]})})}export{N as default};
