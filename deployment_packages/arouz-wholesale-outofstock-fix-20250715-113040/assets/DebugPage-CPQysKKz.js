import{t as C,r as u,j as e,k as i,l as t,U as c,m as l,B as a,as as D,aE as f}from"./index-DRgoPWv8.js";function S(){const{items:o}=C(),[h,x]=u.useState([]),[p,g]=u.useState(!1),j=async()=>{g(!0);try{console.log("🧪 [DEBUG] Fetching marketplace products...");const s=await f("tyres");console.log("🧪 [DEBUG] Fetched products:",s),x(s)}catch(s){console.error("🧪 [DEBUG] Error fetching products:",s)}finally{g(!1)}},m=()=>{console.log("🧪 [DEBUG] Current cart items:",o);const s=o.reduce((r,n)=>{const d=n.supplierName||"Unknown Supplier";return r[d]||(r[d]={items:[],location:n.shippingOrigin||"Location TBD"}),r[d].items.push(n),r},{});console.log("🧪 [DEBUG] Grouped by supplier:",s)},y=()=>{console.log("🧪 [DEBUG] LocalStorage data:"),console.log("Cart:",localStorage.getItem("arouz_cart")),console.log("Phone Session:",localStorage.getItem("phone_auth_session")),console.log("Consumer Profile:",localStorage.getItem("consumer_profile"))};return e.jsxs("div",{className:"container mx-auto p-6 space-y-6",children:[e.jsx("h1",{className:"text-3xl font-bold text-red-600",children:"🐛 DEBUG PAGE"}),e.jsxs(i,{children:[e.jsx(t,{children:e.jsx(c,{children:"📦 Product Fetching Test"})}),e.jsxs(l,{className:"space-y-4",children:[e.jsx(a,{onClick:j,disabled:p,children:p?"Loading...":"Fetch Test Products"}),h.length>0&&e.jsxs("div",{className:"space-y-2",children:[e.jsx("h3",{className:"font-semibold",children:"Fetched Products:"}),h.slice(0,3).map((s,r)=>e.jsxs("div",{className:"p-3 bg-gray-100 rounded text-sm",children:[e.jsxs("div",{children:[e.jsx("strong",{children:"ID:"})," ",s.id]}),e.jsxs("div",{children:[e.jsx("strong",{children:"Name:"})," ",s.name]}),e.jsxs("div",{children:[e.jsx("strong",{children:"Supplier:"})," ",s.supplierName]}),e.jsxs("div",{children:[e.jsx("strong",{children:"Supplier Account ID:"})," ",s.supplierAccountId]}),e.jsxs("div",{children:[e.jsx("strong",{children:"Shipping Origin:"})," ",s.shippingOrigin]})]},r))]})]})]}),e.jsxs(i,{children:[e.jsx(t,{children:e.jsx(c,{children:"🛒 Cart Data Test"})}),e.jsxs(l,{className:"space-y-4",children:[e.jsx(a,{onClick:m,children:"Test Cart Data in Console"}),e.jsxs("div",{className:"space-y-2",children:[e.jsxs("h3",{className:"font-semibold",children:["Current Cart Items (",o.length,"):"]}),o.map((s,r)=>e.jsxs("div",{className:"p-3 bg-gray-100 rounded text-sm",children:[e.jsxs("div",{children:[e.jsx("strong",{children:"Name:"})," ",s.name]}),e.jsxs("div",{children:[e.jsx("strong",{children:"Supplier:"})," ",s.supplierName]}),e.jsxs("div",{children:[e.jsx("strong",{children:"Supplier Account ID:"})," ",s.supplierAccountId]}),e.jsxs("div",{children:[e.jsx("strong",{children:"Shipping Origin:"})," ",s.shippingOrigin]}),e.jsxs("div",{children:[e.jsx("strong",{children:"Original Product ID:"})," ",s.originalProductId]})]},r))]})]})]}),e.jsxs(i,{children:[e.jsx(t,{children:e.jsx(c,{children:"💾 LocalStorage Test"})}),e.jsxs(l,{className:"space-y-4",children:[e.jsx(a,{onClick:y,children:"Test LocalStorage in Console"}),e.jsxs("div",{className:"space-y-2 text-sm",children:[e.jsxs("div",{children:[e.jsx("strong",{children:"Cart Items:"})," ",localStorage.getItem("arouz_cart")?.length||0," characters"]}),e.jsxs("div",{children:[e.jsx("strong",{children:"Phone Session:"})," ",localStorage.getItem("phone_auth_session")?"Present":"Missing"]}),e.jsxs("div",{children:[e.jsx("strong",{children:"Consumer Profile:"})," ",localStorage.getItem("consumer_profile")?"Present":"Missing"]})]})]})]}),e.jsxs(i,{children:[e.jsx(t,{children:e.jsx(c,{children:"🗄️ Database Connection Test"})}),e.jsx(l,{className:"space-y-4",children:e.jsx(a,{onClick:()=>{console.log("🧪 [DEBUG] Testing database connection..."),D(async()=>{const{supabase:s}=await import("./index-DRgoPWv8.js").then(r=>r.db);return{supabase:s}},[]).then(({supabase:s})=>{s.from("products").select("id, name, supplier_name, shipping_origin").limit(5).then(({data:r,error:n})=>{n?console.error("🧪 [DEBUG] Database error:",n):console.log("🧪 [DEBUG] Database products:",r)})})},children:"Test Database Connection"})})]}),e.jsxs(i,{children:[e.jsx(t,{children:e.jsx(c,{children:"📋 Order Creation Test"})}),e.jsx(l,{className:"space-y-4",children:e.jsx(a,{onClick:()=>{console.log("🧪 [DEBUG] Testing order creation data transformation...");const s=o.map(r=>({product_id:r.originalProductId||r.id.toString(),product_name:r.name,quantity:r.quantity,unit_price:r.price,total_price:r.price*r.quantity,supplier_name:r.supplierName||"Unknown Supplier",supplier_wilaya:r.shippingOrigin||"",supplier_account_id:r.supplierAccountId||"",marketplace_section:r.marketplaceSection||"retail"}));console.log("🧪 [DEBUG] Transformed order items:",s)},children:"Test Order Data Transformation"})})]}),e.jsxs(i,{className:"bg-blue-50",children:[e.jsx(t,{children:e.jsx(c,{className:"text-blue-800",children:"📋 Debug Instructions"})}),e.jsx(l,{className:"text-blue-700",children:e.jsxs("ol",{className:"list-decimal list-inside space-y-2",children:[e.jsx("li",{children:"Open browser console (F12)"}),e.jsx("li",{children:'Click "Fetch Test Products" to see if products have shipping origins'}),e.jsx("li",{children:"Add products to cart from marketplace"}),e.jsx("li",{children:'Click "Test Cart Data" to verify cart items have shipping origins'}),e.jsx("li",{children:"Go to checkout and check if shipping origins display correctly"}),e.jsx("li",{children:"Use the debug section in checkout for detailed analysis"})]})})]})]})}export{S as DebugPage};
