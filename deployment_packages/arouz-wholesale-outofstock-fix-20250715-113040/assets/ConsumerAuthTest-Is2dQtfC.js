import{r as n,j as t,c7 as h,c9 as x,cb as N}from"./index-DRgoPWv8.js";const j=()=>{const[c,i]=n.useState([]),[d,m]=n.useState(!1),[r,b]=n.useState("0555123456"),[l,f]=n.useState("Test User"),[g,p]=n.useState("1234"),e=s=>{i(o=>[...o,`${new Date().toLocaleTimeString()}: ${s}`])},T=()=>{i([])},y=async()=>{m(!0),e("🧪 Starting Consumer Authentication Test...");try{e("📱 Test 1: Consumer Authentication (No Passcode)");const s=await h({phone:r,fullName:l});s.success?(e("✅ Test 1 PASSED: Consumer authentication successful"),e(`   User ID: ${s.user?.id}`),e(`   Action: ${s.action}`)):e(`❌ Test 1 FAILED: ${s.error}`),e("🔍 Test 2: Session Check");const o=x();o?(e("✅ Test 2 PASSED: Session found"),e(`   Phone: ${o.phone}`),e(`   User ID: ${o.userId}`)):e("❌ Test 2 FAILED: No session found"),e("🔐 Test 3: Consumer Authentication (With Passcode)");const a=await h({phone:r+"1",fullName:l+" 2",passcode:g});a.success?(e("✅ Test 3 PASSED: Consumer authentication with passcode successful"),e(`   User ID: ${a.user?.id}`),e(`   Action: ${a.action}`)):e(`❌ Test 3 FAILED: ${a.error}`),e("🚪 Test 4: Consumer Logout");try{N();const u=x();e(u?"❌ Test 4 FAILED: Session still exists after logout":"✅ Test 4 PASSED: Logout successful")}catch(u){e(`❌ Test 4 FAILED: Logout error - ${u}`)}e("🏁 Consumer Authentication Test Complete")}catch(s){e(`💥 Test Suite Error: ${s}`)}finally{m(!1)}};return t.jsxs("div",{className:"max-w-4xl mx-auto p-6 bg-white rounded-lg shadow-lg",children:[t.jsx("h2",{className:"text-2xl font-bold text-gray-800 mb-6",children:"🧪 Consumer Authentication Test Suite"}),t.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 mb-6",children:[t.jsxs("div",{children:[t.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Test Phone Number"}),t.jsx("input",{type:"text",value:r,onChange:s=>b(s.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"0555123456"})]}),t.jsxs("div",{children:[t.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Test Name"}),t.jsx("input",{type:"text",value:l,onChange:s=>f(s.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"Test User"})]}),t.jsxs("div",{children:[t.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Test Passcode (4-6 digits)"}),t.jsx("input",{type:"text",value:g,onChange:s=>p(s.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"1234"})]})]}),t.jsxs("div",{className:"flex gap-4 mb-6",children:[t.jsx("button",{onClick:y,disabled:d,className:"px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed",children:d?"🔄 Running Tests...":"🧪 Run Consumer Auth Tests"}),t.jsx("button",{onClick:T,className:"px-6 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700",children:"🗑️ Clear Results"})]}),t.jsxs("div",{className:"bg-gray-50 rounded-lg p-4",children:[t.jsx("h3",{className:"text-lg font-semibold text-gray-800 mb-3",children:"Test Results:"}),t.jsx("div",{className:"space-y-1 max-h-96 overflow-y-auto",children:c.length===0?t.jsx("p",{className:"text-gray-500 italic",children:'No test results yet. Click "Run Consumer Auth Tests" to start.'}):c.map((s,o)=>t.jsx("div",{className:`text-sm font-mono p-2 rounded ${s.includes("✅")?"bg-green-100 text-green-800":s.includes("❌")?"bg-red-100 text-red-800":s.includes("🧪")||s.includes("🏁")?"bg-blue-100 text-blue-800":"bg-gray-100 text-gray-700"}`,children:s},o))})]})]})};export{j as default};
