import{c as Te,r as d,j as e,k as X,l as Fe,U as Ie,c0 as qe,m as Z,aJ as ce,aK as le,aL as P,aM as A,L as r,I as c,B as k,i as ee,aw as te,a9 as Re,aA as xe,x as Qe,d3 as je,aB as ge,E as He,ay as I,bb as ve,D as ne,d as de,e as oe,f as ue,T as me,aa as ye,H as be,ci as Ne,h as fe,ar as Me,u as Oe}from"./index-DRgoPWv8.js";import{P as ke}from"./ProductFormDialog-ZxkxRElL.js";import{W as he,u as pe,I as Se,M as Be,a as Ce,S as Ve,P as Pe}from"./ProductEditDialog-uLc0Brqc.js";import{S as K,a as L,b as W,c as _,d as s}from"./select-KZcg1xny.js";import{generateProductId as Ge,generateUniqueProductId as Ue}from"./idGenerator-CO0vKFJj.js";import{getSubcategoriesForCategory as Ke,CATEGORIES as Le}from"./categories-k_ueMvEL.js";/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const We=Te("Image",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",ry:"2",key:"1m3agn"}],["circle",{cx:"9",cy:"9",r:"2",key:"af1f0g"}],["path",{d:"m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21",key:"1xmnt7"}]]),Ae=({onDescriptionGenerated:j,initialDescription:m=""})=>{const[p,a]=d.useState("partNumber"),[h,y]=d.useState(!1),[b,D]=d.useState(""),[g,Q]=d.useState(""),[S,C]=d.useState(""),[B,w]=d.useState(m),[N,H]=d.useState(""),[x,E]=d.useState(""),U=async()=>{y(!0),E("");try{if(await new Promise(o=>setTimeout(o,2e3)),b||g||S){const o=p==="partNumber"?"part number":p==="oemNumber"?"OEM number":"EAN code",q=p==="partNumber"?b:p==="oemNumber"?g:S,t=`
## Product Identification
- ${o.toUpperCase()}: ${q}
- Brand: Brembo
- Component Type: Brake Disc
- Function: Front Axle

## Technical Specifications
- Diameter: 305mm
- Thickness: 28mm
- Minimum Thickness: 26mm
- Number of Holes: 5
- Hole Circle Ø: 120mm
- Centering Diameter: 67mm
- Surface: Coated
- Brake Disc Type: Internally Vented, Two-Piece
- Height: 45.5mm

## Vehicle Compatibility
- BMW 3 Series (E90/E91/E92/E93) 2005-2011
- BMW 5 Series (E60/E61) 2003-2010
- BMW X1 (E84) 2009-2015

## Additional Information
- High-carbon cast iron construction for improved thermal stability
- UV-coated for corrosion resistance
- Precision balanced for smooth operation
- Meets or exceeds OE specifications
`;return H(t),t}else throw new Error("Please enter a valid identification number")}catch(o){return E(o instanceof Error?o.message:"Failed to generate description"),null}finally{y(!1)}},u=async o=>{y(!0),E("");try{await new Promise(t=>setTimeout(t,3e3));const q=`
## Product Identification
- Part Number: 09.B085.13
- Brand: Brembo
- Component Type: Brake Disc
- Function: Front Axle

## Technical Specifications
- Diameter: 305mm
- Thickness: 28mm
- Minimum Thickness: 26mm
- Number of Holes: 5
- Hole Circle Ø: 120mm
- Centering Diameter: 67mm
- Surface: Coated
- Brake Disc Type: Internally Vented, Two-Piece
- Height: 45.5mm

## Vehicle Compatibility
- BMW 3 Series (E90/E91/E92/E93) 2005-2011
- BMW 5 Series (E60/E61) 2003-2010
- BMW X1 (E84) 2009-2015

## Additional Information
- High-carbon cast iron construction for improved thermal stability
- UV-coated for corrosion resistance
- Precision balanced for smooth operation
- Meets or exceeds OE specifications
`;return H(q),q}catch(q){return E(q instanceof Error?q.message:"Failed to generate description from image"),null}finally{y(!1)}},M=o=>{o.target.files?.[0]&&u()},O=async()=>{await U()&&I.success("Description generated successfully")},V=()=>{j(N),w(N),I.success("Description applied successfully")};return e.jsxs(X,{className:"w-full",children:[e.jsxs(Fe,{children:[e.jsxs(Ie,{className:"text-xl flex items-center gap-2",children:[e.jsx(he,{className:"h-5 w-5 text-primary"}),"AI Description Generator"]}),e.jsx(qe,{children:"Generate a structured product description using AI technology"})]}),e.jsxs(Z,{children:[e.jsxs(ce,{value:p,onValueChange:a,className:"w-full",children:[e.jsxs(le,{className:"grid grid-cols-3 mb-4",children:[e.jsx(P,{value:"partNumber",children:"Part Number"}),e.jsx(P,{value:"oemNumber",children:"OEM/OE Number"}),e.jsx(P,{value:"eanCode",children:"EAN/UPC/GTIN"})]}),e.jsx(A,{value:"partNumber",className:"space-y-4",children:e.jsxs("div",{className:"space-y-2",children:[e.jsx(r,{htmlFor:"partNumber",children:"Manufacturer Part Number (MPN)"}),e.jsxs("div",{className:"flex gap-2",children:[e.jsx(c,{id:"partNumber",value:b,onChange:o=>D(o.target.value),placeholder:"e.g., 09.B085.13",className:"flex-1"}),e.jsxs(k,{onClick:O,disabled:h||!b,className:"gap-2",children:[h?e.jsx(ee,{className:"h-4 w-4 animate-spin"}):e.jsx(te,{className:"h-4 w-4"}),"Search"]})]}),e.jsx("p",{className:"text-sm text-muted-foreground",children:"Enter the manufacturer's part number to find detailed specifications"})]})}),e.jsx(A,{value:"oemNumber",className:"space-y-4",children:e.jsxs("div",{className:"space-y-2",children:[e.jsx(r,{htmlFor:"oemNumber",children:"Original Equipment Number"}),e.jsxs("div",{className:"flex gap-2",children:[e.jsx(c,{id:"oemNumber",value:g,onChange:o=>Q(o.target.value),placeholder:"e.g., 34116797602",className:"flex-1"}),e.jsxs(k,{onClick:O,disabled:h||!g,className:"gap-2",children:[h?e.jsx(ee,{className:"h-4 w-4 animate-spin"}):e.jsx(te,{className:"h-4 w-4"}),"Search"]})]}),e.jsx("p",{className:"text-sm text-muted-foreground",children:"Enter the OEM or OE number to find detailed specifications"})]})}),e.jsx(A,{value:"eanCode",className:"space-y-4",children:e.jsxs("div",{className:"space-y-2",children:[e.jsx(r,{htmlFor:"eanCode",children:"EAN / UPC / GTIN Code"}),e.jsxs("div",{className:"flex gap-2",children:[e.jsx(c,{id:"eanCode",value:S,onChange:o=>C(o.target.value),placeholder:"e.g., 8020584039137",className:"flex-1"}),e.jsxs(k,{onClick:O,disabled:h||!S,className:"gap-2",children:[h?e.jsx(ee,{className:"h-4 w-4 animate-spin"}):e.jsx(te,{className:"h-4 w-4"}),"Search"]})]}),e.jsx("p",{className:"text-sm text-muted-foreground",children:"Enter the EAN, UPC, or GTIN barcode to find detailed specifications"})]})})]}),e.jsx(Re,{className:"my-6"}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("h3",{className:"text-lg font-medium",children:"Image Recognition"}),e.jsxs("div",{children:[e.jsx(c,{type:"file",id:"imageUpload",className:"hidden",accept:"image/*",onChange:M}),e.jsx(r,{htmlFor:"imageUpload",asChild:!0,children:e.jsxs(k,{variant:"outline",className:"gap-2 cursor-pointer",children:[e.jsx(We,{className:"h-4 w-4"}),"Upload Image"]})})]})]}),e.jsx("p",{className:"text-sm text-muted-foreground",children:"Upload an image of the product to automatically extract specifications using AI"})]}),x&&e.jsxs(xe,{variant:"destructive",className:"mt-6",children:[e.jsx(Qe,{className:"h-4 w-4"}),e.jsx(je,{children:"Error"}),e.jsx(ge,{children:x})]}),N&&e.jsxs("div",{className:"mt-6 space-y-4",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("h3",{className:"text-lg font-medium",children:"Generated Description"}),e.jsx(k,{onClick:V,size:"sm",children:"Apply"})]}),e.jsx("div",{className:"p-4 border rounded-md bg-muted/30 whitespace-pre-wrap font-mono text-sm",children:N})]}),e.jsxs(xe,{className:"mt-6",children:[e.jsx(He,{className:"h-4 w-4"}),e.jsx(je,{children:"Description Format"}),e.jsx(ge,{children:"The generated description follows a structured format with sections for Product Identification, Technical Specifications, Vehicle Compatibility, and Additional Information. This format ensures consistency across all products."})]})]})]})},_e=({isOpen:j,onClose:m,onSave:p,categories:a,initialBarcode:h=""})=>{const{userRole:y,isSupplier:b,isMerchant:D}=ve(),[g,Q]=d.useState(!1),[S,C]=d.useState("basic"),[B,w]=d.useState(""),[N,H]=d.useState([]),[x,E]=d.useState([]),[U,u]=d.useState([]),[M,O]=d.useState([]),[V,o]=d.useState([]),[q,t]=d.useState(!1),{register:T,handleSubmit:z,formState:{errors:i},reset:se,setValue:$,watch:G}=pe({defaultValues:{name:"",sku:"",partArticleNumber:h,category:"brakes",subcategory:"Brake Discs",descriptionAndSpecifications:"",manufacturer:"",supplierName:"",stockQuantity:0,retailPrice:0,shippingOrigin:"",minimumOrderQuantity:void 0,quotationRequestEnabled:!1,status:"draft"}}),ae=G("category"),re=G("subcategory"),J=async n=>{if(b()){if(!x||x.length===0){C("pricing"),I.error("Wholesale pricing required",{description:"Please add at least one wholesale pricing tier.",duration:5e3});return}if(x.filter(f=>!f.minQuantity||f.minQuantity<=0||!f.price||f.price<=0).length>0){C("pricing"),I.error("Invalid pricing tiers",{description:"Please ensure all pricing tiers have valid minimum quantity and price values.",duration:5e3});return}}else if(D()&&(!n.retailPrice||n.retailPrice<=0)){C("pricing"),I.error("Retail price required",{description:"Please enter a valid retail price greater than 0.",duration:5e3});return}Q(!0);try{const F=b()?"wholesale":"retail",f=n.category||"brakes",Y=Ge(f,F),Ee={...n,id:Y,category:f,primaryImage:B,additionalImages:N,vehicleTypeCompatibility:U,certifications:M,availableShippingMethods:V,wholesalePricingTiers:x,createdAt:new Date,updatedAt:new Date};await p(Ee),se(),w(""),H([]),E([]),u([]),O([]),o([]),C("basic"),I.success("Product created successfully"),m()}catch(F){console.error("Error creating product:",F),I.error("Failed to create product")}finally{Q(!1)}},ie=()=>{E([...x,{minQuantity:1,price:0}])},l=n=>{E(x.filter((F,f)=>f!==n))},v=(n,F,f)=>{const Y=[...x];Y[n]={...Y[n],[F]:f},E(Y)},R=n=>{$("description",n),t(!1)};return e.jsx(ne,{open:j,onOpenChange:n=>!n&&m(),children:e.jsxs(de,{className:"sm:max-w-4xl max-h-[90vh] overflow-y-auto",children:[e.jsx(oe,{children:e.jsx(ue,{className:"text-xl",children:"Add New Brake Product"})}),e.jsxs("form",{onSubmit:z(J),className:"space-y-6 py-4",children:[e.jsxs(ce,{value:S,onValueChange:C,className:"w-full",children:[e.jsxs(le,{className:"mb-6 bg-muted",children:[e.jsx(P,{value:"basic",className:"data-[state=active]:bg-background",children:"Basic Information"}),e.jsx(P,{value:"images",className:"data-[state=active]:bg-background",children:"Images"}),e.jsx(P,{value:"details",className:"data-[state=active]:bg-background",children:"Product Details"}),e.jsx(P,{value:"description",className:"data-[state=active]:bg-background",children:"Description"}),e.jsxs(P,{value:"pricing",className:"data-[state=active]:bg-background",children:["Pricing & Inventory",(b()&&(!x||x.length===0)||D()&&(!G("retailPrice")||G("retailPrice")<=0))&&e.jsx("span",{className:"ml-1 text-destructive",children:"*"})]}),e.jsx(P,{value:"shipping",className:"data-[state=active]:bg-background",children:"Shipping"})]}),e.jsx(A,{value:"basic",className:"space-y-6",children:e.jsxs("div",{className:"grid grid-cols-2 gap-6",children:[e.jsxs("div",{className:"space-y-3",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsxs(r,{htmlFor:"name",children:["Product Name ",e.jsx("span",{className:"text-destructive",children:"*"})]}),e.jsx(c,{id:"name",...T("name",{required:"Product name is required"}),placeholder:"Enter product name",className:i.name?"border-destructive":""}),i.name&&e.jsx("p",{className:"text-sm text-destructive",children:i.name.message})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(r,{htmlFor:"sku",children:"SKU"}),e.jsx(c,{id:"sku",...T("sku"),placeholder:"Enter SKU (optional)",className:i.sku?"border-destructive":""}),i.sku&&e.jsx("p",{className:"text-sm text-destructive",children:i.sku.message})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsxs(r,{htmlFor:"partArticleNumber",children:["Part Article Number ",e.jsx("span",{className:"text-destructive",children:"*"})]}),e.jsx(c,{id:"partArticleNumber",...T("partArticleNumber",{required:"Part Article Number is required"}),placeholder:"Enter part article number",defaultValue:h,className:i.partArticleNumber?"border-destructive":""}),i.partArticleNumber&&e.jsx("p",{className:"text-sm text-destructive",children:i.partArticleNumber.message})]})]}),e.jsxs("div",{className:"space-y-3",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsxs(r,{htmlFor:"category",children:["Category ",e.jsx("span",{className:"text-destructive",children:"*"})]}),e.jsxs(K,{value:ae,onValueChange:n=>$("category",n),children:[e.jsx(L,{id:"category",className:i.category?"border-destructive":"",children:e.jsx(W,{placeholder:"Select category"})}),e.jsx(_,{children:a.map(n=>e.jsx(s,{value:n.id,children:n.name},n.id))})]}),i.category&&e.jsx("p",{className:"text-sm text-destructive",children:i.category.message})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsxs(r,{htmlFor:"subcategory",children:["Subcategory ",e.jsx("span",{className:"text-destructive",children:"*"})]}),e.jsxs(K,{value:re,onValueChange:n=>$("subcategory",n),children:[e.jsx(L,{id:"subcategory",className:i.subcategory?"border-destructive":"",children:e.jsx(W,{placeholder:"Select subcategory"})}),e.jsxs(_,{children:[e.jsx(s,{value:"Vacuum Brake Booster",children:"Vacuum Brake Booster"}),e.jsx(s,{value:"Vacuum Pump",children:"Vacuum Pump"}),e.jsx(s,{value:"Brake Force Distributor",children:"Brake Force Distributor"}),e.jsx(s,{value:"Master Brake Cylinder",children:"Master Brake Cylinder"}),e.jsx(s,{value:"Repair Kit, Brake Master Cylinder",children:"Repair Kit, Brake Master Cylinder"}),e.jsx(s,{value:"Brake Fluid Reservoir",children:"Brake Fluid Reservoir"}),e.jsx(s,{value:"Brake Cylinder",children:"Brake Cylinder"}),e.jsx(s,{value:"Repair Kit, Wheel Brake Cylinder",children:"Repair Kit, Wheel Brake Cylinder"}),e.jsx(s,{value:"Brake Pads",children:"Brake Pads"}),e.jsx(s,{value:"Brake Pad Wear Indicator",children:"Brake Pad Wear Indicator"}),e.jsx(s,{value:"Accessory Kit, Disc Brake Pad",children:"Accessory Kit, Disc Brake Pad"}),e.jsx(s,{value:"High Performance Brake Caliper",children:"High Performance Brake Caliper"}),e.jsx(s,{value:"Drum Brake",children:"Drum Brake"}),e.jsx(s,{value:"Brake Shoes",children:"Brake Shoes"}),e.jsx(s,{value:"Accessories, Brake Shoe",children:"Accessories, Brake Shoe"}),e.jsx(s,{value:"Brake Drum",children:"Brake Drum"}),e.jsx(s,{value:"Brake Disc, Drum Brake",children:"Brake Disc, Drum Brake"}),e.jsx(s,{value:"Drum Brake Regulator",children:"Drum Brake Regulator"}),e.jsx(s,{value:"Brake Discs",children:"Brake Discs"}),e.jsx(s,{value:"Brake Discs and Pads",children:"Brake Discs and Pads"}),e.jsx(s,{value:"Anchor Plate",children:"Anchor Plate"}),e.jsx(s,{value:"High Performance Brake Disc",children:"High Performance Brake Disc"}),e.jsx(s,{value:"Handbrake",children:"Handbrake"}),e.jsx(s,{value:"Handbrake Cable",children:"Handbrake Cable"}),e.jsx(s,{value:"Handbrake Shoes",children:"Handbrake Shoes"}),e.jsx(s,{value:"Repair Kit, Handbrake Axle",children:"Repair Kit, Handbrake Axle"}),e.jsx(s,{value:"Switch, Handbrake Warning Light",children:"Switch, Handbrake Warning Light"}),e.jsx(s,{value:"Brake Hose",children:"Brake Hose"}),e.jsx(s,{value:"Brake Fluid",children:"Brake Fluid"}),e.jsx(s,{value:"Brake Line",children:"Brake Line"}),e.jsx(s,{value:"Vacuum Hose, Brake System",children:"Vacuum Hose, Brake System"}),e.jsx(s,{value:"Pressure Accumulator, Brake System",children:"Pressure Accumulator, Brake System"}),e.jsx(s,{value:"Pressure Switch, Hydraulic Brake System",children:"Pressure Switch, Hydraulic Brake System"}),e.jsx(s,{value:"ABS Sensor",children:"ABS Sensor"}),e.jsx(s,{value:"ABS Ring",children:"ABS Ring"}),e.jsx(s,{value:"ABS Pump",children:"ABS Pump"}),e.jsx(s,{value:"Overvoltage Relay, ABS",children:"Overvoltage Relay, ABS"}),e.jsx(s,{value:"ESP Sensor",children:"ESP Sensor"}),e.jsx(s,{value:"Control Unit, Brake/Vehicle Dynamics",children:"Control Unit, Brake/Vehicle Dynamics"}),e.jsx(s,{value:"Brake Caliper",children:"Brake Caliper"}),e.jsx(s,{value:"Brake Caliper Repair Kit",children:"Brake Caliper Repair Kit"}),e.jsx(s,{value:"Guide Sleeve Set, Brake Caliper",children:"Guide Sleeve Set, Brake Caliper"}),e.jsx(s,{value:"Brake Caliper Holder",children:"Brake Caliper Holder"}),e.jsx(s,{value:"Piston, Caliper",children:"Piston, Caliper"}),e.jsx(s,{value:"Brake Disc Screw",children:"Brake Disc Screw"}),e.jsx(s,{value:"Brake Light Switch",children:"Brake Light Switch"}),e.jsx(s,{value:"Threadlockers",children:"Threadlockers"}),e.jsx(s,{value:"Multifunctional Lubricant",children:"Multifunctional Lubricant"}),e.jsx(s,{value:"Mounting Paste",children:"Mounting Paste"}),e.jsx(s,{value:"Spray Grease",children:"Spray Grease"}),e.jsx(s,{value:"Brake Caliper Paint",children:"Brake Caliper Paint"}),e.jsx(s,{value:"Brake Tools",children:"Brake Tools"})]})]}),i.subcategory&&e.jsx("p",{className:"text-sm text-destructive",children:i.subcategory.message})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsxs(r,{htmlFor:"status",children:["Status ",e.jsx("span",{className:"text-destructive",children:"*"})]}),e.jsxs(K,{value:G("status"),onValueChange:n=>$("status",n),children:[e.jsx(L,{id:"status",className:i.status?"border-destructive":"",children:e.jsx(W,{placeholder:"Select status"})}),e.jsxs(_,{children:[e.jsx(s,{value:"active",children:"Active"}),e.jsx(s,{value:"draft",children:"Draft"}),e.jsx(s,{value:"pending_approval",children:"Pending Approval"}),e.jsx(s,{value:"out_of_stock",children:"Out of Stock"}),e.jsx(s,{value:"discontinued",children:"Discontinued"})]})]}),i.status&&e.jsx("p",{className:"text-sm text-destructive",children:i.status.message})]})]})]})}),e.jsx(A,{value:"images",className:"space-y-6",children:e.jsxs("div",{className:"grid grid-cols-2 gap-6",children:[e.jsx("div",{className:"space-y-4",children:e.jsxs("div",{className:"space-y-2",children:[e.jsxs(r,{children:["Primary Product Image ",e.jsx("span",{className:"text-destructive",children:"*"})]}),e.jsx(X,{children:e.jsx(Z,{className:"p-4",children:e.jsx(Se,{value:B,onChange:w,className:"w-full h-40 border-2 border-dashed border-muted-foreground/25 rounded-md"})})})]})}),e.jsx("div",{className:"space-y-4",children:e.jsxs("div",{className:"space-y-2",children:[e.jsx(r,{children:"Additional Product Images"}),e.jsx(X,{children:e.jsx(Z,{className:"p-4",children:e.jsx(Be,{value:N,onChange:H,className:"w-full min-h-40 border-2 border-dashed border-muted-foreground/25 rounded-md"})})})]})})]})}),e.jsx(A,{value:"details",className:"space-y-6",children:e.jsxs("div",{className:"grid grid-cols-2 gap-6",children:[e.jsxs("div",{className:"space-y-3",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsxs(r,{htmlFor:"manufacturer",children:["Manufacturer ",e.jsx("span",{className:"text-destructive",children:"*"})]}),e.jsx(c,{id:"manufacturer",...T("manufacturer",{required:"Manufacturer is required"}),placeholder:"Enter manufacturer",className:i.manufacturer?"border-destructive":""}),i.manufacturer&&e.jsx("p",{className:"text-sm text-destructive",children:i.manufacturer.message})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(r,{htmlFor:"supplierName",children:"Supplier Name"}),e.jsx(c,{id:"supplierName",...T("supplierName"),placeholder:"Enter supplier name"})]})]}),e.jsx("div",{className:"space-y-3",children:e.jsx(Ce,{value:U,onChange:u,label:"Vehicle Type Compatibility",description:"Select compatible vehicles from the compatibility hub database."})})]})}),e.jsx(A,{value:"description",className:"space-y-6",children:q?e.jsx(Ae,{onDescriptionGenerated:R,initialDescription:G("descriptionAndSpecifications")}):e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs(r,{htmlFor:"descriptionAndSpecifications",children:["Product Description and Specifications ",e.jsx("span",{className:"text-destructive",children:"*"})]}),e.jsxs(k,{type:"button",variant:"outline",size:"sm",onClick:()=>t(!0),className:"gap-1",children:[e.jsx(he,{className:"h-4 w-4"})," Generate with AI"]})]}),e.jsx(me,{id:"descriptionAndSpecifications",...T("descriptionAndSpecifications",{required:"Description and specifications are required"}),placeholder:"Enter a detailed, structured description of the product including technical specifications and compatibility information.",className:`min-h-[300px] ${i.descriptionAndSpecifications?"border-destructive":""}`}),i.descriptionAndSpecifications&&e.jsx("p",{className:"text-sm text-destructive",children:i.descriptionAndSpecifications.message}),e.jsx("p",{className:"text-sm text-muted-foreground",children:"For brake products, please include detailed technical specifications in a structured format. Use markdown formatting for better readability (e.g., ## Technical Specifications)."})]})}),e.jsx(A,{value:"pricing",className:"space-y-6",children:e.jsxs("div",{className:"grid grid-cols-2 gap-6",children:[e.jsxs("div",{className:"space-y-3",children:[!b()&&e.jsxs("div",{className:"space-y-2",children:[e.jsxs(r,{htmlFor:"retailPrice",children:["Retail Price (DZD) ",e.jsx("span",{className:"text-destructive",children:"*"})]}),e.jsx(c,{id:"retailPrice",type:"number",step:"0.01",...T("retailPrice",{valueAsNumber:!0,required:D()?"Retail price is required for merchant accounts":!1,min:{value:.01,message:"Retail price must be greater than 0"}}),placeholder:"Enter retail price in DZD",className:i.retailPrice?"border-destructive":""}),i.retailPrice&&e.jsx("p",{className:"text-sm text-destructive",children:i.retailPrice.message})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsxs(r,{htmlFor:"stockQuantity",children:["Stock Quantity ",e.jsx("span",{className:"text-destructive",children:"*"})]}),e.jsx(c,{id:"stockQuantity",type:"number",...T("stockQuantity",{required:"Stock quantity is required",valueAsNumber:!0}),placeholder:"0",className:i.stockQuantity?"border-destructive":""}),i.stockQuantity&&e.jsx("p",{className:"text-sm text-destructive",children:i.stockQuantity.message})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(r,{htmlFor:"minimumOrderQuantity",children:"Minimum Order Quantity"}),e.jsx(c,{id:"minimumOrderQuantity",type:"number",...T("minimumOrderQuantity",{valueAsNumber:!0}),placeholder:"1"})]}),e.jsxs("div",{className:"flex items-center space-x-2 pt-2",children:[e.jsx(ye,{id:"quotationRequestEnabled",checked:G("quotationRequestEnabled"),onCheckedChange:n=>$("quotationRequestEnabled",!!n)}),e.jsx(r,{htmlFor:"quotationRequestEnabled",className:"cursor-pointer",children:"Enable Quotation Requests"})]})]}),b()?e.jsx("div",{className:"space-y-3",children:e.jsxs("div",{className:"space-y-2",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs(r,{children:["Wholesale Pricing Tiers ",e.jsx("span",{className:"text-destructive",children:"*"})]}),e.jsxs(k,{type:"button",onClick:ie,size:"sm",variant:"outline",className:"gap-1",children:[e.jsx(be,{className:"h-4 w-4"})," Add Tier"]})]}),(!x||x.length===0)&&e.jsx("div",{className:"text-sm text-destructive bg-destructive/10 p-3 rounded-md",children:"At least one wholesale pricing tier is required for supplier accounts."}),x.length>0?e.jsx("div",{className:"space-y-3",children:x.map((n,F)=>e.jsxs("div",{className:"flex items-end gap-2 border p-2 rounded-md",children:[e.jsxs("div",{className:"space-y-1 flex-1",children:[e.jsx(r,{className:"text-xs",children:"Min Quantity"}),e.jsx(c,{type:"number",value:n.minQuantity,onChange:f=>v(F,"minQuantity",parseInt(f.target.value)),placeholder:"1",className:"h-8"})]}),e.jsxs("div",{className:"space-y-1 flex-1",children:[e.jsx(r,{className:"text-xs",children:"Max Quantity"}),e.jsx(c,{type:"number",value:n.maxQuantity||"",onChange:f=>v(F,"maxQuantity",f.target.value?parseInt(f.target.value):void 0),placeholder:"Optional",className:"h-8"})]}),e.jsxs("div",{className:"space-y-1 flex-1",children:[e.jsx(r,{className:"text-xs",children:"Price"}),e.jsx(c,{type:"number",step:"0.01",value:n.price,onChange:f=>v(F,"price",parseFloat(f.target.value)),placeholder:"0.00",className:"h-8"})]}),e.jsx(k,{type:"button",variant:"ghost",size:"sm",onClick:()=>l(F),className:"h-8 w-8 p-0",children:e.jsx(Ne,{className:"h-4 w-4"})})]},F))}):e.jsx("div",{className:"border border-dashed rounded-md p-4 text-center text-muted-foreground text-sm",children:"No pricing tiers added yet"})]})}):e.jsx("div",{className:"text-center py-8 text-muted-foreground",children:e.jsx("p",{children:"Wholesale pricing tiers are only available for supplier accounts."})})]})}),e.jsx(A,{value:"shipping",className:"space-y-6",children:e.jsx("div",{className:"grid grid-cols-2 gap-6",children:e.jsx("div",{className:"space-y-3",children:e.jsxs("div",{className:"space-y-2",children:[e.jsxs(r,{htmlFor:"shippingOrigin",children:["Shipping Origin ",e.jsx("span",{className:"text-destructive",children:"*"})]}),e.jsx(c,{id:"shippingOrigin",...T("shippingOrigin",{required:"Shipping origin is required"}),placeholder:"Enter shipping origin (country)",className:i.shippingOrigin?"border-destructive":""}),i.shippingOrigin&&e.jsx("p",{className:"text-sm text-destructive",children:i.shippingOrigin.message})]})})})})]}),e.jsxs(fe,{children:[e.jsx(k,{type:"button",variant:"outline",onClick:m,disabled:g,children:"Cancel"}),e.jsxs(k,{type:"submit",disabled:g,children:[g&&e.jsx(Loader2,{className:"mr-2 h-4 w-4 animate-spin"}),"Create Product"]})]})]})]})})},De=({isOpen:j,onClose:m,onSave:p,initialBarcode:a="",defaultCategory:h="brakes"})=>{const{isSupplier:y}=ve(),[b,D]=d.useState(!1),{register:g,handleSubmit:Q,formState:{errors:S},reset:C,setValue:B,watch:w}=pe({defaultValues:{name:"",sku:"",partArticleNumber:a,category:h,subcategory:"",descriptionAndSpecifications:"",manufacturer:"",supplierName:"",stockQuantity:0,retailPrice:0,shippingOrigin:"",minimumOrderQuantity:void 0,quotationRequestEnabled:!1,status:"draft"}}),N=w("category"),H=w("subcategory"),x=N?Ke(N):[];d.useEffect(()=>{N&&B("subcategory","")},[N,B]),d.useEffect(()=>{j&&C({name:"",sku:"",partArticleNumber:a,category:h,subcategory:"",descriptionAndSpecifications:"",manufacturer:"",supplierName:"",stockQuantity:0,retailPrice:0,shippingOrigin:"",minimumOrderQuantity:void 0,quotationRequestEnabled:!1,status:"draft"})},[j,C,a,h]);const E=async u=>{if(!u.category){I.error("Please select a category");return}if(!u.subcategory){I.error("Please select a subcategory");return}D(!0);try{const M=y()?"wholesale":"retail",O=await Ue(u.category,M),V={...u,id:O,category:u.category,createdAt:new Date,updatedAt:new Date};console.log("Creating product with data:",V),await p(V),I.success("Product created successfully"),m()}catch(M){console.error("Error creating product:",M),I.error("Failed to create product")}finally{D(!1)}},U=Le.filter(u=>u.id!=="tyres");return e.jsx(ne,{open:j,onOpenChange:m,children:e.jsxs(de,{className:"max-w-2xl max-h-[90vh] overflow-y-auto",children:[e.jsx(oe,{children:e.jsx(ue,{children:"Add New Product"})}),e.jsxs("form",{onSubmit:Q(E),className:"space-y-4",children:[e.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsx(r,{htmlFor:"category",children:"Category *"}),e.jsxs(K,{value:N,onValueChange:u=>B("category",u),children:[e.jsx(L,{children:e.jsx(W,{placeholder:"Select category"})}),e.jsx(_,{children:U.map(u=>e.jsx(s,{value:u.id,children:u.name},u.id))})]}),S.category&&e.jsx("p",{className:"text-red-500 text-sm",children:"Category is required"})]}),e.jsxs("div",{children:[e.jsx(r,{htmlFor:"subcategory",children:"Subcategory *"}),e.jsxs(K,{value:H,onValueChange:u=>B("subcategory",u),disabled:!N,children:[e.jsx(L,{children:e.jsx(W,{placeholder:"Select subcategory"})}),e.jsx(_,{children:x.map(u=>e.jsx(s,{value:u.id,children:u.name},u.id))})]}),S.subcategory&&e.jsx("p",{className:"text-red-500 text-sm",children:"Subcategory is required"})]})]}),e.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsx(r,{htmlFor:"name",children:"Product Name *"}),e.jsx(c,{id:"name",...g("name",{required:"Product name is required"}),placeholder:"Enter product name"}),S.name&&e.jsx("p",{className:"text-red-500 text-sm",children:S.name.message})]}),e.jsxs("div",{children:[e.jsx(r,{htmlFor:"sku",children:"SKU"}),e.jsx(c,{id:"sku",...g("sku"),placeholder:"Enter SKU"})]})]}),e.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsx(r,{htmlFor:"partArticleNumber",children:"Part/Article Number"}),e.jsx(c,{id:"partArticleNumber",...g("partArticleNumber"),placeholder:"Enter part number"})]}),e.jsxs("div",{children:[e.jsx(r,{htmlFor:"manufacturer",children:"Manufacturer"}),e.jsx(c,{id:"manufacturer",...g("manufacturer"),placeholder:"Enter manufacturer"})]})]}),e.jsxs("div",{children:[e.jsx(r,{htmlFor:"descriptionAndSpecifications",children:"Description & Specifications"}),e.jsx(me,{id:"descriptionAndSpecifications",...g("descriptionAndSpecifications"),placeholder:"Enter product description and specifications",rows:4})]}),e.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsx(r,{htmlFor:"retailPrice",children:"Retail Price (DZD)"}),e.jsx(c,{id:"retailPrice",type:"number",step:"0.01",...g("retailPrice",{valueAsNumber:!0}),placeholder:"0.00"})]}),e.jsxs("div",{children:[e.jsx(r,{htmlFor:"stockQuantity",children:"Stock Quantity"}),e.jsx(c,{id:"stockQuantity",type:"number",...g("stockQuantity",{valueAsNumber:!0}),placeholder:"0"})]})]}),e.jsxs("div",{className:"flex justify-end gap-2 pt-4",children:[e.jsxs(k,{type:"button",variant:"outline",onClick:m,children:[e.jsx(Me,{className:"w-4 h-4 mr-2"}),"Cancel"]}),e.jsxs(k,{type:"submit",disabled:b,children:[e.jsx(Ve,{className:"w-4 h-4 mr-2"}),b?"Creating...":"Create Product"]})]})]})]})})},we=({isOpen:j,onClose:m,onSave:p,product:a,categories:h})=>{const[y,b]=d.useState(!1),[D,g]=d.useState("basic"),[Q,S]=d.useState(a.primaryImage||""),[C,B]=d.useState(a.additionalImages||[]),[w,N]=d.useState(a.wholesalePricingTiers||[]),[H,x]=d.useState(Array.isArray(a.vehicleTypeCompatibility)?typeof a.vehicleTypeCompatibility[0]=="string"?a.vehicleTypeCompatibility.map(l=>({id:l.toLowerCase().replace(/\s+/g,"-"),type:"car",brand:l,model:"",displayName:l})):a.vehicleTypeCompatibility:[]),[E,U]=d.useState(a.certifications||[]),[u,M]=d.useState(a.availableShippingMethods||[]),[O,V]=d.useState(!1),{register:o,handleSubmit:q,formState:{errors:t},reset:T,setValue:z,watch:i}=pe({defaultValues:{id:a.id,name:a.name,sku:a.sku,partArticleNumber:a.partArticleNumber,category:a.category,subcategory:a.subcategory,descriptionAndSpecifications:a.descriptionAndSpecifications,manufacturer:a.manufacturer,supplierName:a.supplierName,stockQuantity:a.stockQuantity,retailPrice:a.retailPrice,shippingOrigin:a.shippingOrigin,minimumOrderQuantity:a.minimumOrderQuantity,quotationRequestEnabled:a.quotationRequestEnabled,status:a.status}});d.useEffect(()=>{if(T({id:a.id,name:a.name,sku:a.sku,barcode:a.barcode,category:a.category,subcategory:a.subcategory,description:a.description,manufacturer:a.manufacturer,supplierName:a.supplierName,stockQuantity:a.stockQuantity,retailPrice:a.retailPrice,shippingOrigin:a.shippingOrigin,minimumOrderQuantity:a.minimumOrderQuantity,quotationRequestEnabled:a.quotationRequestEnabled,status:a.status}),S(a.primaryImage||""),B(a.additionalImages||[]),N(a.wholesalePricingTiers||[]),Array.isArray(a.vehicleTypeCompatibility))if(typeof a.vehicleTypeCompatibility[0]=="string"){const l=a.vehicleTypeCompatibility.map(v=>({id:v.toLowerCase().replace(/\s+/g,"-"),type:"car",brand:v,model:"",displayName:v}));x(l)}else x(a.vehicleTypeCompatibility);else x([]);U(a.certifications||[]),M(a.availableShippingMethods||[])},[a,T]);const se=i("category"),$=i("subcategory"),G=async l=>{b(!0);try{const v={...l,primaryImage:Q,additionalImages:C,vehicleTypeCompatibility:H,certifications:E,availableShippingMethods:u,wholesalePricingTiers:w,updatedAt:new Date};await p(v),I.success("Product updated successfully"),m()}catch(v){console.error("Error updating product:",v),I.error("Failed to update product")}finally{b(!1)}},ae=()=>{N([...w,{minQuantity:1,price:0}])},re=l=>{N(w.filter((v,R)=>R!==l))},J=(l,v,R)=>{const n=[...w];n[l]={...n[l],[v]:R},N(n)},ie=l=>{z("description",l),V(!1)};return e.jsx(ne,{open:j,onOpenChange:l=>!l&&m(),children:e.jsxs(de,{className:"sm:max-w-4xl max-h-[90vh] overflow-y-auto",children:[e.jsx(oe,{children:e.jsx(ue,{className:"text-xl",children:"Edit Brake Product"})}),e.jsxs("form",{onSubmit:q(G),className:"space-y-6 py-4",children:[e.jsxs(ce,{value:D,onValueChange:g,className:"w-full",children:[e.jsxs(le,{className:"mb-6 bg-muted",children:[e.jsx(P,{value:"basic",className:"data-[state=active]:bg-background",children:"Basic Information"}),e.jsx(P,{value:"images",className:"data-[state=active]:bg-background",children:"Images"}),e.jsx(P,{value:"details",className:"data-[state=active]:bg-background",children:"Product Details"}),e.jsx(P,{value:"description",className:"data-[state=active]:bg-background",children:"Description"}),e.jsx(P,{value:"pricing",className:"data-[state=active]:bg-background",children:"Pricing & Inventory"}),e.jsx(P,{value:"shipping",className:"data-[state=active]:bg-background",children:"Shipping"})]}),e.jsx(A,{value:"basic",className:"space-y-6",children:e.jsxs("div",{className:"grid grid-cols-2 gap-6",children:[e.jsxs("div",{className:"space-y-3",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx(r,{htmlFor:"id",children:"Product ID"}),e.jsx(c,{id:"id",...o("id"),disabled:!0,className:"bg-muted"})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsxs(r,{htmlFor:"name",children:["Product Name ",e.jsx("span",{className:"text-destructive",children:"*"})]}),e.jsx(c,{id:"name",...o("name",{required:"Product name is required"}),placeholder:"Enter product name",className:t.name?"border-destructive":""}),t.name&&e.jsx("p",{className:"text-sm text-destructive",children:t.name.message})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(r,{htmlFor:"sku",children:"SKU"}),e.jsx(c,{id:"sku",...o("sku"),placeholder:"Enter SKU (optional)",className:t.sku?"border-destructive":""}),t.sku&&e.jsx("p",{className:"text-sm text-destructive",children:t.sku.message})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsxs(r,{htmlFor:"partArticleNumber",children:["Part Article Number ",e.jsx("span",{className:"text-destructive",children:"*"})]}),e.jsx(c,{id:"partArticleNumber",...o("partArticleNumber",{required:"Part Article Number is required"}),placeholder:"Enter part article number",className:t.partArticleNumber?"border-destructive":""}),t.partArticleNumber&&e.jsx("p",{className:"text-sm text-destructive",children:t.partArticleNumber.message})]})]}),e.jsxs("div",{className:"space-y-3",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsxs(r,{htmlFor:"category",children:["Category ",e.jsx("span",{className:"text-destructive",children:"*"})]}),e.jsxs(K,{value:se,onValueChange:l=>z("category",l),children:[e.jsx(L,{id:"category",className:t.category?"border-destructive":"",children:e.jsx(W,{placeholder:"Select category"})}),e.jsx(_,{children:h.map(l=>e.jsx(s,{value:l.id,children:l.name},l.id))})]}),t.category&&e.jsx("p",{className:"text-sm text-destructive",children:t.category.message})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsxs(r,{htmlFor:"subcategory",children:["Subcategory ",e.jsx("span",{className:"text-destructive",children:"*"})]}),e.jsxs(K,{value:$,onValueChange:l=>z("subcategory",l),children:[e.jsx(L,{id:"subcategory",className:t.subcategory?"border-destructive":"",children:e.jsx(W,{placeholder:"Select subcategory"})}),e.jsxs(_,{children:[e.jsx(s,{value:"Vacuum Brake Booster",children:"Vacuum Brake Booster"}),e.jsx(s,{value:"Vacuum Pump",children:"Vacuum Pump"}),e.jsx(s,{value:"Brake Force Distributor",children:"Brake Force Distributor"}),e.jsx(s,{value:"Master Brake Cylinder",children:"Master Brake Cylinder"}),e.jsx(s,{value:"Repair Kit, Brake Master Cylinder",children:"Repair Kit, Brake Master Cylinder"}),e.jsx(s,{value:"Brake Fluid Reservoir",children:"Brake Fluid Reservoir"}),e.jsx(s,{value:"Brake Cylinder",children:"Brake Cylinder"}),e.jsx(s,{value:"Repair Kit, Wheel Brake Cylinder",children:"Repair Kit, Wheel Brake Cylinder"}),e.jsx(s,{value:"Brake Pads",children:"Brake Pads"}),e.jsx(s,{value:"Brake Pad Wear Indicator",children:"Brake Pad Wear Indicator"}),e.jsx(s,{value:"Accessory Kit, Disc Brake Pad",children:"Accessory Kit, Disc Brake Pad"}),e.jsx(s,{value:"High Performance Brake Caliper",children:"High Performance Brake Caliper"}),e.jsx(s,{value:"Drum Brake",children:"Drum Brake"}),e.jsx(s,{value:"Brake Shoes",children:"Brake Shoes"}),e.jsx(s,{value:"Accessories, Brake Shoe",children:"Accessories, Brake Shoe"}),e.jsx(s,{value:"Brake Drum",children:"Brake Drum"}),e.jsx(s,{value:"Brake Disc, Drum Brake",children:"Brake Disc, Drum Brake"}),e.jsx(s,{value:"Drum Brake Regulator",children:"Drum Brake Regulator"}),e.jsx(s,{value:"Brake Discs",children:"Brake Discs"}),e.jsx(s,{value:"Brake Discs and Pads",children:"Brake Discs and Pads"}),e.jsx(s,{value:"Anchor Plate",children:"Anchor Plate"}),e.jsx(s,{value:"High Performance Brake Disc",children:"High Performance Brake Disc"}),e.jsx(s,{value:"Handbrake",children:"Handbrake"}),e.jsx(s,{value:"Handbrake Cable",children:"Handbrake Cable"}),e.jsx(s,{value:"Handbrake Shoes",children:"Handbrake Shoes"}),e.jsx(s,{value:"Repair Kit, Handbrake Axle",children:"Repair Kit, Handbrake Axle"}),e.jsx(s,{value:"Switch, Handbrake Warning Light",children:"Switch, Handbrake Warning Light"}),e.jsx(s,{value:"Brake Hose",children:"Brake Hose"}),e.jsx(s,{value:"Brake Fluid",children:"Brake Fluid"}),e.jsx(s,{value:"Brake Line",children:"Brake Line"}),e.jsx(s,{value:"Vacuum Hose, Brake System",children:"Vacuum Hose, Brake System"}),e.jsx(s,{value:"Pressure Accumulator, Brake System",children:"Pressure Accumulator, Brake System"}),e.jsx(s,{value:"Pressure Switch, Hydraulic Brake System",children:"Pressure Switch, Hydraulic Brake System"}),e.jsx(s,{value:"ABS Sensor",children:"ABS Sensor"}),e.jsx(s,{value:"ABS Ring",children:"ABS Ring"}),e.jsx(s,{value:"ABS Pump",children:"ABS Pump"}),e.jsx(s,{value:"Overvoltage Relay, ABS",children:"Overvoltage Relay, ABS"}),e.jsx(s,{value:"ESP Sensor",children:"ESP Sensor"}),e.jsx(s,{value:"Control Unit, Brake/Vehicle Dynamics",children:"Control Unit, Brake/Vehicle Dynamics"}),e.jsx(s,{value:"Brake Caliper",children:"Brake Caliper"}),e.jsx(s,{value:"Brake Caliper Repair Kit",children:"Brake Caliper Repair Kit"}),e.jsx(s,{value:"Guide Sleeve Set, Brake Caliper",children:"Guide Sleeve Set, Brake Caliper"}),e.jsx(s,{value:"Brake Caliper Holder",children:"Brake Caliper Holder"}),e.jsx(s,{value:"Piston, Caliper",children:"Piston, Caliper"}),e.jsx(s,{value:"Brake Disc Screw",children:"Brake Disc Screw"}),e.jsx(s,{value:"Brake Light Switch",children:"Brake Light Switch"}),e.jsx(s,{value:"Threadlockers",children:"Threadlockers"}),e.jsx(s,{value:"Multifunctional Lubricant",children:"Multifunctional Lubricant"}),e.jsx(s,{value:"Mounting Paste",children:"Mounting Paste"}),e.jsx(s,{value:"Spray Grease",children:"Spray Grease"}),e.jsx(s,{value:"Brake Caliper Paint",children:"Brake Caliper Paint"}),e.jsx(s,{value:"Brake Tools",children:"Brake Tools"})]})]}),t.subcategory&&e.jsx("p",{className:"text-sm text-destructive",children:t.subcategory.message})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsxs(r,{htmlFor:"status",children:["Status ",e.jsx("span",{className:"text-destructive",children:"*"})]}),e.jsxs(K,{value:i("status"),onValueChange:l=>z("status",l),children:[e.jsx(L,{id:"status",className:t.status?"border-destructive":"",children:e.jsx(W,{placeholder:"Select status"})}),e.jsxs(_,{children:[e.jsx(s,{value:"active",children:"Active"}),e.jsx(s,{value:"draft",children:"Draft"}),e.jsx(s,{value:"pending_approval",children:"Pending Approval"}),e.jsx(s,{value:"out_of_stock",children:"Out of Stock"}),e.jsx(s,{value:"discontinued",children:"Discontinued"})]})]}),t.status&&e.jsx("p",{className:"text-sm text-destructive",children:t.status.message})]})]})]})}),e.jsx(A,{value:"images",className:"space-y-6",children:e.jsxs("div",{className:"grid grid-cols-2 gap-6",children:[e.jsx("div",{className:"space-y-4",children:e.jsxs("div",{className:"space-y-2",children:[e.jsxs(r,{children:["Primary Product Image ",e.jsx("span",{className:"text-destructive",children:"*"})]}),e.jsx(X,{children:e.jsx(Z,{className:"p-4",children:e.jsx(Se,{value:Q,onChange:S,className:"w-full h-40 border-2 border-dashed border-muted-foreground/25 rounded-md"})})})]})}),e.jsx("div",{className:"space-y-4",children:e.jsxs("div",{className:"space-y-2",children:[e.jsx(r,{children:"Additional Product Images"}),e.jsx(X,{children:e.jsx(Z,{className:"p-4",children:e.jsx(Be,{value:C,onChange:B,className:"w-full min-h-40 border-2 border-dashed border-muted-foreground/25 rounded-md"})})})]})})]})}),e.jsx(A,{value:"details",className:"space-y-6",children:e.jsxs("div",{className:"grid grid-cols-2 gap-6",children:[e.jsxs("div",{className:"space-y-3",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsxs(r,{htmlFor:"manufacturer",children:["Manufacturer ",e.jsx("span",{className:"text-destructive",children:"*"})]}),e.jsx(c,{id:"manufacturer",...o("manufacturer",{required:"Manufacturer is required"}),placeholder:"Enter manufacturer",className:t.manufacturer?"border-destructive":""}),t.manufacturer&&e.jsx("p",{className:"text-sm text-destructive",children:t.manufacturer.message})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(r,{htmlFor:"supplierName",children:"Supplier Name"}),e.jsx(c,{id:"supplierName",...o("supplierName"),placeholder:"Enter supplier name"})]})]}),e.jsx("div",{className:"space-y-3",children:e.jsx(Ce,{value:H,onChange:x,label:"Vehicle Type Compatibility",description:"Select compatible vehicles from the compatibility hub database."})})]})}),e.jsx(A,{value:"description",className:"space-y-6",children:O?e.jsx(Ae,{onDescriptionGenerated:ie,initialDescription:i("descriptionAndSpecifications")}):e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs(r,{htmlFor:"descriptionAndSpecifications",children:["Product Description and Specifications ",e.jsx("span",{className:"text-destructive",children:"*"})]}),e.jsxs(k,{type:"button",variant:"outline",size:"sm",onClick:()=>V(!0),className:"gap-1",children:[e.jsx(he,{className:"h-4 w-4"})," Generate with AI"]})]}),e.jsx(me,{id:"descriptionAndSpecifications",...o("descriptionAndSpecifications",{required:"Description and specifications are required"}),placeholder:"Enter a detailed, structured description of the product including technical specifications and compatibility information.",className:`min-h-[300px] ${t.descriptionAndSpecifications?"border-destructive":""}`}),t.descriptionAndSpecifications&&e.jsx("p",{className:"text-sm text-destructive",children:t.descriptionAndSpecifications.message}),e.jsx("p",{className:"text-sm text-muted-foreground",children:"For brake products, please include detailed technical specifications in a structured format. Use markdown formatting for better readability (e.g., ## Technical Specifications)."})]})}),e.jsx(A,{value:"pricing",className:"space-y-6",children:e.jsxs("div",{className:"grid grid-cols-2 gap-6",children:[e.jsxs("div",{className:"space-y-3",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx(r,{htmlFor:"retailPrice",children:"Retail Price"}),e.jsx(c,{id:"retailPrice",type:"number",step:"0.01",...o("retailPrice",{valueAsNumber:!0}),placeholder:"0.00"})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsxs(r,{htmlFor:"stockQuantity",children:["Stock Quantity ",e.jsx("span",{className:"text-destructive",children:"*"})]}),e.jsx(c,{id:"stockQuantity",type:"number",...o("stockQuantity",{required:"Stock quantity is required",valueAsNumber:!0}),placeholder:"0",className:t.stockQuantity?"border-destructive":""}),t.stockQuantity&&e.jsx("p",{className:"text-sm text-destructive",children:t.stockQuantity.message})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(r,{htmlFor:"minimumOrderQuantity",children:"Minimum Order Quantity"}),e.jsx(c,{id:"minimumOrderQuantity",type:"number",...o("minimumOrderQuantity",{valueAsNumber:!0}),placeholder:"1"})]}),e.jsxs("div",{className:"flex items-center space-x-2 pt-2",children:[e.jsx(ye,{id:"quotationRequestEnabled",checked:i("quotationRequestEnabled"),onCheckedChange:l=>z("quotationRequestEnabled",!!l)}),e.jsx(r,{htmlFor:"quotationRequestEnabled",className:"cursor-pointer",children:"Enable Quotation Requests"})]})]}),e.jsx("div",{className:"space-y-3",children:e.jsxs("div",{className:"space-y-2",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx(r,{children:"Wholesale Pricing Tiers"}),e.jsxs(k,{type:"button",onClick:ae,size:"sm",variant:"outline",className:"gap-1",children:[e.jsx(be,{className:"h-4 w-4"})," Add Tier"]})]}),w.length>0?e.jsx("div",{className:"space-y-3",children:w.map((l,v)=>e.jsxs("div",{className:"flex items-end gap-2 border p-2 rounded-md",children:[e.jsxs("div",{className:"space-y-1 flex-1",children:[e.jsx(r,{className:"text-xs",children:"Min Quantity"}),e.jsx(c,{type:"number",value:l.minQuantity,onChange:R=>J(v,"minQuantity",parseInt(R.target.value)),placeholder:"1",className:"h-8"})]}),e.jsxs("div",{className:"space-y-1 flex-1",children:[e.jsx(r,{className:"text-xs",children:"Max Quantity"}),e.jsx(c,{type:"number",value:l.maxQuantity||"",onChange:R=>J(v,"maxQuantity",R.target.value?parseInt(R.target.value):void 0),placeholder:"Optional",className:"h-8"})]}),e.jsxs("div",{className:"space-y-1 flex-1",children:[e.jsx(r,{className:"text-xs",children:"Price"}),e.jsx(c,{type:"number",step:"0.01",value:l.price,onChange:R=>J(v,"price",parseFloat(R.target.value)),placeholder:"0.00",className:"h-8"})]}),e.jsx(k,{type:"button",variant:"ghost",size:"sm",onClick:()=>re(v),className:"h-8 w-8 p-0",children:e.jsx(Ne,{className:"h-4 w-4"})})]},v))}):e.jsx("div",{className:"border border-dashed rounded-md p-4 text-center text-muted-foreground text-sm",children:"No pricing tiers added yet"})]})})]})}),e.jsx(A,{value:"shipping",className:"space-y-6",children:e.jsx("div",{className:"grid grid-cols-2 gap-6",children:e.jsx("div",{className:"space-y-3",children:e.jsxs("div",{className:"space-y-2",children:[e.jsxs(r,{htmlFor:"shippingOrigin",children:["Shipping Origin ",e.jsx("span",{className:"text-destructive",children:"*"})]}),e.jsx(c,{id:"shippingOrigin",...o("shippingOrigin",{required:"Shipping origin is required"}),placeholder:"Enter shipping origin (country)",className:t.shippingOrigin?"border-destructive":""}),t.shippingOrigin&&e.jsx("p",{className:"text-sm text-destructive",children:t.shippingOrigin.message})]})})})})]}),e.jsxs(fe,{children:[e.jsx(k,{type:"button",variant:"outline",onClick:m,disabled:y,children:"Cancel"}),e.jsxs(k,{type:"submit",disabled:y,children:[y&&e.jsx(ee,{className:"mr-2 h-4 w-4 animate-spin"}),"Update Product"]})]})]})]})})},es=({isOpen:j,onClose:m,onSave:p,categories:a,initialBarcode:h="",categoryId:y})=>y==="all-other-categories"?e.jsx(De,{isOpen:j,onClose:m,onSave:p,initialBarcode:h,defaultCategory:"brakes"}):y==="brakes"?e.jsx(_e,{isOpen:j,onClose:m,onSave:p,categories:a,initialBarcode:h}):e.jsx(ke,{isOpen:j,onClose:m,onSave:p,categories:a,initialBarcode:h}),ss=({isOpen:j,onClose:m,onSave:p,product:a,categories:h})=>a.category==="brakes"?e.jsx(we,{isOpen:j,onClose:m,onSave:p,product:a,categories:h}):e.jsx(Pe,{isOpen:j,onClose:m,onSave:p,product:a,categories:h}),as=({isOpen:j,onClose:m,onSave:p,initialProduct:a,isEditMode:h,categories:y})=>{const{t:b}=Oe(),[D,g]=d.useState(a?.category||"tyres"),[Q,S]=d.useState(!!a?.category||!h);d.useEffect(()=>{a?.category&&(g(a.category),S(!0))},[a]);const C=B=>{g(B),S(!0)};return h&&a?a.category==="brakes"?e.jsx(we,{isOpen:j,onClose:m,onSave:p,product:a,categories:y}):e.jsx(Pe,{isOpen:j,onClose:m,onSave:p,product:a,categories:y}):Q?D==="tyres"?e.jsx(ke,{isOpen:j,onClose:m,onSave:p,categories:y,initialBarcode:a?.barcode||"",initialCategory:D}):e.jsx(De,{isOpen:j,onClose:m,onSave:p,initialBarcode:a?.barcode||"",defaultCategory:D||"brakes"}):e.jsx(X,{className:"bg-white rounded-lg shadow-sm",children:e.jsxs(Z,{className:"p-6",children:[e.jsx("h2",{className:"text-xl font-semibold mb-4",children:b("products.selectCategory")}),e.jsx("p",{className:"text-muted-foreground mb-6",children:b("products.selectCategoryDescription","Select a product category to continue")}),e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:y.map(B=>e.jsx(k,{variant:"outline",className:"h-auto p-6 flex flex-col items-center justify-center gap-2 hover:bg-muted/50",onClick:()=>C(B.id),children:e.jsx("span",{className:"text-lg font-medium",children:B.name})},B.id))})]})})};export{We as I,es as P,as as U,ss as a};
