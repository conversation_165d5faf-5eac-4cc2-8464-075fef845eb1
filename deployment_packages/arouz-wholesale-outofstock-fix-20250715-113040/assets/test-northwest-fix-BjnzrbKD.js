import{r as n,j as e,k as h,l as B,U as L,m as g,I as N,B as j,n as R,aN as f,aQ as b,ab as S,aI as I,C as k}from"./index-DRgoPWv8.js";import{T as F}from"./target-Dl3X5brR.js";function D(){const[l,o]=n.useState([]),[d,i]=n.useState(!1),[m,C]=n.useState("34.834096"),[x,y]=n.useState("-1.670609"),T=[{name:"🚨 CRITICAL: User reported coordinate",lat:34.834096,lng:-1.670609,expected:"Tlemcen",description:"The exact coordinate from user's screenshot"},{name:"Tlemcen center verification",lat:34.8786,lng:-1.315,expected:"Tlemcen",description:"Should definitely be Tlemcen (center)"},{name:"Sidi Bel Abbès center",lat:35.1977,lng:-.6388,expected:"Sidi Bel Abbès",description:"Should definitely be Sidi Bel Abbès"},{name:"Border test: West of Tlemcen",lat:34.8,lng:-2,expected:"Tlemcen or Naâma",description:"Far west border area"},{name:"Border test: Between Tlemcen and Sidi Bel Abbès",lat:35,lng:-1,expected:"Tlemcen or Sidi Bel Abbès",description:"Border area between wilayas"}],p=async()=>{i(!0);const s=[];for(const t of T){const a=f(t.lat,t.lng),c=await b(t.lat,t.lng);let r="unknown";t.expected.includes("or")?r=t.expected.split(" or ").some(u=>a?.name===u||c?.name===u)?"pass":"fail":r=a?.name===t.expected&&c?.name===t.expected?"pass":"fail",s.push({coordinates:{lat:t.lat,lng:t.lng},accurate:a,precise:c,testName:t.name,expected:t.expected,status:r})}o(s),i(!1)},w=async()=>{const s=parseFloat(m),t=parseFloat(x);if(isNaN(s)||isNaN(t)){alert("Please enter valid coordinates");return}i(!0);const a=f(s,t),c=await b(s,t);o([{coordinates:{lat:s,lng:t},accurate:a,precise:c,testName:"Custom coordinate test",expected:"Unknown",status:"unknown"},...l]),i(!1)},v=s=>{switch(s){case"pass":return e.jsx(k,{className:"h-4 w-4 text-green-500"});case"fail":return e.jsx(I,{className:"h-4 w-4 text-red-500"});default:return e.jsx(S,{className:"h-4 w-4 text-yellow-500"})}},A=s=>{switch(s){case"pass":return"bg-green-100 text-green-800";case"fail":return"bg-red-100 text-red-800";default:return"bg-yellow-100 text-yellow-800"}};return n.useEffect(()=>{p()},[]),e.jsx("div",{className:"max-w-6xl mx-auto p-6 space-y-6",children:e.jsxs(h,{children:[e.jsxs(B,{children:[e.jsxs(L,{className:"flex items-center gap-2",children:[e.jsx(F,{className:"h-5 w-5 text-orange-500"}),"Northwest Border Fix Verification"]}),e.jsx("p",{className:"text-sm text-gray-600",children:"Testing the fix for coordinate 34.834096, -1.670609 that was incorrectly detected"})]}),e.jsxs(g,{className:"space-y-6",children:[e.jsxs("div",{className:"flex flex-wrap gap-4 items-end",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"text-sm font-medium",children:"Latitude"}),e.jsx(N,{value:m,onChange:s=>C(s.target.value),placeholder:"34.834096",className:"w-32"})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"text-sm font-medium",children:"Longitude"}),e.jsx(N,{value:x,onChange:s=>y(s.target.value),placeholder:"-1.670609",className:"w-32"})]}),e.jsx(j,{onClick:w,disabled:d,children:"Test Custom"}),e.jsx(j,{onClick:p,disabled:d,variant:"outline",children:d?"Testing...":"Run All Tests"})]}),l.length>0&&e.jsxs("div",{className:"space-y-4",children:[e.jsx("h3",{className:"font-semibold",children:"Test Results:"}),l.map((s,t)=>e.jsx(h,{className:"border-l-4 border-l-orange-500",children:e.jsx(g,{className:"pt-4",children:e.jsxs("div",{className:"space-y-3",children:[e.jsx("div",{className:"flex items-center justify-between",children:e.jsxs("div",{className:"flex items-center gap-2",children:[v(s.status),e.jsx("span",{className:"font-medium",children:s.testName}),e.jsx(R,{className:A(s.status),children:s.status.toUpperCase()})]})}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 text-sm",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsxs("p",{children:[e.jsx("strong",{children:"Coordinates:"})," ",s.coordinates.lat,", ",s.coordinates.lng]}),e.jsxs("p",{children:[e.jsx("strong",{children:"Expected:"})," ",s.expected]}),e.jsxs("div",{className:"bg-blue-50 p-3 rounded",children:[e.jsx("p",{className:"font-medium text-blue-800",children:"Accurate Detection:"}),e.jsxs("p",{children:["Wilaya: ",s.accurate?.name||"NULL"," (",s.accurate?.name_ar||"N/A",")"]}),e.jsxs("p",{children:["Confidence: ",((s.accurate?.confidence||0)*100).toFixed(1),"%"]})]})]}),e.jsx("div",{className:"space-y-2",children:e.jsxs("div",{className:"bg-green-50 p-3 rounded",children:[e.jsx("p",{className:"font-medium text-green-800",children:"Precise Detection:"}),e.jsxs("p",{children:["Wilaya: ",s.precise?.name||"NULL"," (",s.precise?.name_ar||"N/A",")"]}),e.jsxs("p",{children:["Confidence: ",((s.precise?.confidence||0)*100).toFixed(1),"%"]}),e.jsxs("p",{children:["Method: ",s.precise?.method||"N/A"]}),e.jsxs("p",{children:["Accuracy: ",s.precise?.accuracy||"N/A"]})]})})]}),s.testName.includes("CRITICAL")&&e.jsxs("div",{className:"bg-orange-50 p-3 rounded border-l-4 border-l-orange-500",children:[e.jsx("p",{className:"font-medium text-orange-800",children:"🔍 Critical Analysis:"}),e.jsxs("p",{className:"text-sm",children:["This is the exact coordinate from your screenshot.",s.accurate?.name==="Tlemcen"&&s.precise?.name==="Tlemcen"?" ✅ Both systems now correctly detect it as Tlemcen.":" ❌ Detection mismatch - needs further investigation."]})]})]})})},t))]})]})]})})}export{D as default};
