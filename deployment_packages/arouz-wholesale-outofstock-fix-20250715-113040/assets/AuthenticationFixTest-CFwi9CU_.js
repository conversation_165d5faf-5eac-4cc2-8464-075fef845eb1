import{r as a,a as S,a7 as b,j as e,k as g,l as x,U as p,B as T,a6 as f,m as j,n as I,bm as R,cd as v,c9 as E,cb as U,ab as k,aI as $,C as F}from"./index-DRgoPWv8.js";function P(){const[C,w]=a.useState([]),[i,o]=a.useState(!1),{toast:A}=S(),{isAuthenticated:r,user:c}=b(),l=async()=>{o(!0);const s=[];try{console.log("🧪 Testing admin authentication isolation...");const t=await R(),d=await v();!t.data.user&&!d.data.session?s.push({name:"Admin Authentication Isolation",status:"pass",message:"Admin authentication correctly returns null when no admin is logged in",details:"getCurrentUser and getCurrentSession properly isolated from consumer auth"}):s.push({name:"Admin Authentication Isolation",status:"fail",message:"Admin authentication incorrectly returning user/session data",details:`User: ${!!t.data.user}, Session: ${!!d.data.session}`}),console.log("🧪 Testing consumer authentication independence...");const m=E();m?s.push({name:"Consumer Authentication Independence",status:"pass",message:"Consumer authentication working independently",details:`Consumer: ${m.profile.full_name||"Unknown"}`}):s.push({name:"Consumer Authentication Independence",status:"warning",message:"No consumer session found (expected if no consumer is logged in)",details:"This is normal if no consumer is currently authenticated"}),console.log("🧪 Testing AuthContext state..."),s.push({name:"AuthContext State",status:r?"warning":"pass",message:r?"AuthContext shows authenticated (admin user logged in)":"AuthContext correctly shows not authenticated",details:`User ID: ${c?.id||"None"}, Email: ${c?.email||"None"}`}),console.log("🧪 Testing consumer logout functionality...");try{U(),s.push({name:"Consumer Logout Function",status:"pass",message:"Consumer logout function executes without errors",details:"logoutConsumer() completed successfully"})}catch(h){s.push({name:"Consumer Logout Function",status:"fail",message:"Consumer logout function threw an error",details:`Error: ${h instanceof Error?h.message:"Unknown error"}`})}}catch(t){s.push({name:"Test Execution",status:"fail",message:"Error during test execution",details:t instanceof Error?t.message:"Unknown error"})}w(s),o(!1);const n=s.filter(t=>t.status==="pass").length,u=s.filter(t=>t.status==="fail").length;A({title:"Authentication Tests Complete",description:`${n} passed, ${u} failed`,variant:u>0?"destructive":"default"})},N=s=>{switch(s){case"pass":return e.jsx(F,{className:"h-5 w-5 text-green-500"});case"fail":return e.jsx($,{className:"h-5 w-5 text-red-500"});case"warning":return e.jsx(k,{className:"h-5 w-5 text-yellow-500"})}},y=s=>{switch(s){case"pass":return"bg-green-100 text-green-800";case"fail":return"bg-red-100 text-red-800";case"warning":return"bg-yellow-100 text-yellow-800"}};return a.useEffect(()=>{l()},[]),e.jsxs("div",{className:"container mx-auto p-6 space-y-6",children:[e.jsxs("div",{className:"text-center",children:[e.jsx("h1",{className:"text-3xl font-bold mb-2",children:"Authentication Fix Verification"}),e.jsx("p",{className:"text-gray-600",children:"Testing consumer logout and admin authentication isolation fixes"})]}),e.jsxs(g,{children:[e.jsx(x,{children:e.jsxs(p,{className:"flex items-center justify-between",children:["Test Results",e.jsx(T,{onClick:l,disabled:i,variant:"outline",size:"sm",children:i?e.jsxs(e.Fragment,{children:[e.jsx(f,{className:"h-4 w-4 mr-2 animate-spin"}),"Running..."]}):e.jsxs(e.Fragment,{children:[e.jsx(f,{className:"h-4 w-4 mr-2"}),"Re-run Tests"]})})]})}),e.jsx(j,{children:e.jsx("div",{className:"space-y-4",children:C.map((s,n)=>e.jsxs("div",{className:"border rounded-lg p-4",children:[e.jsxs("div",{className:"flex items-center justify-between mb-2",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[N(s.status),e.jsx("h3",{className:"font-semibold",children:s.name})]}),e.jsx(I,{className:y(s.status),children:s.status.toUpperCase()})]}),e.jsx("p",{className:"text-gray-700 mb-2",children:s.message}),s.details&&e.jsx("p",{className:"text-sm text-gray-500 bg-gray-50 p-2 rounded",children:s.details})]},n))})})]}),e.jsxs(g,{children:[e.jsx(x,{children:e.jsx(p,{children:"Expected Results"})}),e.jsx(j,{children:e.jsxs("div",{className:"space-y-2 text-sm",children:[e.jsxs("p",{children:[e.jsx("strong",{children:"Admin Authentication Isolation:"})," Should PASS - No admin user/session when not logged in"]}),e.jsxs("p",{children:[e.jsx("strong",{children:"Consumer Authentication Independence:"})," Should PASS or WARNING - Consumer auth works separately"]}),e.jsxs("p",{children:[e.jsx("strong",{children:"AuthContext State:"})," Should PASS when no admin logged in"]}),e.jsxs("p",{children:[e.jsx("strong",{children:"Consumer Logout Function:"})," Should PASS - No errors when calling logoutConsumer()"]})]})})]})]})}export{P as default};
