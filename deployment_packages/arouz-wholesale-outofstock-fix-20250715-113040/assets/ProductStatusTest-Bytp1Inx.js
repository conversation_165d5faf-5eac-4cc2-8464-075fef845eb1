import{r as _,v as N,j as e,B as S,k as l,l as i,U as a,m as r,n as c}from"./index-DRgoPWv8.js";function D(){const[n,f]=_.useState(!1),{products:b,isLoading:P}=N("tyres"),{products:w,isLoading:y}=N("brakes"),t=[...b,...w],p=t.filter(s=>s.status==="active"),d=t.filter(s=>s.status==="draft"),k=t.filter(s=>s.status==="out_of_stock"),o=t.filter(s=>s.status==="discontinued"),m=t.filter(s=>s.wholesalePricingTiers&&s.wholesalePricingTiers.length>0),g=t.filter(s=>s.retailPrice&&s.retailPrice>0),h=t.filter(s=>{const j=s.wholesalePricingTiers&&s.wholesalePricingTiers.length>0,u=s.status==="active"||s.status==="out_of_stock";return j&&u}),x=t.filter(s=>{const j=s.retailPrice&&s.retailPrice>0,u=s.status==="active"||s.status==="out_of_stock";return j&&u}),T=t.map(s=>({id:s.id,name:s.name,status:s.status,retailPrice:s.retailPrice,hasWholesalePricing:s.wholesalePricingTiers&&s.wholesalePricingTiers.length>0,wholesalePricingTiers:s.wholesalePricingTiers,marketplaceSection:s.marketplaceSection,category:s.category})),v=t.filter(s=>(!s.retailPrice||s.retailPrice<=0)&&(!s.wholesalePricingTiers||s.wholesalePricingTiers.length===0));return P||y?e.jsx("div",{children:"Loading products..."}):e.jsxs("div",{className:"p-6 space-y-6",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("h2",{className:"text-2xl font-bold",children:"Product Status & Visibility Test"}),e.jsx(S,{variant:"outline",onClick:()=>f(!n),children:n?"Hide Details":"Show Details"})]}),e.jsxs("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:[e.jsxs(l,{children:[e.jsx(i,{className:"pb-2",children:e.jsx(a,{className:"text-sm",children:"Total Products"})}),e.jsx(r,{children:e.jsx("div",{className:"text-2xl font-bold",children:t.length})})]}),e.jsxs(l,{children:[e.jsx(i,{className:"pb-2",children:e.jsx(a,{className:"text-sm",children:"Wholesale Products"})}),e.jsxs(r,{children:[e.jsx("div",{className:"text-2xl font-bold text-blue-600",children:m.length}),e.jsxs("div",{className:"text-xs text-muted-foreground",children:["Visible: ",h.length]})]})]}),e.jsxs(l,{children:[e.jsx(i,{className:"pb-2",children:e.jsx(a,{className:"text-sm",children:"Retail Products"})}),e.jsxs(r,{children:[e.jsx("div",{className:"text-2xl font-bold text-green-600",children:g.length}),e.jsxs("div",{className:"text-xs text-muted-foreground",children:["Visible: ",x.length]})]})]}),e.jsxs(l,{children:[e.jsx(i,{className:"pb-2",children:e.jsx(a,{className:"text-sm",children:"Hidden Products"})}),e.jsxs(r,{children:[e.jsx("div",{className:"text-2xl font-bold text-red-600",children:d.length+o.length}),e.jsxs("div",{className:"text-xs text-muted-foreground",children:["Draft: ",d.length,", Discontinued: ",o.length]})]})]})]}),e.jsxs(l,{children:[e.jsx(i,{children:e.jsx(a,{children:"Products by Status"})}),e.jsx(r,{children:e.jsxs("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:[e.jsxs("div",{className:"text-center",children:[e.jsx(c,{className:"bg-green-100 text-green-800",children:"Active"}),e.jsx("div",{className:"text-lg font-semibold mt-1",children:p.length})]}),e.jsxs("div",{className:"text-center",children:[e.jsx(c,{className:"bg-gray-100 text-gray-800",children:"Draft"}),e.jsx("div",{className:"text-lg font-semibold mt-1",children:d.length})]}),e.jsxs("div",{className:"text-center",children:[e.jsx(c,{className:"bg-red-100 text-red-800",children:"Out of Stock"}),e.jsx("div",{className:"text-lg font-semibold mt-1",children:k.length})]}),e.jsxs("div",{className:"text-center",children:[e.jsx(c,{className:"bg-purple-100 text-purple-800",children:"Discontinued"}),e.jsx("div",{className:"text-lg font-semibold mt-1",children:o.length})]})]})})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[e.jsxs(l,{children:[e.jsxs(i,{children:[e.jsx(a,{className:"text-blue-600",children:"Wholesale Marketplace"}),e.jsx("p",{className:"text-sm text-muted-foreground",children:"Products visible in /wholesale-offers (must have pricing tiers + active/out_of_stock status)"})]}),e.jsxs(r,{children:[e.jsxs("div",{className:"text-2xl font-bold mb-2",children:[h.length," visible"]}),n&&e.jsx("div",{className:"space-y-2 max-h-40 overflow-y-auto",children:h.map(s=>e.jsxs("div",{className:"text-sm border-l-2 border-blue-200 pl-2",children:[e.jsx("div",{className:"font-medium",children:s.name}),e.jsxs("div",{className:"text-xs text-muted-foreground",children:["Status: ",s.status," | Tiers: ",s.wholesalePricingTiers?.length||0]})]},s.id))})]})]}),e.jsxs(l,{children:[e.jsxs(i,{children:[e.jsx(a,{className:"text-green-600",children:"Retail Marketplace"}),e.jsx("p",{className:"text-sm text-muted-foreground",children:"Products visible in /my-vehicle-parts (must have retail price + active/out_of_stock status)"})]}),e.jsxs(r,{children:[e.jsxs("div",{className:"text-2xl font-bold mb-2",children:[x.length," visible"]}),n&&e.jsx("div",{className:"space-y-2 max-h-40 overflow-y-auto",children:x.map(s=>e.jsxs("div",{className:"text-sm border-l-2 border-green-200 pl-2",children:[e.jsx("div",{className:"font-medium",children:s.name}),e.jsxs("div",{className:"text-xs text-muted-foreground",children:["Status: ",s.status," | Price: ",s.retailPrice||0," DA"]})]},s.id))})]})]})]}),e.jsxs(l,{className:"border-yellow-200 bg-yellow-50",children:[e.jsx(i,{children:e.jsx(a,{className:"text-yellow-800",children:"Potential Issues"})}),e.jsx(r,{children:e.jsxs("div",{className:"space-y-2 text-sm",children:[m.length===0&&e.jsx("div",{className:"text-yellow-700",children:"⚠️ No products have wholesale pricing tiers - they won't appear in wholesale marketplace"}),h.length===0&&m.length>0&&e.jsx("div",{className:"text-yellow-700",children:"⚠️ Products have wholesale pricing but wrong status - check product status settings"}),d.length>0&&e.jsxs("div",{className:"text-yellow-700",children:["ℹ️ ",d.length," products are in draft status (hidden from marketplace)"]}),o.length>0&&e.jsxs("div",{className:"text-yellow-700",children:["ℹ️ ",o.length," products are discontinued (hidden from marketplace)"]}),v.length>0&&e.jsxs("div",{className:"text-red-700",children:["🚨 ",v.length," products have no pricing information (neither retail price nor wholesale tiers)"]}),g.length===0&&e.jsx("div",{className:"text-red-700",children:"🚨 No products have retail pricing - they won't appear in retail marketplace"}),x.length===0&&g.length>0&&e.jsx("div",{className:"text-yellow-700",children:"⚠️ Products have retail pricing but wrong status - check product status settings"})]})})]}),n&&e.jsxs(l,{children:[e.jsxs(i,{children:[e.jsx(a,{children:"Detailed Product Information"}),e.jsx("p",{className:"text-sm text-muted-foreground",children:"Debug information for all products showing pricing and marketplace section data"})]}),e.jsx(r,{children:e.jsx("div",{className:"space-y-4 max-h-96 overflow-y-auto",children:T.map(s=>e.jsx("div",{className:"border rounded-lg p-3 bg-gray-50",children:e.jsxs("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-2 text-sm",children:[e.jsxs("div",{children:[e.jsx("strong",{children:"Name:"})," ",s.name]}),e.jsxs("div",{children:[e.jsx("strong",{children:"Status:"}),e.jsx(c,{className:`ml-1 ${s.status==="active"?"bg-green-100 text-green-800":s.status==="draft"?"bg-gray-100 text-gray-800":s.status==="out_of_stock"?"bg-red-100 text-red-800":"bg-purple-100 text-purple-800"}`,children:s.status})]}),e.jsxs("div",{children:[e.jsx("strong",{children:"Retail Price:"})," ",s.retailPrice?`${s.retailPrice} DA`:"None"]}),e.jsxs("div",{children:[e.jsx("strong",{children:"Wholesale Tiers:"})," ",s.hasWholesalePricing?"Yes":"No"]}),e.jsxs("div",{children:[e.jsx("strong",{children:"Category:"})," ",s.category]}),e.jsxs("div",{children:[e.jsx("strong",{children:"Marketplace:"})," ",s.marketplaceSection||"Not set"]}),e.jsxs("div",{className:"col-span-2",children:[e.jsx("strong",{children:"Visible in:"}),s.retailPrice&&s.retailPrice>0&&(s.status==="active"||s.status==="out_of_stock")&&e.jsx(c,{className:"ml-1 bg-green-100 text-green-800",children:"Retail"}),s.hasWholesalePricing&&(s.status==="active"||s.status==="out_of_stock")&&e.jsx(c,{className:"ml-1 bg-blue-100 text-blue-800",children:"Wholesale"}),(!s.retailPrice||s.retailPrice<=0)&&!s.hasWholesalePricing&&e.jsx(c,{className:"ml-1 bg-red-100 text-red-800",children:"None"})]})]})},s.id))})})]})]})}function C(){return e.jsx("div",{className:"min-h-screen bg-gray-50",children:e.jsx("div",{className:"container mx-auto",children:e.jsx(D,{})})})}export{C as ProductStatusTest};
