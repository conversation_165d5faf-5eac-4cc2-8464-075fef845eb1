import{j as e,k as c,aZ as a,l as m,U as o,m as x}from"./index-DRgoPWv8.js";import{T as d}from"./trending-up--T-juoT2.js";import{T as u}from"./trending-down-C7IziEBA.js";function j({title:r,value:i,icon:l,description:t,trend:s,className:n}){return e.jsxs(c,{className:a("stats-card group",n),children:[e.jsxs(m,{className:"flex flex-row items-center justify-between pb-2 space-y-0",children:[e.jsx(o,{className:"text-sm font-medium text-muted-foreground",children:r}),e.jsx("div",{className:"h-9 w-9 rounded-full bg-soft-white flex items-center justify-center text-electric-orange group-hover:scale-110 transition-transform duration-300",children:l})]}),e.jsxs(x,{children:[e.jsx("div",{className:"text-2xl font-bold text-graphite",children:i}),t&&e.jsx("p",{className:"text-xs text-muted-foreground mt-1",children:t}),s&&e.jsxs("div",{className:"flex items-center gap-1 text-xs mt-2 font-medium",children:[e.jsxs("div",{className:a("flex items-center gap-0.5 px-1.5 py-0.5 rounded-full",s.positive?"bg-service-blue/10 text-service-blue":"bg-racing-red/10 text-racing-red"),children:[s.positive?e.jsx(d,{className:"h-3 w-3"}):e.jsx(u,{className:"h-3 w-3"}),e.jsxs("span",{children:[s.positive?"+":"-",Math.abs(s.value),"%"]})]}),e.jsx("span",{className:"text-muted-foreground ml-1",children:"from last month"})]})]})]})}export{j as S};
