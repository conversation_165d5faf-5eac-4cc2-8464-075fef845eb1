-- EMERGENCY FIX: Remove infinite recursion in profiles RLS policies
-- Run this directly in Supabase SQL Editor to fix the recursion error
-- Date: July 8, 2025

-- 1. First, let's see what policies are currently causing the recursion
SELECT 'CURRENT_POLICIES_BEFORE_FIX' as status, schemaname, tablename, policyname, cmd, qual
FROM pg_policies 
WHERE tablename = 'profiles'
ORDER BY policyname;

-- 2. DROP ALL RECURSIVE POLICIES that are causing infinite recursion
-- These policies contain "EXISTS (SELECT 1 FROM profiles..." which causes the recursion
DROP POLICY IF EXISTS "profiles_authenticated_select_admin_merchant_supplier" ON profiles;
DROP POLICY IF EXISTS "profiles_authenticated_update_admin_merchant_supplier" ON profiles;
DROP POLICY IF EXISTS "profiles_authenticated_insert_admin_merchant_supplier" ON profiles;
DROP POLICY IF EXISTS "profiles_admin_emergency_access" ON profiles;

-- 3. CREATE SIMPLE, NON-RECURSIVE POLICIES
-- These policies do NOT reference the profiles table in their conditions

-- Allow service role to do anything (this should already exist but ensure it's there)
CREATE POLICY "service_role_all_access" ON profiles
  FOR ALL
  USING (auth.role() = 'service_role')
  WITH CHECK (auth.role() = 'service_role');

-- Allow authenticated users to view their own profile (simple, no recursion)
CREATE POLICY "users_select_own_profile" ON profiles
  FOR SELECT
  USING (auth.uid() = id);

-- Allow authenticated users to update their own profile (simple, no recursion)
CREATE POLICY "users_update_own_profile" ON profiles
  FOR UPDATE
  USING (auth.uid() = id)
  WITH CHECK (auth.uid() = id);

-- Allow authenticated users to insert their own profile during signup (simple, no recursion)
CREATE POLICY "users_insert_own_profile" ON profiles
  FOR INSERT
  WITH CHECK (auth.uid() = id);

-- 4. CONSUMER ANONYMOUS ACCESS (keep the working consumer policies)
-- Allow anonymous access for consumer profiles (this should already exist)
CREATE POLICY "profiles_anonymous_select_consumer" ON profiles
  FOR SELECT 
  USING (role = 'consumer');

CREATE POLICY "profiles_anonymous_insert_consumer" ON profiles
  FOR INSERT 
  WITH CHECK (role = 'consumer');

CREATE POLICY "profiles_anonymous_update_consumer" ON profiles
  FOR UPDATE 
  USING (role = 'consumer')
  WITH CHECK (role = 'consumer');

-- 5. Add emergency admin access using auth.users table (NO recursion)
-- This checks auth.users directly, not profiles table
CREATE POLICY "admin_emergency_access_via_auth_users" ON profiles
  FOR ALL
  USING (
    EXISTS (
      SELECT 1 FROM auth.users 
      WHERE auth.users.id = auth.uid()
      AND auth.users.email IN (
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>'
      )
    )
  )
  WITH CHECK (
    EXISTS (
      SELECT 1 FROM auth.users 
      WHERE auth.users.id = auth.uid()
      AND auth.users.email IN (
        '<EMAIL>',
        '<EMAIL>', 
        '<EMAIL>'
      )
    )
  );

-- 6. Verify RLS is enabled
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;

-- 7. Add comments for documentation
COMMENT ON POLICY "service_role_all_access" ON profiles IS 
'Allows service role to manage all profiles for system operations';

COMMENT ON POLICY "users_select_own_profile" ON profiles IS 
'Allows authenticated users to view their own profile - NO RECURSION';

COMMENT ON POLICY "users_update_own_profile" ON profiles IS 
'Allows authenticated users to update their own profile - NO RECURSION';

COMMENT ON POLICY "users_insert_own_profile" ON profiles IS 
'Allows authenticated users to create their own profile during signup - NO RECURSION';

COMMENT ON POLICY "profiles_anonymous_select_consumer" ON profiles IS 
'Allows anonymous access to consumer profiles for marketplace functionality';

COMMENT ON POLICY "profiles_anonymous_insert_consumer" ON profiles IS 
'Allows anonymous creation of consumer profiles';

COMMENT ON POLICY "profiles_anonymous_update_consumer" ON profiles IS 
'Allows anonymous updates to consumer profiles';

COMMENT ON POLICY "admin_emergency_access_via_auth_users" ON profiles IS 
'Emergency admin access using auth.users table - NO RECURSION';

-- 8. Test queries to verify everything works WITHOUT recursion
SELECT 'ADMIN_ACCESS_TEST: Admin profiles accessible' as test_result, 
       count(*) as admin_count,
       string_agg(email, ', ') as admin_emails
FROM profiles 
WHERE role IN ('admin', 'merchant', 'supplier') 
LIMIT 10;

SELECT 'CONSUMER_ACCESS_TEST: Consumer profiles accessible' as test_result, 
       count(*) as consumer_count
FROM profiles 
WHERE role = 'consumer' 
LIMIT 5;

-- 9. Show final policy status
SELECT 'FINAL_POLICIES_AFTER_FIX' as status, schemaname, tablename, policyname, cmd
FROM pg_policies 
WHERE tablename = 'profiles'
ORDER BY policyname;

-- 10. Test a simple profile query that should work now
SELECT 'PROFILE_QUERY_TEST' as test_type, 
       id, email, role, full_name, company_name, store_name
FROM profiles 
WHERE role IN ('admin', 'merchant', 'supplier')
LIMIT 3;
