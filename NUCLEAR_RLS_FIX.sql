-- =====================================================================
-- 🚨 NUCLEAR RLS FIX - LAST RESORT
-- =====================================================================
-- This completely disables RLS on profiles table and recreates simple policies
-- Use this only when all other approaches fail

-- STEP 1: COMPLETELY DISABLE RLS
-- =====================================================================
ALTER TABLE profiles DISABLE ROW LEVEL SECURITY;

-- STEP 2: VERIFY RLS IS DISABLED
-- =====================================================================
-- Test that we can access profiles without any restrictions
SELECT 'RLS disabled test:' as test_name, count(*) as profile_count 
FROM profiles 
WHERE role IN ('supplier', 'merchant', 'admin');

-- STEP 3: CLEAN SLATE - DROP ALL POLICIES
-- =====================================================================
-- Get all policy names and drop them
DO $$
DECLARE
    policy_record RECORD;
BEGIN
    FOR policy_record IN 
        SELECT policyname 
        FROM pg_policies 
        WHERE tablename = 'profiles' 
        AND schemaname = 'public'
    LOOP
        EXECUTE 'DROP POLICY IF EXISTS "' || policy_record.policyname || '" ON profiles';
        RAISE NOTICE 'Dropped policy: %', policy_record.policyname;
    END LOOP;
END $$;

-- STEP 4: RE-ENABLE RLS WITH MINIMAL POLICIES
-- =====================================================================
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;

-- Create only the most essential policies
-- Policy 1: Service role can do everything
CREATE POLICY "service_role_access" ON profiles
  FOR ALL
  USING (auth.role() = 'service_role')
  WITH CHECK (auth.role() = 'service_role');

-- Policy 2: Users can access their own profile (NO RECURSION)
CREATE POLICY "own_profile_access" ON profiles
  FOR ALL
  USING (auth.uid() = id)
  WITH CHECK (auth.uid() = id);

-- Policy 3: Anonymous consumer access (NO AUTH.USERS REFERENCE)
CREATE POLICY "consumer_access" ON profiles
  FOR ALL
  USING (role = 'consumer')
  WITH CHECK (role = 'consumer');

-- STEP 5: VERIFY THE FIX
-- =====================================================================
-- Show final policies
SELECT 
  policyname,
  cmd,
  CASE 
    WHEN qual IS NOT NULL THEN qual 
    ELSE 'No USING clause' 
  END as using_clause,
  CASE 
    WHEN with_check IS NOT NULL THEN with_check 
    ELSE 'No WITH CHECK clause' 
  END as with_check_clause
FROM pg_policies 
WHERE tablename = 'profiles' 
AND schemaname = 'public'
ORDER BY policyname;

-- Test profile access
SELECT 'Final test:' as test_name, count(*) as profile_count 
FROM profiles 
WHERE role IN ('supplier', 'merchant', 'admin')
LIMIT 5;
